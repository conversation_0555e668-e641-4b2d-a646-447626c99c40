{"name": "advanced-code-context-engine", "version": "1.0.0", "description": "AI-driven code context engine with Neo4j and Graphiti for intelligent code understanding", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "build": "webpack --mode production", "docker:build": "docker build -t context-engine .", "docker:run": "docker-compose up -d", "docker:stop": "docker-compose down"}, "keywords": ["code-analysis", "context-engine", "neo4j", "graphiti", "ast", "embeddings", "ai", "code-intelligence"], "author": "Context Engine Team", "license": "MIT", "dependencies": {"@pinecone-database/pinecone": "^0.1.6", "axios": "^1.6.2", "chokidar": "^3.5.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "ioredis": "^5.6.1", "joi": "^17.11.0", "lodash": "^4.17.21", "moment": "^2.29.4", "neo4j-driver": "^5.28.1", "rate-limiter-flexible": "^2.4.1", "redis": "^4.6.10", "simple-git": "^3.20.0", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@babel/core": "^7.23.6", "@babel/preset-env": "^7.23.6", "babel-jest": "^29.7.0", "eslint": "^8.55.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3", "webpack": "^5.89.0", "webpack-cli": "^5.1.4"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/advanced-code-context-engine.git"}, "bugs": {"url": "https://github.com/your-org/advanced-code-context-engine/issues"}, "homepage": "https://github.com/your-org/advanced-code-context-engine#readme"}