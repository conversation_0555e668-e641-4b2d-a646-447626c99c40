/**
 * Performance benchmarks for Context Engine
 */

import { jest } from '@jest/globals';
import fs from 'fs/promises';
import path from 'path';

// Mock dependencies for performance testing
jest.unstable_mockModule('../../src/utils/logger.js', () => ({
  createLogger: jest.fn(() => ({
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  })),
  logPerformance: jest.fn()
}));

describe('Performance Benchmarks', () => {
  let ASTProcessor;
  let SymbolTableBuilder;
  let IngestionPipeline;

  beforeAll(async () => {
    // Import modules
    const astModule = await import('../../src/core/processors/ASTProcessor.js');
    const symbolModule = await import('../../src/core/processors/SymbolTableBuilder.js');
    const pipelineModule = await import('../../src/ingestion/pipeline/IngestionPipeline.js');

    ASTProcessor = astModule.ASTProcessor;
    SymbolTableBuilder = symbolModule.SymbolTableBuilder;
    IngestionPipeline = pipelineModule.IngestionPipeline;
  });

  describe('AST Processing Performance', () => {
    let processor;

    beforeEach(async () => {
      processor = new ASTProcessor({
        supportedLanguages: ['javascript', 'python']
      });
      await processor.initialize();
    });

    test('should process small JavaScript file within performance threshold', async () => {
      const smallJsCode = `
        function simpleFunction(a, b) {
          return a + b;
        }
        
        class SimpleClass {
          constructor(value) {
            this.value = value;
          }
        }
      `;

      const file = {
        path: 'small.js',
        content: smallJsCode,
        language: 'javascript',
        size: smallJsCode.length
      };

      const startTime = Date.now();
      const result = await processor.process({ file });
      const duration = Date.now() - startTime;

      expect(duration).toBeLessThan(100); // Should process in under 100ms
      expect(result.ast.nodes.length).toBeGreaterThan(0);
    });

    test('should process medium JavaScript file within performance threshold', async () => {
      // Generate a medium-sized JavaScript file
      const functions = [];
      for (let i = 0; i < 50; i++) {
        functions.push(`
          function function${i}(param1, param2) {
            const result = param1 + param2;
            return result * ${i};
          }
        `);
      }

      const classes = [];
      for (let i = 0; i < 20; i++) {
        classes.push(`
          class Class${i} {
            constructor(value) {
              this.value = value;
              this.id = ${i};
            }
            
            getValue() {
              return this.value;
            }
            
            process() {
              return this.value * this.id;
            }
          }
        `);
      }

      const mediumJsCode = functions.join('\n') + classes.join('\n');
      const file = {
        path: 'medium.js',
        content: mediumJsCode,
        language: 'javascript',
        size: mediumJsCode.length
      };

      const startTime = Date.now();
      const result = await processor.process({ file });
      const duration = Date.now() - startTime;

      expect(duration).toBeLessThan(500); // Should process in under 500ms
      expect(result.ast.nodes.length).toBeGreaterThan(50); // Should find functions and classes
    });

    test('should process large JavaScript file within performance threshold', async () => {
      // Generate a large JavaScript file
      const functions = [];
      for (let i = 0; i < 200; i++) {
        functions.push(`
          function function${i}(param1, param2, param3) {
            const temp1 = param1 + param2;
            const temp2 = param2 * param3;
            const temp3 = param1 - param3;
            
            if (temp1 > temp2) {
              return temp1 + temp3;
            } else if (temp2 > temp3) {
              return temp2 - temp1;
            } else {
              return temp3 * temp1;
            }
          }
        `);
      }

      const classes = [];
      for (let i = 0; i < 50; i++) {
        classes.push(`
          class LargeClass${i} {
            constructor(a, b, c, d) {
              this.a = a;
              this.b = b;
              this.c = c;
              this.d = d;
              this.id = ${i};
            }
            
            method1() { return this.a + this.b; }
            method2() { return this.c * this.d; }
            method3() { return this.a - this.c; }
            method4() { return this.b / this.d; }
            
            complexMethod() {
              const result1 = this.method1();
              const result2 = this.method2();
              const result3 = this.method3();
              const result4 = this.method4();
              
              return (result1 + result2) * (result3 - result4);
            }
          }
        `);
      }

      const largeJsCode = functions.join('\n') + classes.join('\n');
      const file = {
        path: 'large.js',
        content: largeJsCode,
        language: 'javascript',
        size: largeJsCode.length
      };

      const startTime = Date.now();
      const result = await processor.process({ file });
      const duration = Date.now() - startTime;

      expect(duration).toBeLessThan(2000); // Should process in under 2 seconds
      expect(result.ast.nodes.length).toBeGreaterThan(200); // Should find many functions and classes
    });
  });

  describe('Symbol Table Building Performance', () => {
    let builder;
    let processor;

    beforeEach(async () => {
      processor = new ASTProcessor({
        supportedLanguages: ['javascript', 'python']
      });
      await processor.initialize();

      builder = new SymbolTableBuilder({
        supportedLanguages: ['javascript', 'python']
      });
      await builder.initialize();
    });

    test('should build symbol table for complex AST within performance threshold', async () => {
      // Create a complex JavaScript file
      const complexCode = `
        import React from 'react';
        import { useState, useEffect } from 'react';
        import * as utils from './utils';
        
        class UserManager {
          constructor(database) {
            this.database = database;
            this.users = new Map();
            this.cache = new WeakMap();
          }
          
          async createUser(userData) {
            const user = {
              id: this.generateId(),
              ...userData,
              createdAt: new Date()
            };
            
            this.users.set(user.id, user);
            await this.database.save('users', user);
            return user;
          }
          
          async findUser(id) {
            if (this.users.has(id)) {
              return this.users.get(id);
            }
            
            const user = await this.database.find('users', id);
            if (user) {
              this.users.set(id, user);
            }
            return user;
          }
          
          generateId() {
            return Math.random().toString(36).substr(2, 9);
          }
        }
        
        function createUserManager(database) {
          return new UserManager(database);
        }
        
        const defaultConfig = {
          timeout: 5000,
          retries: 3,
          cache: true
        };
        
        export { UserManager, createUserManager, defaultConfig };
        export default UserManager;
      `;

      const file = {
        path: 'complex.js',
        content: complexCode,
        language: 'javascript',
        size: complexCode.length
      };

      // First process AST
      const astStartTime = Date.now();
      const astResult = await processor.process({ file });
      const astDuration = Date.now() - astStartTime;

      // Then build symbol table
      const symbolStartTime = Date.now();
      const symbolResult = await builder.process(astResult);
      const symbolDuration = Date.now() - symbolStartTime;

      expect(astDuration).toBeLessThan(200);
      expect(symbolDuration).toBeLessThan(300);
      expect(symbolResult.symbols.symbols.length).toBeGreaterThan(5);
      expect(symbolResult.symbols.imports.length).toBeGreaterThan(0);
      expect(symbolResult.symbols.exports.length).toBeGreaterThan(0);
    });
  });

  describe('Batch Processing Performance', () => {
    let pipeline;

    beforeEach(async () => {
      const mockNeo4jClient = {
        session: jest.fn(() => ({
          run: jest.fn(),
          close: jest.fn(),
          beginTransaction: jest.fn(),
          commitTransaction: jest.fn(),
          rollbackTransaction: jest.fn()
        })),
        run: jest.fn(),
        healthCheck: jest.fn(() => ({ status: 'healthy' })),
        getStatistics: jest.fn(() => ({ nodeCount: 0, relationshipCount: 0 }))
      };

      pipeline = new IngestionPipeline({
        neo4jClient: mockNeo4jClient,
        maxConcurrent: 5,
        batchSize: 10,
        supportedLanguages: ['javascript', 'python']
      });

      await pipeline.initialize();

      // Mock graph manager
      pipeline.graphManager.storeContext = jest.fn().mockResolvedValue({
        nodesCreated: 2,
        relationshipsCreated: 1
      });
    });

    test('should process batch of files within performance threshold', async () => {
      // Create a batch of files
      const files = [];
      for (let i = 0; i < 20; i++) {
        files.push({
          path: `file${i}.js`,
          content: `
            function process${i}(data) {
              return data.map(item => item * ${i});
            }
            
            class Processor${i} {
              constructor() {
                this.id = ${i};
              }
              
              process(input) {
                return input + this.id;
              }
            }
          `,
          language: 'javascript',
          size: 200,
          lastModified: new Date(),
          version: '1.0.0'
        });
      }

      const startTime = Date.now();
      
      // Process files sequentially (simulating batch processing)
      const results = [];
      for (const file of files) {
        const result = await pipeline.processFile(file);
        results.push(result);
      }
      
      const duration = Date.now() - startTime;
      const avgTimePerFile = duration / files.length;

      expect(duration).toBeLessThan(5000); // Total time under 5 seconds
      expect(avgTimePerFile).toBeLessThan(250); // Average under 250ms per file
      expect(results).toHaveLength(20);
      expect(pipeline.statistics.filesProcessed).toBe(20);
    });

    test('should handle concurrent processing efficiently', async () => {
      // Create files for concurrent processing
      const files = [];
      for (let i = 0; i < 10; i++) {
        files.push({
          path: `concurrent${i}.js`,
          content: `
            async function asyncProcess${i}(data) {
              await new Promise(resolve => setTimeout(resolve, 10));
              return data.filter(item => item > ${i});
            }
          `,
          language: 'javascript',
          size: 150,
          lastModified: new Date(),
          version: '1.0.0'
        });
      }

      const startTime = Date.now();
      
      // Process files concurrently
      const promises = files.map(file => pipeline.processFile(file));
      const results = await Promise.all(promises);
      
      const duration = Date.now() - startTime;

      expect(duration).toBeLessThan(2000); // Should be faster than sequential
      expect(results).toHaveLength(10);
      expect(pipeline.statistics.filesProcessed).toBe(10);
    });
  });

  describe('Memory Usage Benchmarks', () => {
    test('should not leak memory during large file processing', async () => {
      const processor = new ASTProcessor({
        supportedLanguages: ['javascript']
      });
      await processor.initialize();

      const initialMemory = process.memoryUsage();

      // Process many files to test for memory leaks
      for (let i = 0; i < 100; i++) {
        const code = `
          function test${i}() {
            const data = new Array(1000).fill(${i});
            return data.map(x => x * 2);
          }
        `;

        const file = {
          path: `memory-test-${i}.js`,
          content: code,
          language: 'javascript',
          size: code.length
        };

        await processor.process({ file });
      }

      const finalMemory = process.memoryUsage();
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
      const memoryIncreasePerFile = memoryIncrease / 100;

      // Memory increase should be reasonable (less than 1MB per file)
      expect(memoryIncreasePerFile).toBeLessThan(1024 * 1024);
    });
  });

  describe('Query Performance Simulation', () => {
    test('should simulate fast query response times', async () => {
      // Simulate different types of queries and their expected performance
      const queryTypes = [
        { type: 'simple', expectedTime: 50 },
        { type: 'complex', expectedTime: 200 },
        { type: 'graph_traversal', expectedTime: 300 },
        { type: 'hybrid_search', expectedTime: 500 }
      ];

      for (const queryType of queryTypes) {
        const startTime = Date.now();
        
        // Simulate query processing time
        await new Promise(resolve => setTimeout(resolve, Math.random() * queryType.expectedTime));
        
        const duration = Date.now() - startTime;
        
        // Allow some variance but ensure it's within reasonable bounds
        expect(duration).toBeLessThan(queryType.expectedTime * 1.5);
      }
    });
  });

  describe('Throughput Benchmarks', () => {
    test('should achieve target throughput for file processing', async () => {
      const processor = new ASTProcessor({
        supportedLanguages: ['javascript']
      });
      await processor.initialize();

      const fileCount = 50;
      const files = [];

      // Generate test files
      for (let i = 0; i < fileCount; i++) {
        files.push({
          path: `throughput-test-${i}.js`,
          content: `function test${i}() { return ${i}; }`,
          language: 'javascript',
          size: 30
        });
      }

      const startTime = Date.now();
      
      // Process all files
      const results = await Promise.all(
        files.map(file => processor.process({ file }))
      );
      
      const duration = Date.now() - startTime;
      const throughput = fileCount / (duration / 1000); // files per second

      expect(results).toHaveLength(fileCount);
      expect(throughput).toBeGreaterThan(10); // Should process at least 10 files per second
    });
  });
});
