export interface ContextEngineConfig {
  ragEnabled: boolean
  memoryDepth: number
  validationLevel: "basic" | "strict" | "paranoid"
  templateVersion: string
}

export interface EnhancedProjectContext {
  // Core context
  originalPrompt: string
  projectType: string
  features: string[]

  // Context engineering metadata
  complexity: "Simple" | "Medium" | "Complex"
  confidence: number
  completeness: number

  // RAG-enhanced data
  externalKnowledge: Record<string, any>
  bestPractices: string[]
  templates: Record<string, any>

  // Memory and dependencies
  stepHistory: string[]
  dependencies: Record<string, any>

  // Validation and security
  validation: {
    isValid: boolean
    issues: string[]
    securityScore: number
  }
}

export interface PlanningStep {
  id: string
  name: string
  dependencies: string[]
  templates: any[]
  ragQueries: string[]
  validationRules: string[]
}

// Deep Context Propagation Types
export interface ContextNode {
  id: string
  dependencies: string[]
  outputs: string[]
  contextRequirements: string[]
  validationRules: string[]
  crossReferences: string[]
  timestamp: string
  version: string
}

export interface ContextualHint {
  type: 'alignment' | 'dependency' | 'constraint' | 'best_practice'
  source: string
  target: string
  message: string
  severity: 'info' | 'warning' | 'error'
  autoFix?: string
}

export interface CrossReference {
  fromStep: string
  toStep: string
  relationship: 'depends_on' | 'influences' | 'validates' | 'conflicts_with'
  field: string
  description: string
}

export interface ContextIntegrityReport {
  isValid: boolean
  issues: ContextIntegrityIssue[]
  score: number
  recommendations: string[]
}

export interface ContextIntegrityIssue {
  type: 'missing_dependency' | 'circular_reference' | 'data_mismatch' | 'validation_failure'
  step: string
  field?: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  message: string
  suggestedFix?: string
}
