/**
 * PartyKit Real-time Planning Updates
 * Live collaboration and progress updates
 */

import type * as Party from "partykit/server"

export interface PlanningRoomState {
  sessionId: string
  status: "idle" | "running" | "completed" | "failed"
  currentStep?: string
  progress: number
  results: Record<string, any>
  participants: Set<string>
  lastUpdate: string
}

export interface PlanningMessage {
  type: "step_started" | "step_completed" | "step_failed" | "progress_update" | "user_joined" | "user_left"
  data: any
  timestamp: string
  userId?: string
}

export default class PlanningParty implements Party.Server {
  private state: PlanningRoomState

  constructor(readonly room: Party.Room) {
    this.state = {
      sessionId: room.id,
      status: "idle",
      progress: 0,
      results: {},
      participants: new Set(),
      lastUpdate: new Date().toISOString()
    }
  }

  async onConnect(conn: Party.Connection, ctx: Party.ConnectionContext) {
    const userId = new URL(ctx.request.url).searchParams.get("userId") || `user_${Date.now()}`
    
    // Add user to participants
    this.state.participants.add(userId)
    
    // Send current state to new connection
    conn.send(JSON.stringify({
      type: "state_sync",
      data: {
        ...this.state,
        participants: Array.from(this.state.participants)
      },
      timestamp: new Date().toISOString()
    }))

    // Notify others about new participant
    this.broadcast({
      type: "user_joined",
      data: { userId, participantCount: this.state.participants.size },
      timestamp: new Date().toISOString(),
      userId
    }, [conn.id])

    console.log(`User ${userId} joined planning session ${this.room.id}`)
  }

  async onClose(conn: Party.Connection) {
    // Remove user from participants (we'd need to track conn -> userId mapping)
    // For now, we'll clean up on periodic basis
    console.log(`Connection closed in planning session ${this.room.id}`)
  }

  async onMessage(message: string, sender: Party.Connection) {
    try {
      const msg: PlanningMessage = JSON.parse(message)
      
      switch (msg.type) {
        case "step_started":
          this.state.status = "running"
          this.state.currentStep = msg.data.step
          this.state.lastUpdate = msg.timestamp
          break
          
        case "step_completed":
          this.state.results[msg.data.step] = msg.data.results
          this.state.progress = msg.data.progress || this.calculateProgress()
          this.state.lastUpdate = msg.timestamp
          break
          
        case "step_failed":
          this.state.status = "failed"
          this.state.lastUpdate = msg.timestamp
          break
          
        case "progress_update":
          this.state.progress = msg.data.progress
          this.state.status = msg.data.status
          this.state.lastUpdate = msg.timestamp
          break
      }

      // Broadcast update to all connections
      this.broadcast(msg)
      
    } catch (error) {
      console.error("Error processing message:", error)
    }
  }

  private broadcast(message: PlanningMessage, exclude: string[] = []) {
    const messageStr = JSON.stringify(message)
    
    this.room.connections.forEach((conn) => {
      if (!exclude.includes(conn.id)) {
        conn.send(messageStr)
      }
    })
  }

  private calculateProgress(): number {
    const totalSteps = 13 // Total planning steps
    const completedSteps = Object.keys(this.state.results).length
    return Math.round((completedSteps / totalSteps) * 100)
  }

  // Webhook endpoint for Inngest events
  async onRequest(req: Party.Request): Promise<Response> {
    if (req.method === "POST") {
      try {
        const event = await req.json()
        
        // Handle Inngest webhook events
        switch (event.name) {
          case "planning/step.completed":
            this.broadcast({
              type: "step_completed",
              data: {
                step: event.data.step,
                results: event.data.results,
                progress: this.calculateProgress()
              },
              timestamp: event.data.timestamp
            })
            break
            
          case "planning/step.failed":
            this.broadcast({
              type: "step_failed",
              data: {
                step: event.data.step,
                error: event.data.error
              },
              timestamp: event.data.timestamp
            })
            break
            
          case "planning/session.completed":
            this.state.status = "completed"
            this.state.progress = 100
            this.broadcast({
              type: "progress_update",
              data: {
                status: "completed",
                progress: 100,
                results: event.data.results
              },
              timestamp: event.data.timestamp
            })
            break
        }
        
        return new Response("OK")
      } catch (error) {
        return new Response("Error", { status: 400 })
      }
    }
    
    return new Response("Method not allowed", { status: 405 })
  }
}

PlanningParty satisfies Party.Worker
