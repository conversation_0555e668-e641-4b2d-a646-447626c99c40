/**
 * Context Engine Migration Service
 * Handles migration from old context engine to unified context engine
 */

import { ContextEngine as OldContextEngine } from './context-engine'
import { UnifiedContextEngine } from './unified-context-engine'
import { getConfig } from './unified-context-config'
import type { ProjectContext } from "@/types/planning"

export interface MigrationResult {
  success: boolean
  migratedAgents: number
  migratedContext: boolean
  errors: string[]
  warnings: string[]
}

export class ContextEngineMigration {
  private oldEngine: OldContextEngine | null = null
  private newEngine: UnifiedContextEngine | null = null
  private migrationLog: string[] = []

  /**
   * Initialize migration with existing context engine
   */
  async initializeMigration(oldEngine: OldContextEngine): Promise<void> {
    this.oldEngine = oldEngine
    this.log('Migration initialized with existing context engine')
  }

  /**
   * Create new unified context engine
   */
  async createUnifiedEngine(initialContext?: Partial<ProjectContext>): Promise<UnifiedContextEngine> {
    const config = getConfig()
    
    // Extract context from old engine if available
    const contextToMigrate = initialContext || this.extractContextFromOldEngine()
    
    this.newEngine = new UnifiedContextEngine(contextToMigrate, config)
    
    // Wait for initialization
    await new Promise((resolve, reject) => {
      this.newEngine!.once('initialized', resolve)
      this.newEngine!.once('error', reject)
      
      // Timeout after 30 seconds
      setTimeout(() => reject(new Error('Initialization timeout')), 30000)
    })
    
    this.log('Unified context engine created and initialized')
    return this.newEngine
  }

  /**
   * Perform complete migration
   */
  async migrate(): Promise<MigrationResult> {
    const result: MigrationResult = {
      success: false,
      migratedAgents: 0,
      migratedContext: false,
      errors: [],
      warnings: []
    }

    try {
      if (!this.oldEngine || !this.newEngine) {
        throw new Error('Migration not properly initialized')
      }

      this.log('Starting migration process')

      // Step 1: Migrate context data
      result.migratedContext = await this.migrateContextData()
      
      // Step 2: Migrate agent registrations
      result.migratedAgents = await this.migrateAgentRegistrations()
      
      // Step 3: Migrate memory store
      await this.migrateMemoryStore()
      
      // Step 4: Validate migration
      const validation = await this.validateMigration()
      
      if (validation.isValid) {
        result.success = true
        this.log('Migration completed successfully')
      } else {
        result.errors.push(...validation.errors)
        this.log('Migration completed with errors')
      }

    } catch (error) {
      result.errors.push(error.message)
      this.log(`Migration failed: ${error.message}`)
    }

    return result
  }

  /**
   * Extract context from old engine
   */
  private extractContextFromOldEngine(): Partial<ProjectContext> {
    if (!this.oldEngine) {
      return {}
    }

    try {
      // Access private context through reflection (for migration only)
      const oldContext = (this.oldEngine as any).context
      
      if (oldContext) {
        this.log('Extracted context from old engine')
        return {
          originalPrompt: oldContext.originalPrompt || '',
          projectType: oldContext.projectType || '',
          features: oldContext.features || [],
          clarifications: oldContext.clarifications || {},
          summary: oldContext.summary || '',
          techStack: oldContext.techStack || {},
          prd: oldContext.prd || {},
          wireframes: oldContext.wireframes || [],
          filesystem: oldContext.filesystem || {},
          workflow: oldContext.workflow || {},
          tasks: oldContext.tasks || []
        }
      }
    } catch (error) {
      this.log(`Failed to extract context: ${error.message}`)
    }

    return {}
  }

  /**
   * Migrate context data
   */
  private async migrateContextData(): Promise<boolean> {
    try {
      if (!this.oldEngine || !this.newEngine) {
        return false
      }

      // Get relevant context from old engine
      const relevantContext = this.oldEngine.getRelevantContext('all')
      
      if (relevantContext && relevantContext.current) {
        // Update new engine with migrated context
        Object.keys(relevantContext.current).forEach(key => {
          if (relevantContext.current[key] && key !== 'originalPrompt') {
            this.newEngine!.updateContext(key, relevantContext.current[key])
          }
        })
        
        this.log('Context data migrated successfully')
        return true
      }

      return false
    } catch (error) {
      this.log(`Context migration failed: ${error.message}`)
      return false
    }
  }

  /**
   * Migrate agent registrations
   */
  private async migrateAgentRegistrations(): Promise<number> {
    try {
      if (!this.oldEngine || !this.newEngine) {
        return 0
      }

      let migratedCount = 0

      // Access agent contexts from old engine
      const agentContexts = (this.oldEngine as any).agentContexts
      
      if (agentContexts && agentContexts instanceof Map) {
        for (const [agentId, agentScope] of agentContexts) {
          try {
            // Register agent in new engine
            const newAgentId = this.newEngine.registerAgent(
              agentScope.agentType,
              agentScope.operationId,
              {
                parentContext: agentScope.parentContext,
                requiredCapabilities: agentScope.requiredCapabilities,
                contextFilters: agentScope.contextFilters
              }
            )
            
            this.log(`Migrated agent: ${agentId} -> ${newAgentId}`)
            migratedCount++
          } catch (error) {
            this.log(`Failed to migrate agent ${agentId}: ${error.message}`)
          }
        }
      }

      this.log(`Migrated ${migratedCount} agents`)
      return migratedCount
    } catch (error) {
      this.log(`Agent migration failed: ${error.message}`)
      return 0
    }
  }

  /**
   * Migrate memory store
   */
  private async migrateMemoryStore(): Promise<void> {
    try {
      if (!this.oldEngine || !this.newEngine) {
        return
      }

      // Access memory store from old engine
      const memoryStore = (this.oldEngine as any).memoryStore
      
      if (memoryStore && memoryStore instanceof Map) {
        for (const [key, value] of memoryStore) {
          // Extract step name from key (format: "step_<stepname>")
          const stepMatch = key.match(/^step_(.+)$/)
          if (stepMatch) {
            const stepName = stepMatch[1]
            this.newEngine.updateContext(stepName, value.data || value)
          }
        }
        
        this.log('Memory store migrated successfully')
      }
    } catch (error) {
      this.log(`Memory store migration failed: ${error.message}`)
    }
  }

  /**
   * Validate migration
   */
  private async validateMigration(): Promise<{ isValid: boolean; errors: string[] }> {
    const errors: string[] = []

    try {
      if (!this.newEngine) {
        errors.push('New engine not available for validation')
        return { isValid: false, errors }
      }

      // Validate context integrity
      const contextValidation = this.newEngine.validateContext()
      if (!contextValidation.isValid) {
        errors.push(...contextValidation.issues.map(issue => issue.message))
      }

      // Test basic functionality
      try {
        const testAgentId = this.newEngine.registerAgent('planner', 'migration-test')
        const testContext = await this.newEngine.getAgentContext(testAgentId)
        
        if (!testContext) {
          errors.push('Failed to get agent context after migration')
        }
      } catch (error) {
        errors.push(`Agent functionality test failed: ${error.message}`)
      }

      this.log(`Migration validation completed with ${errors.length} errors`)
      return { isValid: errors.length === 0, errors }
    } catch (error) {
      errors.push(`Validation failed: ${error.message}`)
      return { isValid: false, errors }
    }
  }

  /**
   * Get migration log
   */
  getMigrationLog(): string[] {
    return [...this.migrationLog]
  }

  /**
   * Clear migration log
   */
  clearMigrationLog(): void {
    this.migrationLog = []
  }

  /**
   * Log migration events
   */
  private log(message: string): void {
    const timestamp = new Date().toISOString()
    const logEntry = `[${timestamp}] ${message}`
    this.migrationLog.push(logEntry)
    console.log(`[MIGRATION] ${logEntry}`)
  }
}

/**
 * Convenience function to perform migration
 */
export async function migrateToUnifiedEngine(
  oldEngine: OldContextEngine,
  initialContext?: Partial<ProjectContext>
): Promise<{ newEngine: UnifiedContextEngine; result: MigrationResult }> {
  const migration = new ContextEngineMigration()
  
  await migration.initializeMigration(oldEngine)
  const newEngine = await migration.createUnifiedEngine(initialContext)
  const result = await migration.migrate()
  
  return { newEngine, result }
}

/**
 * Create unified engine without migration
 */
export async function createUnifiedEngine(
  initialContext: Partial<ProjectContext> = {}
): Promise<UnifiedContextEngine> {
  const config = getConfig()
  const engine = new UnifiedContextEngine(initialContext, config)
  
  // Wait for initialization
  await new Promise((resolve, reject) => {
    engine.once('initialized', resolve)
    engine.once('error', reject)
    
    // Timeout after 30 seconds
    setTimeout(() => reject(new Error('Initialization timeout')), 30000)
  })
  
  return engine
}
