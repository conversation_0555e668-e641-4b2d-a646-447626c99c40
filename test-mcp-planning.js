/**
 * Quick test to verify MCP integration is working
 */

const { PlanningGraph } = require('./lib/planning-graph')

async function testMCPPlanning() {
  console.log('🧪 Testing MCP-Enhanced Planning Agent...\n')

  const planningGraph = new PlanningGraph()

  try {
    const result = await planningGraph.execute({
      prompt: "Build a simple React todo app with authentication",
      isInteractive: false,
      userAnswers: {}
    })

    console.log('\n✅ Planning completed!')
    console.log('📊 Results:')
    
    // Check if MCP enhancements were applied
    const analyze = result.results?.analyze
    if (analyze?._mcpEnhanced) {
      console.log('🧠 Analysis: MCP-enhanced ✅')
      console.log(`   - Enrichments: ${analyze._enrichmentCount || 0}`)
      console.log(`   - Sequential thinking: ${analyze._hasSequentialThinking ? '✅' : '❌'}`)
    } else {
      console.log('❌ Analysis: Not MCP-enhanced')
    }

    const techstack = result.results?.techstack
    if (techstack?._mcpEnhanced) {
      console.log('🔧 Tech Stack: MCP-enhanced ✅')
      console.log(`   - Enrichments: ${techstack._enrichmentCount || 0}`)
      console.log(`   - Sequential thinking: ${techstack._hasSequentialThinking ? '✅' : '❌'}`)
      console.log(`   - Compatibility data: ${techstack._hasCompatibilityData ? '✅' : '❌'}`)
    } else {
      console.log('❌ Tech Stack: Not MCP-enhanced')
    }

    // Check context integrity
    if (result.contextIntegrity) {
      console.log('🔍 Context Integrity: ✅')
    }

    // Check cross-reference validation
    if (result.crossReferenceValidation) {
      console.log('✅ Cross-Reference Validation: ✅')
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
}

testMCPPlanning().catch(console.error)
