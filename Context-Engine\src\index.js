import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { config } from './utils/config.js';
import { createLogger } from './utils/logger.js';
import { ContextEngine } from './core/engine/ContextEngine.js';
import contextRoutes from './api/routes/contextRoutes.js';
import healthRoutes from './api/routes/healthRoutes.js';

const logger = createLogger('Application');

/**
 * Main application class
 */
class Application {
  constructor() {
    this.app = express();
    this.contextEngine = null;
    this.server = null;
  }

  /**
   * Initialize the application
   */
  async initialize() {
    try {
      logger.info('Starting Context Engine application', {
        environment: config.env,
        port: config.port,
        logLevel: config.logLevel
      });

      // Initialize Context Engine
      await this.initializeContextEngine();

      // Setup Express middleware
      this.setupMiddleware();

      // Setup routes
      this.setupRoutes();

      // Setup error handling
      this.setupErrorHandling();

      // Start server
      await this.start();

      logger.info('Context Engine application started successfully');

    } catch (error) {
      logger.error('Failed to initialize application', { error: error.message });
      throw error;
    }
  }

  /**
   * Initialize the Context Engine
   */
  async initializeContextEngine() {
    logger.info('Initializing Context Engine');
    
    this.contextEngine = new ContextEngine(config);
    await this.contextEngine.initialize();
    
    // Make context engine available to routes
    this.app.set('contextEngine', this.contextEngine);
    
    logger.info('Context Engine initialized successfully');
  }

  /**
   * Setup Express middleware
   */
  setupMiddleware() {
    logger.info('Setting up middleware');

    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"]
        }
      }
    }));

    // CORS middleware
    if (config.api.cors.enabled) {
      this.app.use(cors({
        origin: config.api.cors.origin,
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key']
      }));
    }

    // Body parsing middleware
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Request logging middleware
    this.app.use((req, res, next) => {
      const startTime = Date.now();
      
      res.on('finish', () => {
        const duration = Date.now() - startTime;
        logger.info('Request completed', {
          method: req.method,
          url: req.url,
          statusCode: res.statusCode,
          duration,
          userAgent: req.get('User-Agent'),
          ip: req.ip
        });
      });
      
      next();
    });

    // Trust proxy for accurate IP addresses
    this.app.set('trust proxy', true);

    logger.info('Middleware setup completed');
  }

  /**
   * Setup application routes
   */
  setupRoutes() {
    logger.info('Setting up routes');

    // Health check routes (no authentication required)
    this.app.use('/api/health', healthRoutes);

    // API routes
    this.app.use('/api/context', contextRoutes);

    // Root endpoint
    this.app.get('/', (req, res) => {
      res.json({
        name: 'Advanced Code Context Engine',
        version: process.env.VERSION || '1.0.0',
        status: 'running',
        timestamp: new Date().toISOString(),
        endpoints: {
          health: '/api/health',
          context: '/api/context',
          documentation: '/api/docs'
        }
      });
    });

    // API documentation endpoint
    this.app.get('/api/docs', (req, res) => {
      res.json({
        name: 'Advanced Code Context Engine API',
        version: process.env.VERSION || '1.0.0',
        description: 'AI-driven code context engine with Neo4j and hybrid retrieval',
        endpoints: {
          'POST /api/context/query': 'Query for code context',
          'POST /api/context/repositories/process': 'Process a repository',
          'GET /api/context/statistics': 'Get engine statistics',
          'GET /api/context/files/search': 'Search for files',
          'GET /api/context/elements/:id': 'Get code element details',
          'GET /api/context/elements/:id/relationships': 'Get element relationships',
          'GET /api/health': 'Basic health check',
          'GET /api/health/detailed': 'Detailed health information',
          'GET /api/health/ready': 'Readiness probe',
          'GET /api/health/live': 'Liveness probe'
        },
        timestamp: new Date().toISOString()
      });
    });

    // 404 handler
    this.app.use('*', (req, res) => {
      res.status(404).json({
        success: false,
        error: {
          message: 'Endpoint not found',
          path: req.originalUrl,
          method: req.method,
          type: 'NOT_FOUND'
        },
        timestamp: new Date().toISOString()
      });
    });

    logger.info('Routes setup completed');
  }

  /**
   * Setup error handling middleware
   */
  setupErrorHandling() {
    logger.info('Setting up error handling');

    // Global error handler
    this.app.use((error, req, res, next) => {
      logger.error('Unhandled error', {
        error: error.message,
        stack: error.stack,
        url: req.url,
        method: req.method,
        ip: req.ip
      });

      // Don't expose internal errors in production
      const isDevelopment = config.env === 'development';
      
      res.status(error.status || 500).json({
        success: false,
        error: {
          message: isDevelopment ? error.message : 'Internal server error',
          type: 'INTERNAL_ERROR',
          ...(isDevelopment && { stack: error.stack })
        },
        timestamp: new Date().toISOString()
      });
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught exception', {
        error: error.message,
        stack: error.stack
      });
      
      // Graceful shutdown
      this.shutdown().then(() => {
        process.exit(1);
      });
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled promise rejection', {
        reason: reason?.message || reason,
        stack: reason?.stack
      });
      
      // Graceful shutdown
      this.shutdown().then(() => {
        process.exit(1);
      });
    });

    logger.info('Error handling setup completed');
  }

  /**
   * Start the HTTP server
   */
  async start() {
    return new Promise((resolve, reject) => {
      this.server = this.app.listen(config.port, (error) => {
        if (error) {
          logger.error('Failed to start server', { error: error.message });
          reject(error);
        } else {
          logger.info('Server started successfully', {
            port: config.port,
            environment: config.env
          });
          resolve();
        }
      });

      // Handle server errors
      this.server.on('error', (error) => {
        logger.error('Server error', { error: error.message });
        reject(error);
      });
    });
  }

  /**
   * Graceful shutdown
   */
  async shutdown() {
    logger.info('Starting graceful shutdown');

    try {
      // Stop accepting new connections
      if (this.server) {
        await new Promise((resolve) => {
          this.server.close(resolve);
        });
        logger.info('HTTP server closed');
      }

      // Shutdown Context Engine
      if (this.contextEngine) {
        await this.contextEngine.shutdown();
        logger.info('Context Engine shutdown completed');
      }

      logger.info('Graceful shutdown completed');
    } catch (error) {
      logger.error('Error during shutdown', { error: error.message });
      throw error;
    }
  }
}

/**
 * Main entry point
 */
async function main() {
  const app = new Application();
  
  try {
    await app.initialize();
    
    // Setup graceful shutdown handlers
    const shutdownHandler = async (signal) => {
      logger.info(`Received ${signal}, starting graceful shutdown`);
      try {
        await app.shutdown();
        process.exit(0);
      } catch (error) {
        logger.error('Error during shutdown', { error: error.message });
        process.exit(1);
      }
    };

    process.on('SIGTERM', () => shutdownHandler('SIGTERM'));
    process.on('SIGINT', () => shutdownHandler('SIGINT'));
    
  } catch (error) {
    logger.error('Failed to start application', { error: error.message });
    process.exit(1);
  }
}

// Start the application if this file is run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch((error) => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

export { Application };
export default Application;
