"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ArrowRight, GitBranch, Zap } from "lucide-react"
import type { ModuleProps } from "@/types/planning"

interface WorkflowStep {
  id: string
  title: string
  description: string
  type: "process" | "decision" | "action"
  dependencies?: string[]
}

export function WorkflowDefiner({ context, onComplete }: ModuleProps) {
  const [workflow, setWorkflow] = useState<WorkflowStep[]>([])
  const [isGenerating, setIsGenerating] = useState(true)

  useEffect(() => {
    generateWorkflow()
  }, [])

  const generateWorkflow = async () => {
    setIsGenerating(true)

    // Simulate AI generation
    await new Promise((resolve) => setTimeout(resolve, 2000))

    const generatedWorkflow = createWorkflow()
    setWorkflow(generatedWorkflow)
    setIsGenerating(false)
  }

  const createWorkflow = (): WorkflowStep[] => {
    const { projectType, features, clarifications } = context || {}
    const needsAuth = clarifications?.authentication?.includes("Yes")

    const steps: WorkflowStep[] = []

    if (projectType === "AI Agent") {
      steps.push(
        {
          id: "input_processing",
          title: "Input Processing",
          description: "Receive and validate user input or trigger events",
          type: "process",
        },
        {
          id: "context_analysis",
          title: "Context Analysis",
          description: "Analyze input context and determine appropriate response strategy",
          type: "decision",
          dependencies: ["input_processing"],
        },
        {
          id: "action_execution",
          title: "Action Execution",
          description: "Execute the determined action or generate response",
          type: "action",
          dependencies: ["context_analysis"],
        },
        {
          id: "response_delivery",
          title: "Response Delivery",
          description: "Format and deliver response to user or system",
          type: "process",
          dependencies: ["action_execution"],
        },
      )
    } else if (projectType === "Workflow System") {
      steps.push(
        {
          id: "workflow_trigger",
          title: "Workflow Trigger",
          description: "Detect trigger event or manual initiation",
          type: "process",
        },
        {
          id: "data_collection",
          title: "Data Collection",
          description: "Gather required data from various sources",
          type: "action",
          dependencies: ["workflow_trigger"],
        },
        {
          id: "processing_logic",
          title: "Processing Logic",
          description: "Apply business rules and transformation logic",
          type: "process",
          dependencies: ["data_collection"],
        },
        {
          id: "approval_check",
          title: "Approval Check",
          description: "Check if manual approval is required",
          type: "decision",
          dependencies: ["processing_logic"],
        },
        {
          id: "execution",
          title: "Execution",
          description: "Execute the workflow actions",
          type: "action",
          dependencies: ["approval_check"],
        },
        {
          id: "notification",
          title: "Notification",
          description: "Send notifications to relevant stakeholders",
          type: "process",
          dependencies: ["execution"],
        },
      )
    } else {
      // Standard web application workflow
      steps.push(
        {
          id: "user_access",
          title: "User Access",
          description: needsAuth ? "User authentication and authorization" : "Direct application access",
          type: "process",
        },
        {
          id: "data_loading",
          title: "Data Loading",
          description: "Load initial application data and user preferences",
          type: "action",
          dependencies: ["user_access"],
        },
        {
          id: "user_interaction",
          title: "User Interaction",
          description: "Handle user inputs and interface interactions",
          type: "process",
          dependencies: ["data_loading"],
        },
        {
          id: "data_processing",
          title: "Data Processing",
          description: "Process user requests and update application state",
          type: "action",
          dependencies: ["user_interaction"],
        },
        {
          id: "data_persistence",
          title: "Data Persistence",
          description: "Save changes to database or storage system",
          type: "process",
          dependencies: ["data_processing"],
        },
        {
          id: "response_update",
          title: "Response Update",
          description: "Update UI with processed results and feedback",
          type: "action",
          dependencies: ["data_persistence"],
        },
      )
    }

    return steps
  }

  const getStepIcon = (type: string) => {
    switch (type) {
      case "decision":
        return <GitBranch className="w-4 h-4" />
      case "action":
        return <Zap className="w-4 h-4" />
      default:
        return <ArrowRight className="w-4 h-4" />
    }
  }

  const getStepColor = (type: string) => {
    switch (type) {
      case "decision":
        return "bg-yellow-600"
      case "action":
        return "bg-green-600"
      default:
        return "bg-blue-600"
    }
  }

  const handleContinue = () => {
    onComplete({ steps: workflow })
  }

  return (
    <Card className="border-slate-700" style={{backgroundColor: '#818181'}}>
      <CardHeader>
        <div className="flex items-center gap-2">
          <ArrowRight className="w-5 h-5 text-purple-400" />
          <CardTitle className="text-white">Workflow Logic Definition</CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        {isGenerating ? (
          <div className="flex items-center justify-center py-12">
            <div className="flex items-center gap-3">
              <ArrowRight className="w-5 h-5 text-purple-400 animate-pulse" />
              <span className="text-gray-300">Defining workflow logic and process flow...</span>
            </div>
          </div>
        ) : (
          <>
            <div className="space-y-4">
              {workflow.map((step, index) => (
                <div key={step.id} className="relative">
                  {index > 0 && <div className="absolute -top-4 left-6 w-0.5 h-4 bg-gray-600"></div>}
                  <div className="flex items-start gap-4 p-4 bg-slate-700/50 rounded-lg">
                    <div className={`p-2 rounded-full ${getStepColor(step.type)} text-white`}>
                      {getStepIcon(step.type)}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h4 className="text-white font-semibold">{step.title}</h4>
                        <Badge
                          variant="outline"
                          className={`text-xs ${
                            step.type === "decision"
                              ? "border-yellow-400 text-yellow-300"
                              : step.type === "action"
                                ? "border-green-400 text-green-300"
                                : "border-blue-400 text-blue-300"
                          }`}
                        >
                          {step.type}
                        </Badge>
                      </div>
                      <p className="text-gray-300 text-sm">{step.description}</p>
                      {step.dependencies && step.dependencies.length > 0 && (
                        <div className="mt-2">
                          <span className="text-xs text-gray-400">Depends on: </span>
                          {step.dependencies.map((dep, depIndex) => (
                            <Badge key={dep} variant="secondary" className="text-xs mr-1 bg-slate-600">
                              {workflow.find((s) => s.id === dep)?.title || dep}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-6 p-4 bg-slate-700/30 rounded-lg">
              <h4 className="text-white font-semibold mb-2">Workflow Summary</h4>
              <p className="text-gray-300 text-sm">
                This workflow defines {workflow.length} key steps for your {context?.projectType || "application"}. The
                process includes {workflow.filter((s) => s.type === "process").length} processing steps,
                {workflow.filter((s) => s.type === "decision").length} decision points, and
                {workflow.filter((s) => s.type === "action").length} action items.
              </p>
            </div>

            <Button onClick={handleContinue} className="w-full mt-6 bg-purple-600 hover:bg-purple-700">
              Approve Workflow & Continue
            </Button>
          </>
        )}
      </CardContent>
    </Card>
  )
}
