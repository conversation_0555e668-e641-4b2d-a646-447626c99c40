<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gemini Design Agent Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .container {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .upload-area {
            border: 2px dashed #666;
            padding: 40px;
            text-align: center;
            border-radius: 8px;
            margin-bottom: 20px;
            cursor: pointer;
            transition: border-color 0.3s;
        }
        .upload-area:hover {
            border-color: #999;
        }
        .upload-area.dragover {
            border-color: #4CAF50;
            background: rgba(76, 175, 80, 0.1);
        }
        input[type="file"] {
            display: none;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #45a049;
        }
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        .preview {
            max-width: 100%;
            max-height: 300px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .result {
            background: #333;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 500px;
            overflow-y: auto;
        }
        .loading {
            text-align: center;
            padding: 20px;
        }
        .spinner {
            border: 4px solid #333;
            border-top: 4px solid #4CAF50;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .error {
            background: #d32f2f;
            color: white;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background: #388e3c;
            color: white;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🎨 Gemini Design Agent Test</h1>
    <p>Test the Gemini 2.5 Pro multimodal design guide generation with image upload</p>

    <div class="container">
        <h2>Upload Design Reference Image</h2>
        <div class="upload-area" id="uploadArea">
            <p>📁 Click here or drag & drop an image</p>
            <p style="font-size: 14px; color: #999;">Supports: JPG, PNG, GIF, WebP</p>
        </div>
        <input type="file" id="fileInput" accept="image/*">
        <div id="preview"></div>
        <button id="generateBtn" disabled>🚀 Generate Style Guide with Gemini</button>
        <button id="clearBtn">🗑️ Clear</button>
    </div>

    <div class="container">
        <h2>Results</h2>
        <div id="status"></div>
        <div id="result"></div>
    </div>

    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const preview = document.getElementById('preview');
        const generateBtn = document.getElementById('generateBtn');
        const clearBtn = document.getElementById('clearBtn');
        const status = document.getElementById('status');
        const result = document.getElementById('result');

        let selectedFile = null;

        // Upload area click
        uploadArea.addEventListener('click', () => fileInput.click());

        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        // File input change
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFile(e.target.files[0]);
            }
        });

        function handleFile(file) {
            if (!file.type.startsWith('image/')) {
                showError('Please select an image file');
                return;
            }

            selectedFile = file;
            
            // Show preview
            const reader = new FileReader();
            reader.onload = (e) => {
                preview.innerHTML = `<img src="${e.target.result}" class="preview" alt="Preview">`;
            };
            reader.readAsDataURL(file);

            generateBtn.disabled = false;
            clearStatus();
        }

        // Generate style guide
        generateBtn.addEventListener('click', async () => {
            if (!selectedFile) return;

            generateBtn.disabled = true;
            showLoading('Analyzing image with Gemini 2.5 Pro...');

            try {
                const formData = new FormData();
                formData.append('images', selectedFile);

                const response = await fetch('/api/test-design', {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }

                const data = await response.json();
                
                if (data.styleGuide) {
                    const metadata = data.metadata || {};
                    showSuccess(`Style guide generated successfully!
                        Model: ${metadata.model || 'Unknown'}
                        Images: ${metadata.imageCount || 0}
                        Length: ${metadata.responseLength || 0} chars
                        Colors found: ${metadata.hexColors ? metadata.hexColors.length : 0}`);
                    showResult(data.styleGuide);

                    // Show metadata
                    if (metadata.hexColors && metadata.hexColors.length > 0) {
                        showColorPalette(metadata.hexColors);
                    }
                } else {
                    throw new Error('No style guide in response');
                }

            } catch (error) {
                console.error('Error:', error);
                showError(`Failed to generate style guide: ${error.message}`);
            } finally {
                generateBtn.disabled = false;
            }
        });

        // Clear
        clearBtn.addEventListener('click', () => {
            selectedFile = null;
            fileInput.value = '';
            preview.innerHTML = '';
            generateBtn.disabled = true;
            clearStatus();
            result.innerHTML = '';
        });

        function showLoading(message) {
            status.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    <p>${message}</p>
                </div>
            `;
        }

        function showError(message) {
            status.innerHTML = `<div class="error">❌ ${message}</div>`;
        }

        function showSuccess(message) {
            status.innerHTML = `<div class="success">✅ ${message}</div>`;
        }

        function clearStatus() {
            status.innerHTML = '';
        }

        function showResult(styleGuide) {
            result.innerHTML = `<div class="result">${styleGuide}</div>`;
        }

        function showColorPalette(colors) {
            const colorPalette = colors.map(color =>
                `<div style="display: inline-block; margin: 5px;">
                    <div style="width: 40px; height: 40px; background: ${color}; border: 1px solid #666; border-radius: 4px;"></div>
                    <div style="text-align: center; font-size: 12px; margin-top: 5px;">${color}</div>
                </div>`
            ).join('');

            result.innerHTML += `
                <div style="margin-top: 20px; padding: 15px; background: #333; border-radius: 4px;">
                    <h3>🎨 Extracted Color Palette</h3>
                    <div>${colorPalette}</div>
                </div>
            `;
        }
    </script>
</body>
</html>
