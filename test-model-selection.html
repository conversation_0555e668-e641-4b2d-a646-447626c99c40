<!DOCTYPE html>
<html>
<head>
    <title>Test Model Selection</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #000; color: #fff; }
        .test-section { background: #181818; padding: 20px; margin: 10px 0; border-radius: 8px; }
        .log { background: #333; padding: 10px; margin: 10px 0; border-radius: 5px; font-family: monospace; }
        .success { background: #0d4f3c; }
        .error { background: #4f0d0d; }
        button { padding: 10px 20px; margin: 5px; background: #ff2d55; color: white; border: none; border-radius: 5px; cursor: pointer; }
        select { padding: 8px; margin: 5px; background: #000; color: white; border: 1px solid #666; border-radius: 4px; }
        input { padding: 8px; margin: 5px; background: #333; color: white; border: 1px solid #666; border-radius: 4px; width: 300px; }
    </style>
</head>
<body>
    <h1>🧪 Test Model Selection & User Preferences</h1>
    
    <div class="test-section">
        <h3>User Preferences</h3>
        <label>Model:</label>
        <select id="modelSelect">
            <option value="x-ai/grok-4">Grok 4 (X.AI)</option>
            <option value="moonshotai/kimi-k2">Kimi K2 (Moonshot)</option>
            <option value="anthropic/claude-sonnet-4" selected>Claude Sonnet 4 (Default)</option>
            <option value="anthropic/claude-3.7-sonnet:thinking">Claude 3.7 Sonnet (Thinking)</option>
            <option value="anthropic/claude-3.7-sonnet">Claude 3.7 Sonnet</option>
            <option value="openai/gpt-4.1">GPT-4.1 (OpenAI)</option>
        </select>
        <br>
        <label>API Key (optional):</label>
        <input type="password" id="apiKeyInput" placeholder="sk-or-v1-...">
        <br>
        <label>
            <input type="checkbox" id="autonomousMode"> Autonomous Mode
        </label>
    </div>

    <div class="test-section">
        <h3>Test Planning API</h3>
        <button onclick="testPlanningAPI()">Test Planning with Selected Model</button>
        <button onclick="testStepAPI()">Test Step API with Model</button>
        <button onclick="clearLogs()">Clear Logs</button>
    </div>
    
    <div id="logs"></div>

    <script>
        function log(message, type = 'log') {
            const logs = document.getElementById('logs');
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            logs.appendChild(div);
            logs.scrollTop = logs.scrollHeight;
        }

        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }

        function getUserPreferences() {
            return {
                model: document.getElementById('modelSelect').value,
                apiKey: document.getElementById('apiKeyInput').value || undefined,
                autonomousMode: document.getElementById('autonomousMode').checked
            };
        }

        async function testPlanningAPI() {
            const userPreferences = getUserPreferences();
            log(`🚀 Testing Planning API with model: ${userPreferences.model}`, 'log');
            
            try {
                const response = await fetch('/api/planning', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        prompt: 'Test: Build a simple todo app with the selected AI model',
                        isInteractive: false,
                        answers: {},
                        userPreferences: userPreferences
                    })
                });

                const result = await response.json();
                
                if (response.ok) {
                    log(`✅ Planning API Success!`, 'success');
                    log(`📊 Session ID: ${result.sessionId}`, 'success');
                    log(`🤖 Model used: ${userPreferences.model}`, 'success');
                    log(`📈 Progress: ${result.progress}%`, 'success');
                    log(`🔧 Has results: ${!!result.results}`, 'success');
                    
                    // Store session ID for step test
                    window.testSessionId = result.sessionId;
                    window.testUserPreferences = userPreferences;
                } else {
                    log(`❌ Planning API Error: ${result.error}`, 'error');
                }
            } catch (error) {
                log(`❌ Planning API Exception: ${error.message}`, 'error');
            }
        }

        async function testStepAPI() {
            if (!window.testSessionId) {
                log('⚠️ No session ID. Run Planning API test first.', 'error');
                return;
            }

            const userPreferences = window.testUserPreferences || getUserPreferences();
            log(`🔧 Testing Step API with model: ${userPreferences.model}`, 'log');
            
            try {
                const response = await fetch('/api/planning/step', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        step: 'clarify',
                        context: {
                            sessionId: window.testSessionId,
                            prompt: 'Test: Build a simple todo app with the selected AI model',
                            isInteractive: false,
                            userAnswers: {},
                            results: { analyze: { test: true } },
                            userPreferences: userPreferences
                        }
                    })
                });

                const result = await response.json();
                
                if (response.ok) {
                    log(`✅ Step API Success!`, 'success');
                    log(`📊 Session ID: ${result.sessionId}`, 'success');
                    log(`🤖 Model used: ${userPreferences.model}`, 'success');
                    log(`🔧 Has results: ${!!result.results}`, 'success');
                } else {
                    log(`❌ Step API Error: ${result.error}`, 'error');
                }
            } catch (error) {
                log(`❌ Step API Exception: ${error.message}`, 'error');
            }
        }

        // Auto-log on load
        window.onload = function() {
            log('🧪 Model Selection Test Page Loaded', 'log');
            log('Select your preferred model and test the APIs', 'log');
        };
    </script>
</body>
</html>
