"use client"

import React from "react"
import { useState, useRef, useC<PERSON>back, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  MessageSquare,
  Settings,
  Upload,
  Eye,
  Code,
  ChevronLeft,
  ChevronRight,
  RotateCcw,
  Search,
  ArrowUp,
  User,
  Bot,
} from "lucide-react"
import AdminDashboard from "@/components/admin-dashboard"
import TaskList from "@/components/task-list"

import ConversationThreads from "@/components/conversation-threads"
import AgentPlanning from "@/components/agent-planning"

import { ScrollArea } from "@/components/ui/scroll-area"

interface Message {
  id: string
  type: "user" | "ai"
  content: string
  timestamp: Date
  tasks?: Task[]
}

interface Task {
  id: string
  title: string
  status: "pending" | "running" | "completed" | "error"
  type: "component" | "api" | "database" | "test"
  progress?: number
}

export default function AIDevInterface() {
  const [hasStarted, setHasStarted] = useState(false)
  const [messages, setMessages] = useState<Message[]>([])
  const [currentView, setCurrentView] = useState<"main" | "admin">("main")
  const [activeTab, setActiveTab] = useState<"preview" | "code" | "planning">("preview")
  const [chatInput, setChatInput] = useState("")
  const [isStreaming, setIsStreaming] = useState(false)
  const [selectedModel, setSelectedModel] = useState("elite-fullstack")
  const [sidebarWidth, setSidebarWidth] = useState(400) // Default width for SSR
  const [isResizing, setIsResizing] = useState(false)
  const [isPlanning, setIsPlanning] = useState(false)
  const [planningSteps, setPlanningSteps] = useState<any[]>([])
  const [showCompactPlanning, setShowCompactPlanning] = useState(false)

  // Set proper sidebar width after hydration
  useEffect(() => {
    if (typeof window !== "undefined") {
      const calculatedWidth = Math.floor((window.innerWidth - 48) / 3)
      setSidebarWidth(calculatedWidth)
    }
  }, [])

  const resizeRef = useRef<HTMLDivElement>(null)



  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
  }

  const handleSendMessage = async () => {
    if (!chatInput.trim()) return

    const newMessage: Message = {
      id: Date.now().toString(),
      type: "user",
      content: chatInput,
      timestamp: new Date(),
    }

    setMessages((prev) => [...prev, newMessage])
    const currentInput = chatInput
    setChatInput("")
    setIsStreaming(true)
    setHasStarted(true)

    // Start planning process for any development request
    setIsPlanning(true)
    setActiveTab("planning")
    setShowCompactPlanning(true)

    // Mock planning steps
    const mockSteps = [
      {
        id: "analyze",
        title: "Analyze project requirements",
        description: "Understanding the project scope and requirements",
        status: "running" as const,
        progress: 45
      },
      {
        id: "gather",
        title: "Gather additional details",
        description: "Collecting more information about the project",
        status: "pending" as const
      },
      {
        id: "summary",
        title: "Generate project summary",
        description: "Creating a comprehensive project overview",
        status: "pending" as const
      },
      {
        id: "tech-stack",
        title: "Select technology stack",
        description: "Choosing the best technologies for the project",
        status: "pending" as const
      }
    ]

    setPlanningSteps(mockSteps)

    // Simulate step progression
    setTimeout(() => {
      setPlanningSteps(prev => prev.map(step =>
        step.id === "analyze"
          ? { ...step, status: "completed" as const, progress: 100 }
          : step.id === "gather"
          ? { ...step, status: "running" as const, progress: 30 }
          : step
      ))
    }, 2000)

    setTimeout(() => {
      setPlanningSteps(prev => prev.map(step =>
        step.id === "gather"
          ? { ...step, status: "completed" as const, progress: 100 }
          : step.id === "summary"
          ? { ...step, status: "running" as const, progress: 60 }
          : step
      ))
    }, 4000)

    try {
      // TODO: Replace with actual API call to backend
      const response = await fetch('/api/build', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ prompt: currentInput })
      })

      if (!response.ok) {
        throw new Error('Failed to send message to backend')
      }

      const data = await response.json()

      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        type: "ai",
        content: data.message || "Processing your request...",
        timestamp: new Date(),
        tasks: data.tasks || []
      }
      setMessages((prev) => [...prev, aiResponse])

      // Update planning steps if provided
      if (data.planningSteps) {
        setPlanningSteps(data.planningSteps)
      }

      setIsStreaming(false)
    } catch (error) {
      console.error('Error sending message:', error)
      const errorResponse: Message = {
        id: (Date.now() + 1).toString(),
        type: "ai",
        content: "Error: Backend not connected. Please set up the API endpoint at /api/build",
        timestamp: new Date(),
      }
      setMessages((prev) => [...prev, errorResponse])
      setIsStreaming(false)
    }
  }

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    setIsResizing(true)
    e.preventDefault()
  }, [])

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isResizing) return
      const newWidth = e.clientX - 16 // Account for padding
      setSidebarWidth(Math.max(300, Math.min(800, newWidth)))
    },
    [isResizing]
  )

  const handleMouseUp = useCallback(() => {
    setIsResizing(false)
  }, [])

  useEffect(() => {
    if (isResizing) {
      document.addEventListener("mousemove", handleMouseMove)
      document.addEventListener("mouseup", handleMouseUp)
      return () => {
        document.removeEventListener("mousemove", handleMouseMove)
        document.removeEventListener("mouseup", handleMouseUp)
      }
    }
  }, [isResizing, handleMouseMove, handleMouseUp])

  if (currentView === "admin") {
    return <AdminDashboard onBack={() => setCurrentView("main")} />
  }

  // Landing page before user starts
  if (!hasStarted) {
    return (
      <div className="h-screen w-full bg-black text-white flex items-center justify-center">
        <div className="text-center">
          <div className="mb-8">
            <div className="w-16 h-16 mx-auto mb-4 bg-black rounded-full flex items-center justify-center border border-gray-800 relative">
              <div className="text-white text-lg font-bold">AP</div>
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></div>
              <div className="text-red-500 text-lg font-bold ml-1">3X</div>
            </div>
          </div>
          <div className="max-w-md mx-auto">
            <div className="relative">
              <input
                type="text"
                value={chatInput}
                onChange={(e) => setChatInput(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleSendMessage();
                  }
                }}
                placeholder="What would you like to build?"
                className="w-full bg-white text-black px-4 py-3 pr-12 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500"
              />
              <button
                onClick={handleSendMessage}
                disabled={!chatInput.trim() || isStreaming}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-red-500 hover:bg-red-600 disabled:bg-gray-400 rounded-md flex items-center justify-center transition-colors"
              >
                <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="h-screen w-full bg-[#000000] text-[#e5e5e5] font-sans text-sm flex flex-col overflow-hidden">
        <ConversationThreads isVisible={false} onSelectThread={() => {}} />



        {/* Top Navigation Bar */}
        <header className="h-14 bg-[#0a0a0a] border-b border-[#1a1a1a] flex items-center justify-between px-6">
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-3">
              <div className="w-14 h-14 rounded-lg overflow-hidden flex items-center justify-center">
                <img
                  src="/logo.png"
                  alt="AI Dev Ecosystem"
                  className="w-full h-full object-contain"
                />
              </div>
              
            </div>
            <div className="flex items-center gap-1 text-xs text-[#666]">
              <span>Personal</span>
              <span>/</span>
              <span>The Orb</span>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 px-3 text-xs text-[#888] hover:text-white hover:bg-[#1a1a1a] rounded-md"
              onClick={() => setCurrentView("admin")}
            >
              <Settings className="w-3 h-3 mr-2" />
              Settings
            </Button>
            <Button 
              size="sm" 
              className="h-8 px-4 bg-white text-black hover:bg-gray-100 text-xs font-medium rounded-md"
            >
              Publish
            </Button>
            <div className="w-7 h-7 bg-[#1a1a1a] rounded-full flex items-center justify-center ml-2">
              <User className="w-4 h-4 text-[#888]" />
            </div>
          </div>
        </header>

        <div className="flex flex-1 overflow-hidden p-4 gap-4">
          {/* Left Sidebar - Chat */}
          <aside
            style={{ width: sidebarWidth }}
            className="bg-[#0a0a0a] rounded-xl flex flex-col overflow-hidden relative h-full shadow-xl"
          >
            {/* Chat Header */}
            <div className="px-6 py-4 border-b border-[#1a1a1a] flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <h2 className="font-medium text-white text-sm">Chat</h2>
              </div>
              <div className="flex items-center gap-2">
                <button className="w-6 h-6 hover:bg-[#1a1a1a] rounded-md flex items-center justify-center transition-colors">
                  <MessageSquare className="w-4 h-4 text-[#666]" />
                </button>
              </div>
            </div>

            {/* Chat Messages */}
            <div className="flex-1 min-h-0 overflow-hidden">
              <ScrollArea className="h-full">
                <div className="px-6 py-4 space-y-4">
              {messages.length === 0 ? (
                <div className="h-full flex items-center justify-center min-h-[400px]">
                  <div className="text-center">
                    <div className="text-gray-400 text-sm">
                      Start a conversation to begin planning your project
                    </div>
                  </div>
                </div>
              ) : (
                messages.map((message) => (
                <div key={message.id} className="group">
                  <div className={`flex gap-3 ${message.type === "user" ? "flex-row-reverse" : ""}`}>
                    <div className="flex-shrink-0">
                      <div
                        className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium ${
                          message.type === "user"
                            ? "bg-blue-600 text-white"
                            : "bg-[#1a1a1a] text-[#888] border border-[#2a2a2a]"
                        }`}
                      >
                        {message.type === "user" ? "U" : "AI"}
                      </div>
                    </div>
                    <div className={`flex-1 max-w-[85%] ${message.type === "user" ? "text-right" : ""}`}>
                      <div
                        className={`inline-block px-4 py-3 rounded-lg text-sm leading-relaxed ${
                          message.type === "user"
                            ? "bg-blue-600 text-white shadow-lg"
                            : "bg-[#111111] text-[#e5e5e5] shadow-md"
                        }`}
                      >
                        <div className="whitespace-pre-wrap">{message.content}</div>
                      </div>
                      <div className={`text-xs text-[#666] mt-1 ${message.type === "user" ? "text-right" : ""}`}>
                        {formatTime(message.timestamp)}
                      </div>
                    </div>
                  </div>

                  {/* Task List for AI messages */}
                  {message.type === "ai" && message.tasks && (
                    <div className="ml-10">
                      <TaskList tasks={message.tasks} title="Current Tasks" />
                    </div>
                  )}


                </div>
              )))}
                </div>
              </ScrollArea>
            </div>




            {/* Compact Planning Display */}
            {showCompactPlanning && isPlanning && (
              <div className="shrink-0 p-3 bg-[#0a0a0a] border-t border-[#1a1a1a]">
                <AgentPlanning
                  mode="compact"
                  steps={planningSteps}
                  title="AG3NT is Planning Your Project"
                  onStepClick={(stepId) => setActiveTab("planning")}
                  onComplete={() => {
                    setShowCompactPlanning(false)
                    setIsPlanning(false)
                  }}
                  className="max-w-md"
                />
              </div>
            )}

            {/* Chat Input */}
            <div className="shrink-0 p-3 bg-[#0a0a0a] shadow-inner">
              <div className="relative bg-[#111111] rounded-xl p-1 focus-within:shadow-blue-500/20 focus-within:shadow-lg transition-all shadow-md">
                <textarea
                  value={chatInput}
                  onChange={(e) => {
                    setChatInput(e.target.value);
                    // Auto-resize textarea
                    const textarea = e.target as HTMLTextAreaElement;
                    textarea.style.height = 'auto';
                    const scrollHeight = textarea.scrollHeight;
                    const maxHeight = 240; // 10 lines * 24px line height
                    textarea.style.height = Math.min(scrollHeight, maxHeight) + 'px';
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
                      e.preventDefault();
                      handleSendMessage();
                    }
                  }}
                  placeholder="Message AI..."
                  className="w-full bg-transparent px-4 py-3 pr-12 text-sm text-white placeholder:text-[#666] resize-none focus:outline-none leading-6 scrollbar-none overflow-y-auto"
                  rows={3}
                  style={{
                    minHeight: '84px',
                    maxHeight: '240px'
                  }}
                />
                <button
                  onClick={handleSendMessage}
                  disabled={!chatInput.trim()}
                  className="absolute right-2 top-3 w-8 h-8 bg-blue-600 hover:bg-blue-700 disabled:bg-[#333] disabled:text-[#666] rounded-lg flex items-center justify-center transition-colors shadow-md"
                >
                  <ArrowUp className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* Resize Handle */}
            <div
              ref={resizeRef}
              className="absolute top-0 right-0 w-1 h-full cursor-col-resize hover:bg-blue-500 transition-colors rounded-r-xl"
              onMouseDown={handleMouseDown}
            />
          </aside>

          {/* Main Editor Area */}
          <main className="flex-1 flex flex-col bg-[#0a0a0a] rounded-xl overflow-hidden shadow-xl">
            {/* Editor Tabs */}
            <div className="h-12 bg-[#0a0a0a] border-b border-[#1a1a1a] flex items-center px-6">
              <div className="flex items-center gap-1">
                <button
                  className={`flex items-center gap-2 px-3 py-1.5 text-xs rounded-md transition-colors ${
                    activeTab === "preview"
                      ? "bg-[#1a1a1a] text-white"
                      : "text-[#666] hover:text-white hover:bg-[#111111]"
                  }`}
                  onClick={() => setActiveTab("preview")}
                >
                  Preview
                </button>
                <button
                  className={`flex items-center gap-2 px-3 py-1.5 text-xs rounded-md transition-colors ${
                    activeTab === "code"
                      ? "bg-[#1a1a1a] text-white"
                      : "text-[#666] hover:text-white hover:bg-[#111111]"
                  }`}
                  onClick={() => setActiveTab("code")}
                >
                  Code
                </button>
                <button
                  className={`flex items-center gap-2 px-3 py-1.5 text-xs rounded-md transition-colors ${
                    activeTab === "planning"
                      ? "bg-[#1a1a1a] text-white"
                      : "text-[#666] hover:text-white hover:bg-[#111111]"
                  }`}
                  onClick={() => setActiveTab("planning")}
                >
                  Planning
                  {isPlanning && (
                    <span className="ml-1 inline-block w-1.5 h-1.5 bg-blue-400 rounded-full animate-pulse" />
                  )}
                </button>
              </div>
            </div>

            {/* Content Area */}
            <div className="flex-1 bg-[#0a0a0a]">
              {activeTab === "preview" ? (
                <div className="h-full flex items-center justify-center">
                  <div className="text-center text-gray-400">
                    <div className="text-sm">Preview will appear here</div>
                    <div className="text-xs mt-2 opacity-70">Start planning to see your project preview</div>
                  </div>
                </div>
              ) : activeTab === "planning" ? (
                <div className="h-full p-6">
                  <AgentPlanning
                    mode="full"
                    steps={planningSteps}
                    title="Project Development Plan"
                    onStepClick={(stepId) => console.log('Step clicked:', stepId)}
                    onComplete={() => {
                      setIsPlanning(false)
                      setActiveTab("preview")
                    }}
                  />
                </div>
              ) : (
                <div className="h-full flex items-center justify-center">
                  <div className="text-center text-gray-400">
                    <div className="text-sm">Code editor will appear here</div>
                    <div className="text-xs mt-2 opacity-70">Generated code will be displayed in this tab</div>
                  </div>
                </div>
              )}
            </div>
          </main>
        </div>
      </div>
  )
}
