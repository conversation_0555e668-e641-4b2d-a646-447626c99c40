/**
 * Test Module Imports
 * Verify that all the new modules can be imported correctly
 */

async function testImports() {
  console.log('🧪 Testing Module Imports...\n')

  const tests = [
    {
      name: 'Inngest Client',
      path: './src/lib/inngest/client.js',
      check: (module) => !!module.inngest && !!module.sendPlanningEvent
    },
    {
      name: 'Planning Types',
      path: './src/modules/planning/planning.types.js',
      check: (module) => !!module.PlanningStepSchema && !!module.PlanningStateSchema
    },
    {
      name: 'Planning Service',
      path: './src/modules/planning/planning.service.js',
      check: (module) => !!module.PlanningService
    },
    {
      name: 'Environment Config',
      path: './src/env.js',
      check: (module) => !!module.simpleConfig
    }
  ]

  let passed = 0
  let failed = 0

  for (const test of tests) {
    try {
      console.log(`Testing ${test.name}...`)
      const module = await import(test.path)
      
      if (test.check(module)) {
        console.log(`✅ ${test.name} imported and validated successfully`)
        passed++
      } else {
        console.log(`❌ ${test.name} imported but validation failed`)
        failed++
      }
    } catch (error) {
      console.log(`❌ ${test.name} import failed:`, error.message)
      failed++
    }
  }

  console.log(`\n📊 Results: ${passed} passed, ${failed} failed`)
  
  if (failed === 0) {
    console.log('🎉 All imports successful! The async orchestration stack is ready.')
    console.log('\n🚀 Next steps:')
    console.log('1. Start your dev server: pnpm run dev')
    console.log('2. Start Inngest dev server: npx inngest-cli@latest dev')
    console.log('3. Test the planning workflow in the UI')
  } else {
    console.log('⚠️  Some imports failed. Please fix the issues above.')
  }
}

testImports().catch(console.error)
