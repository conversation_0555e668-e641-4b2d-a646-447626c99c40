import { createLogger } from '../../utils/logger.js';
import { GitIngester } from '../sources/GitIngester.js';
import { LocalFileWatcher } from '../sources/LocalFileWatcher.js';
import { ASTProcessor } from '../../core/processors/ASTProcessor.js';
import { SymbolTableBuilder } from '../../core/processors/SymbolTableBuilder.js';
import { GraphManager } from '../../core/graph/GraphManager.js';

const logger = createLogger('IngestionPipeline');

/**
 * Main ingestion pipeline that orchestrates code processing
 */
export class IngestionPipeline {
  constructor(config) {
    this.config = config;
    this.processors = [];
    this.queue = [];
    this.isProcessing = false;
    this.isRunning = false;
    this.graphManager = null;
    this.statistics = {
      filesProcessed: 0,
      filesQueued: 0,
      processingErrors: 0,
      startTime: null
    };
  }

  /**
   * Initialize the ingestion pipeline
   */
  async initialize() {
    logger.info('Initializing ingestion pipeline');

    try {
      // Initialize graph manager
      this.graphManager = new GraphManager(this.config.neo4jClient);
      await this.graphManager.initialize();

      // Initialize processors
      this.processors = [
        new ASTProcessor(this.config),
        new SymbolTableBuilder(this.config)
      ];

      // Initialize each processor
      for (const processor of this.processors) {
        await processor.initialize();
      }

      this.statistics.startTime = Date.now();
      logger.info('Ingestion pipeline initialized successfully', {
        processorCount: this.processors.length
      });

    } catch (error) {
      logger.error('Failed to initialize ingestion pipeline', { error: error.message });
      throw error;
    }
  }

  /**
   * Start the processing loop
   */
  async start() {
    if (this.isRunning) {
      logger.warn('Ingestion pipeline already running');
      return;
    }

    this.isRunning = true;
    logger.info('Starting ingestion pipeline processing loop');

    // Start the processing loop
    this.startProcessingLoop();
  }

  /**
   * Stop the processing loop
   */
  async stop() {
    if (!this.isRunning) {
      return;
    }

    logger.info('Stopping ingestion pipeline');
    this.isRunning = false;

    // Wait for current processing to complete
    while (this.isProcessing) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    logger.info('Ingestion pipeline stopped');
  }

  /**
   * Process a repository
   */
  async processRepository(repositoryPath, options = {}) {
    logger.info('Starting repository processing', {
      repositoryPath,
      options
    });

    const startTime = Date.now();
    let filesProcessed = 0;
    let nodesCreated = 0;
    let relationshipsCreated = 0;

    try {
      // Initialize Git ingester
      const gitIngester = new GitIngester(repositoryPath, this.config);
      await gitIngester.initialize();

      // Get all files from repository
      const files = await gitIngester.getFiles(options.fileFilter);
      logger.info('Found files for processing', {
        fileCount: files.length,
        repositoryPath
      });

      // Process files in batches
      const batchSize = this.config.batchSize || 10;
      for (let i = 0; i < files.length; i += batchSize) {
        const batch = files.slice(i, i + batchSize);
        
        const batchResults = await Promise.allSettled(
          batch.map(file => this.processFile(file))
        );

        // Collect results
        for (const result of batchResults) {
          if (result.status === 'fulfilled') {
            filesProcessed++;
            nodesCreated += result.value.nodesCreated || 0;
            relationshipsCreated += result.value.relationshipsCreated || 0;
          } else {
            this.statistics.processingErrors++;
            logger.error('File processing failed in batch', {
              error: result.reason.message
            });
          }
        }

        // Log progress
        logger.info('Batch processing completed', {
          batchNumber: Math.floor(i / batchSize) + 1,
          totalBatches: Math.ceil(files.length / batchSize),
          filesProcessed,
          repositoryPath
        });
      }

      const duration = Date.now() - startTime;
      const result = {
        repositoryPath,
        filesProcessed,
        nodesCreated,
        relationshipsCreated,
        duration,
        errors: this.statistics.processingErrors
      };

      logger.info('Repository processing completed', result);
      return result;

    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error('Repository processing failed', {
        repositoryPath,
        duration,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Process a single file
   */
  async processFile(file) {
    const startTime = Date.now();
    
    try {
      logger.debug('Processing file', {
        path: file.path,
        language: file.language,
        size: file.size
      });

      // Create initial context
      let context = {
        file: file,
        ast: null,
        symbols: null,
        embeddings: null,
        metadata: {
          timestamp: new Date(),
          version: file.version || file.lastModified
        }
      };

      // Process through pipeline
      for (const processor of this.processors) {
        context = await processor.process(context);
      }

      // Store in graph database
      const graphResult = await this.graphManager.storeContext(context);

      const duration = Date.now() - startTime;
      this.statistics.filesProcessed++;

      logger.debug('File processing completed', {
        path: file.path,
        duration,
        nodesCreated: graphResult.nodesCreated,
        relationshipsCreated: graphResult.relationshipsCreated
      });

      return {
        file: file.path,
        duration,
        nodesCreated: graphResult.nodesCreated,
        relationshipsCreated: graphResult.relationshipsCreated
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      this.statistics.processingErrors++;
      
      logger.error('File processing failed', {
        path: file.path,
        duration,
        error: error.message
      });
      
      throw error;
    }
  }

  /**
   * Add file to processing queue
   */
  queueFile(file) {
    this.queue.push(file);
    this.statistics.filesQueued++;
    
    logger.debug('File queued for processing', {
      path: file.path,
      queueSize: this.queue.length
    });
  }

  /**
   * Start the processing loop for queued files
   */
  startProcessingLoop() {
    const processNext = async () => {
      if (!this.isRunning) {
        return;
      }

      if (!this.isProcessing && this.queue.length > 0) {
        this.isProcessing = true;
        
        try {
          const file = this.queue.shift();
          await this.processFile(file);
        } catch (error) {
          logger.error('Queue processing error', { error: error.message });
        } finally {
          this.isProcessing = false;
        }
      }

      // Schedule next iteration
      setTimeout(processNext, 100);
    };

    processNext();
  }

  /**
   * Get pipeline health status
   */
  async getHealth() {
    return {
      status: this.isRunning ? 'healthy' : 'stopped',
      isRunning: this.isRunning,
      isProcessing: this.isProcessing,
      queueSize: this.queue.length,
      processorCount: this.processors.length
    };
  }

  /**
   * Get pipeline statistics
   */
  async getStatistics() {
    const uptime = this.statistics.startTime ? Date.now() - this.statistics.startTime : 0;
    
    return {
      ...this.statistics,
      uptime,
      queueSize: this.queue.length,
      isProcessing: this.isProcessing,
      processingRate: uptime > 0 ? (this.statistics.filesProcessed / uptime) * 1000 : 0 // files per second
    };
  }
}

export default IngestionPipeline;
