"use client"

import type React from "react"

import { useState, useEffect } from "react"

interface HoverTriggerProps {
  onHoverChange: (isHovering: boolean) => void
  children: React.ReactNode
}

export default function HoverTrigger({ onHoverChange, children }: HoverTriggerProps) {
  const [isHovering, setIsHovering] = useState(false)

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      const shouldShow = e.clientX <= 10 // Show when mouse is within 10px of left edge

      if (shouldShow !== isHovering) {
        setIsHovering(shouldShow)
        onHoverChange(shouldShow)
      }
    }

    const handleMouseLeave = () => {
      setIsHovering(false)
      onHoverChange(false)
    }

    document.addEventListener("mousemove", handleMouseMove)
    document.addEventListener("mouseleave", handleMouseLeave)

    return () => {
      document.removeEventListener("mousemove", handleMouseMove)
      document.removeEventListener("mouseleave", handleMouseLeave)
    }
  }, [isHovering, onHoverChange])

  return (
    <>
      {children}
      {/* Invisible trigger zone */}
      <div className="fixed left-0 top-0 w-2 h-full z-40 pointer-events-none" />
    </>
  )
}
