import { type NextRequest, NextResponse } from "next/server"
import { aiService } from "@/lib/ai-service"
import { PlanningGraph } from "@/lib/planning-graph"
import { trackStepStart, trackStepComplete, trackStepFailed } from "@/lib/progress-tracker"

// Simple async orchestration flag
const USE_ASYNC_ORCHESTRATION = process.env.ENABLE_ASYNC_ORCHESTRATION === 'true'

export async function POST(request: NextRequest) {
  let step = "unknown"
  let context = {}

  try {
    const requestData = await request.json()
    step = requestData.step
    context = requestData.context || {}
    const answer = requestData.answer

    if (!step) {
      return NextResponse.json({ error: "Step is required" }, { status: 400 })
    }

    console.log(`Processing AI-powered step: ${step}`)

    // Check if this is the design step and we already have a design style guide from images
    if (step === "design" && context?.designStyleGuide) {
      console.log("Using design style guide from uploaded images")
      return NextResponse.json({
        results: {
          design: context.designStyleGuide,
        },
        needsInput: false,
        completed: true,
      }, {
        headers: {
          "Content-Type": "application/json",
        },
      })
    }

    // Process the step using enhanced orchestration
    console.log(`🚀 STEP API: Processing step: ${step} (Async: ${USE_ASYNC_ORCHESTRATION})`)
    console.log(`📊 STEP API: Session ID: ${context?.sessionId || 'no-session'}`)
    console.log(`🔧 STEP API: Progress tracking enabled: TRUE`)

    if (USE_ASYNC_ORCHESTRATION) {
      // TODO: Use Inngest workflow
      console.log('📋 STEP API: Using async orchestration (coming soon)')
      const result = await processStepWithMCP(step, context, answer)
      return NextResponse.json(result)
    } else {
      // Use current MCP-enhanced system with progress tracking
      console.log('🧠 STEP API: Using MCP-enhanced system with progress tracking')
      const result = await processStepWithMCP(step, context, answer)
      console.log(`✅ STEP API: Step ${step} completed successfully`)
      return NextResponse.json(result)
    }

    console.log(`Step ${step} completed successfully`)

    return NextResponse.json(result, {
      headers: {
        "Content-Type": "application/json",
      },
    })

  } catch (error) {
    console.error("Planning step API error:", error)
    console.error("Error stack:", error instanceof Error ? error.stack : "No stack trace")
    console.error("Step context:", { step, contextKeys: Object.keys(context || {}) })

    // More detailed error information
    let errorMessage = "Failed to process planning step"
    let suggestion = "Please try again"

    if (error instanceof Error) {
      console.error("Error message:", error.message)

      if (error.message.includes("OPENROUTER_API_KEY")) {
        errorMessage = "API key configuration error"
        suggestion = "Please check your OPENROUTER_API_KEY environment variable"
      } else if (error.message.includes("AI API Error")) {
        errorMessage = "AI service error"
        suggestion = "The AI service is temporarily unavailable. Please try again in a moment"
      } else if (error.message.includes("timeout")) {
        errorMessage = "Request timeout"
        suggestion = "The request took too long. Please try again"
      } else if (error.message.includes("JSON")) {
        errorMessage = "Response parsing error"
        suggestion = "The AI response was malformed. Please try again"
      } else if (error.message.includes("analyzePrompt")) {
        errorMessage = "Analysis function error"
        suggestion = "There was an issue with the analysis function. Please try again"
      }
    }

    return NextResponse.json({
      error: errorMessage,
      details: error instanceof Error ? error.message : "Unknown error",
      suggestion: suggestion,
      needsInput: false,
      completed: false,
    }, { status: 500 })
  }
}

/**
 * Process step using MCP-enhanced PlanningGraph with progress tracking
 */
async function processStepWithMCP(step: string, context: any, answer?: string): Promise<any> {
  const sessionId = context?.sessionId || `session-${Date.now()}`

  console.log(`🧠 Processing MCP-enhanced step: ${step}`)
  console.log(`Context keys:`, Object.keys(context || {}))

  // Track step start
  trackStepStart(sessionId, step)

  const prompt = context?.prompt || ""
  const userAnswers = context?.userAnswers || {}
  const previousResults = context?.results || {}

  // Update user answers if answer provided
  if (answer && context?.question) {
    userAnswers[context.question.id] = answer
  }

  // Create planning state for the enhanced PlanningGraph
  const planningState = {
    prompt,
    isInteractive: false, // API calls are not interactive
    userAnswers,
    currentStep: step,
    results: previousResults,
    completed: false,
    needsInput: false
  }

  try {
    // Use the MCP-enhanced PlanningGraph
    const planningGraph = new PlanningGraph()

    // Initialize the context engine for this step
    await planningGraph.initializeContextEngine(planningState)

    // Execute just this step with MCP enhancement
    const result = await planningGraph.executeStepWithContext(step, planningState)

    console.log(`✅ MCP-enhanced ${step} completed successfully`)

    // Track step completion
    trackStepComplete(sessionId, step, result.results)

    // Return in the format expected by the API
    return {
      results: result.results,
      needsInput: result.needsInput,
      question: result.question,
      completed: result.completed,
      sessionId // Include session ID for tracking
    }

  } catch (error) {
    console.error(`❌ MCP-enhanced ${step} failed:`, error)

    // Track step failure
    trackStepFailed(sessionId, step, error instanceof Error ? error.message : 'Unknown error')

    // Fallback to original processStep if MCP fails
    console.log(`🔄 Falling back to original processStep for ${step}`)
    return await processStep(step, context, answer)
  }
}

async function processStep(step: string, context: any, answer?: string): Promise<any> {
  console.log(`Processing step: ${step}`)
  console.log(`Context keys:`, Object.keys(context || {}))

  const prompt = context?.prompt || ""
  const userAnswers = context?.userAnswers || {}
  const previousResults = context?.results || {}
  const designContext = context?.designStyleGuide || previousResults?.design

  console.log(`Prompt: ${prompt.substring(0, 100)}...`)
  console.log(`Previous results keys:`, Object.keys(previousResults))
  if (designContext) {
    console.log("Design context available - will be included in planning")
  }

  // Update user answers if answer provided
  if (answer && context?.question) {
    userAnswers[context.question.id] = answer
  }

  // Use AI service for ALL steps - no fallbacks
  switch (step) {
    case "analyze":
      return await analyzePrompt(prompt)
    case "clarify":
      return await clarifyRequirements(prompt, previousResults.analyze, userAnswers)
    case "summary":
      return await generateSummary(prompt, previousResults.analyze, userAnswers)
    case "techstack":
      return await selectTechStack(prompt, previousResults.analyze, userAnswers)
    case "prd":
      return await createPRD(prompt, previousResults, userAnswers)
    case "context-profile":
      return await generateContextProfile(prompt, previousResults, userAnswers)
    case "wireframes":
      return await designWireframes(prompt, previousResults)
    case "design":
      return await createDesignGuidelines(prompt, previousResults)
    case "database":
      return await designDatabaseSchema(prompt, previousResults)
    case "filesystem":
      return await planFilesystem(prompt, previousResults, designContext)
    case "workflow":
      return await defineWorkflow(prompt, previousResults, designContext)
    case "tasks":
      return await breakdownTasks(prompt, previousResults, designContext)
    case "scaffold":
      return await generateScaffold(prompt, previousResults)
    default:
      throw new Error(`Unknown step: ${step}`)
  }
}

async function analyzePrompt(prompt: string) {
  console.log("Starting AI-powered prompt analysis...")
  try {
    const analysis = await aiService.analyzePrompt(prompt, {})
    console.log("AI analysis completed successfully")

    return {
      results: {
        analyze: analysis,
      },
      needsInput: false,
      completed: false,
    }
  } catch (error) {
    console.error("Error in analyzePrompt:", error)
    throw new Error(`Failed to analyze prompt: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

async function clarifyRequirements(prompt: string, analysis: any, userAnswers: Record<string, string>) {
  console.log("Generating AI-powered clarification questions...")

  // Check if we already have enough answers
  const requiredAnswers = ['target_users', 'platform', 'timeline', 'budget']
  const missingAnswers = requiredAnswers.filter(key => !userAnswers[key])

  if (missingAnswers.length === 0) {
    // All questions answered, proceed
    return {
      results: {
        clarify: {
          targetUsers: userAnswers.target_users,
          platform: userAnswers.platform,
          timeline: userAnswers.timeline,
          budget: userAnswers.budget,
          completed: true
        }
      },
      needsInput: false,
      completed: false,
    }
  }

  // Generate clarification questions using AI
  const clarificationData = await aiService.generateClarificationQuestions(prompt, analysis)
  
  // Return the first unanswered question
  const nextQuestion = clarificationData.questions.find((q: any) => 
    !userAnswers[q.id] && missingAnswers.includes(q.id)
  ) || clarificationData.questions[0]

  return {
    results: {
      clarify: clarificationData
    },
    question: nextQuestion,
    needsInput: true,
    completed: false,
  }
}

async function generateSummary(prompt: string, analysis: any, clarifications: any) {
  console.log("Generating AI-powered project summary...")
  const summary = await aiService.generateSummary(prompt, analysis, clarifications)
  console.log("AI summary generated successfully")

  return {
    results: {
      summary: summary,
    },
    needsInput: false,
    completed: false,
  }
}

async function selectTechStack(prompt: string, analysis: any, clarifications: any) {
  console.log("Selecting AI-powered technology stack...")
  const techStack = await aiService.selectTechStack(prompt, analysis, clarifications)
  console.log("AI tech stack selection completed")

  return {
    results: {
      techstack: techStack,
    },
    needsInput: false,
    completed: false,
  }
}

async function createPRD(prompt: string, previousResults: any, clarifications: any) {
  console.log("Creating AI-powered PRD...")
  const prd = await aiService.createPRD(
    prompt, 
    previousResults.analyze, 
    previousResults.techstack, 
    clarifications
  )
  console.log("AI PRD creation completed")

  return {
    results: {
      prd: prd,
    },
    needsInput: false,
    completed: false,
  }
}

async function generateContextProfile(prompt: string, previousResults: any, clarifications: any) {
  console.log("Generating AI-powered context profile...")
  const contextProfile = await aiService.generateContextProfile(
    prompt,
    previousResults.analyze,
    previousResults.summary,
    previousResults.prd
  )
  console.log("AI context profile generation completed")

  return {
    results: {
      "context-profile": contextProfile,
    },
    needsInput: false,
    completed: false,
  }
}

async function designWireframes(prompt: string, previousResults: any) {
  console.log("Designing AI-powered wireframes...")
  const wireframes = await aiService.designWireframes(
    prompt, 
    previousResults.analyze, 
    previousResults.prd
  )
  console.log("AI wireframe design completed")

  return {
    results: {
      wireframes: wireframes,
    },
    needsInput: false,
    completed: false,
  }
}

async function createDesignGuidelines(prompt: string, previousResults: any) {
  console.log("Creating AI-powered design guidelines...")
  
  // For now, create basic design guidelines
  // This could be expanded with a dedicated AI method in the future
  const designGuidelines = {
    colorPalette: ["#1f2937", "#3b82f6", "#10b981", "#f59e0b"],
    typography: "Modern sans-serif fonts",
    spacing: "8px grid system",
    components: "Consistent button styles, form inputs, cards"
  }

  return {
    results: {
      design: designGuidelines,
    },
    needsInput: false,
    completed: false,
  }
}

async function planFilesystem(prompt: string, previousResults: any, designContext?: string) {
  console.log("Planning AI-powered file system...")
  const filesystem = await aiService.planFileSystem(
    prompt,
    previousResults.techstack,
    previousResults.analyze,
    designContext
  )
  console.log("AI filesystem planning completed")

  return {
    results: {
      filesystem: filesystem,
    },
    needsInput: false,
    completed: false,
  }
}

async function defineWorkflow(prompt: string, previousResults: any, designContext?: string) {
  console.log("Defining AI-powered workflow...")
  const workflow = await aiService.defineWorkflow(
    prompt,
    previousResults.analyze,
    previousResults.prd,
    previousResults.wireframes,
    designContext
  )
  console.log("AI workflow definition completed")

  return {
    results: {
      workflow: workflow,
    },
    needsInput: false,
    completed: false,
  }
}

async function breakdownTasks(prompt: string, previousResults: any, designContext?: string) {
  console.log("Breaking down AI-powered tasks...")
  const tasks = await aiService.breakdownTasks(prompt, previousResults, designContext)
  console.log("AI task breakdown completed")

  return {
    results: {
      tasks: tasks,
    },
    needsInput: false,
    completed: false, // Not the final step anymore
  }
}

async function designDatabaseSchema(prompt: string, previousResults: any) {
  console.log("Designing AI-powered database schema...")
  const schema = await aiService.designDatabaseSchema(
    prompt,
    previousResults.analyze,
    previousResults.prd,
    previousResults.techstack
  )
  console.log("AI database schema design completed")

  return {
    results: {
      database: schema,
    },
    needsInput: false,
    completed: false,
  }
}

async function generateScaffold(prompt: string, previousResults: any) {
  console.log("Generating AI-powered project scaffold...")
  const scaffold = await aiService.generateProjectScaffold(
    prompt,
    previousResults.analyze,
    previousResults.techstack,
    previousResults.filesystem,
    previousResults.database
  )
  console.log("AI project scaffold generation completed")

  return {
    results: {
      scaffold: scaffold,
    },
    needsInput: false,
    completed: true, // This is now the final step
  }
}
