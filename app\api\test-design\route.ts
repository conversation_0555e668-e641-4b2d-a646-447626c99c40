import { type NextRequest, NextResponse } from "next/server"

export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Test Design Agent: Starting image analysis')
    
    const formData = await request.formData()
    const images = formData.getAll('images') as File[]

    if (!images || images.length === 0) {
      return NextResponse.json(
        { error: 'No images provided' },
        { status: 400 }
      )
    }

    console.log(`🧪 Test Design Agent: Processing ${images.length} image(s)`)

    // Convert images to base64 for multimodal input
    const imageContent = await Promise.all(
      images.map(async (image) => {
        const bytes = await image.arrayBuffer()
        const base64 = Buffer.from(bytes).toString('base64')
        return {
          type: 'image_url',
          image_url: {
            url: `data:${image.type};base64,${base64}`
          }
        }
      })
    )

    console.log('🧪 Test Design Agent: Images converted to base64')

    // Create the multimodal prompt for Gemini
    const textPrompt = `Analyze this design reference image and create a comprehensive web design style guide. Extract EXACT colors, typography, spacing, and component details.

IMPORTANT:
- Use color picker precision to get EXACT hex codes from the image
- Measure actual spacing and sizing you observe
- Identify specific fonts or provide accurate alternatives
- Document every visual detail you can see
- Create a professional style guide that a developer could use to recreate this design exactly

Your response must be a detailed style guide document following this structure:

# Web Design Style Guide

## 1. Color Palette
Extract the EXACT hex codes for each color you see:
**Primary Background:** #[extract exact hex from darkest background areas]
**Secondary Background:** #[extract hex from secondary background areas]
**Card/Module Background:** #[extract hex from card/content backgrounds]
**Primary Accent:** #[extract hex from main accent color - buttons, highlights]
**Secondary Accent:** #[extract hex from secondary accent colors]
**Primary Text:** #[extract hex from main text color]
**Secondary Text:** #[extract hex from secondary/muted text]
**Border Color:** #[extract hex from borders and dividers]

For each color, also provide RGB values: rgb(R, G, B)

## 2. Typography
**Font Family:** [Identify the font or provide close alternatives]
**Main Heading Size:** [measure and specify in px]
**Body Text Size:** [measure and specify in px]
**Small Text Size:** [measure and specify in px]
**Font Weights Used:** [list all weights you observe]
**Line Height:** [measure spacing between lines]

## 3. Layout & Spacing
**Container Width:** [measure max content width]
**Spacing Between Elements:** [measure gaps - small, medium, large]
**Border Radius:** [measure corner rounding in px]
**Grid System:** [describe layout structure]

## 4. Components
**Buttons:** [describe button styles, colors, sizes, states]
**Cards:** [describe card styling, shadows, borders]
**Navigation:** [describe nav styling and layout]
**Forms:** [describe input styling and layout]

## 5. CSS Variables
Provide a CSS custom properties setup:
\`\`\`css
:root {
  --bg-primary: #[primary background hex];
  --bg-secondary: #[secondary background hex];
  --bg-card: #[card background hex];
  --color-accent: #[accent color hex];
  --color-text: #[text color hex];
  --color-text-muted: #[muted text hex];
  --border-color: #[border color hex];
}
\`\`\`

REMEMBER: Look carefully at the image and extract the ACTUAL colors you see. Don't use generic colors - use the exact colors from this specific design.`

    // Check API key
    if (!process.env.OPENROUTER_API_KEY) {
      console.error('🧪 Test Design Agent: OPENROUTER_API_KEY not configured')
      return NextResponse.json(
        { error: 'API key not configured' },
        { status: 500 }
      )
    }

    console.log('🧪 Test Design Agent: Calling OpenRouter API with Gemini 2.5 Pro')

    // Call OpenRouter API with Gemini 2.5 Pro (multimodal)
    const openRouterResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
        'X-Title': 'Design Agent Test',
        'HTTP-Referer': process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'
      },
      body: JSON.stringify({
        model: 'google/gemini-2.5-pro',
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: textPrompt
              },
              ...imageContent
            ]
          }
        ],
        max_tokens: 8000,
        temperature: 0.1
      })
    })

    if (!openRouterResponse.ok) {
      const errorText = await openRouterResponse.text()
      console.error('🧪 Test Design Agent: OpenRouter API error:', openRouterResponse.status, errorText)
      return NextResponse.json(
        { 
          error: 'OpenRouter API error',
          details: errorText,
          status: openRouterResponse.status
        },
        { status: 500 }
      )
    }

    const openRouterData = await openRouterResponse.json()
    console.log('🧪 Test Design Agent: OpenRouter API response received')

    if (!openRouterData.choices || !openRouterData.choices[0] || !openRouterData.choices[0].message) {
      console.error('🧪 Test Design Agent: Invalid response structure:', openRouterData)
      return NextResponse.json(
        { error: 'Invalid response from AI service' },
        { status: 500 }
      )
    }

    const styleGuide = openRouterData.choices[0].message.content
    console.log('🧪 Test Design Agent: Style guide generated successfully')
    console.log('🧪 Style guide length:', styleGuide.length, 'characters')
    console.log('🧪 First 500 characters:', styleGuide.substring(0, 500))

    // Extract hex colors for additional processing
    const hexColors = styleGuide.match(/#[0-9A-Fa-f]{6}/g) || []
    console.log('🧪 Found hex colors:', hexColors)

    return NextResponse.json({
      success: true,
      styleGuide: styleGuide,
      metadata: {
        model: 'google/gemini-2.5-pro',
        imageCount: images.length,
        responseLength: styleGuide.length,
        hexColors: hexColors,
        usage: openRouterData.usage || null
      }
    })

  } catch (error) {
    console.error('🧪 Test Design Agent error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
