import simpleGit from 'simple-git';
import fs from 'fs/promises';
import path from 'path';
import { createLogger } from '../../utils/logger.js';

const logger = createLogger('GitIngester');

/**
 * Git repository ingester for code analysis
 */
export class GitIngester {
  constructor(repositoryPath, config) {
    this.repositoryPath = repositoryPath;
    this.config = config;
    this.git = null;
    this.isInitialized = false;
  }

  /**
   * Initialize the Git ingester
   */
  async initialize() {
    try {
      logger.info('Initializing Git ingester', {
        repositoryPath: this.repositoryPath
      });

      // Check if path exists
      const stats = await fs.stat(this.repositoryPath);
      if (!stats.isDirectory()) {
        throw new Error(`Repository path is not a directory: ${this.repositoryPath}`);
      }

      // Initialize simple-git
      this.git = simpleGit(this.repositoryPath);

      // Verify it's a git repository
      const isRepo = await this.git.checkIsRepo();
      if (!isRepo) {
        throw new Error(`Path is not a Git repository: ${this.repositoryPath}`);
      }

      this.isInitialized = true;
      logger.info('Git ingester initialized successfully');

    } catch (error) {
      logger.error('Failed to initialize Git ingester', {
        repositoryPath: this.repositoryPath,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Get all files from the repository
   */
  async getFiles(filter = null) {
    if (!this.isInitialized) {
      throw new Error('Git ingester not initialized');
    }

    try {
      logger.info('Scanning repository for files');

      // Get all tracked files
      const files = await this.git.raw(['ls-files']);
      const fileList = files.trim().split('\n').filter(file => file.length > 0);

      logger.info('Found tracked files', { count: fileList.length });

      // Process each file
      const processedFiles = [];
      for (const filePath of fileList) {
        try {
          const fileInfo = await this.getFileInfo(filePath);
          
          // Apply filter if provided
          if (filter && !filter(fileInfo)) {
            continue;
          }

          // Check if file should be processed based on language support
          if (this.shouldProcessFile(fileInfo)) {
            processedFiles.push(fileInfo);
          }
        } catch (error) {
          logger.warn('Failed to process file info', {
            filePath,
            error: error.message
          });
        }
      }

      logger.info('Files ready for processing', {
        totalFiles: fileList.length,
        processableFiles: processedFiles.length
      });

      return processedFiles;

    } catch (error) {
      logger.error('Failed to get repository files', { error: error.message });
      throw error;
    }
  }

  /**
   * Get detailed information about a file
   */
  async getFileInfo(filePath) {
    const fullPath = path.join(this.repositoryPath, filePath);
    
    try {
      // Get file stats
      const stats = await fs.stat(fullPath);
      
      // Read file content
      const content = await fs.readFile(fullPath, 'utf-8');
      
      // Get file history
      const log = await this.git.log({ file: filePath, maxCount: 1 });
      const lastCommit = log.latest;

      // Determine language
      const language = this.detectLanguage(filePath);

      return {
        path: filePath,
        fullPath: fullPath,
        content: content,
        size: stats.size,
        language: language,
        lastModified: stats.mtime,
        created: stats.birthtime,
        lastCommit: lastCommit ? {
          hash: lastCommit.hash,
          date: lastCommit.date,
          author: lastCommit.author_name,
          message: lastCommit.message
        } : null,
        version: lastCommit ? lastCommit.hash : stats.mtime.toISOString()
      };

    } catch (error) {
      logger.error('Failed to get file info', {
        filePath,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Detect programming language from file extension
   */
  detectLanguage(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    const fileExtensions = this.config.fileExtensions || {};

    for (const [language, extensions] of Object.entries(fileExtensions)) {
      if (extensions.includes(ext)) {
        return language;
      }
    }

    // Fallback detection
    const extensionMap = {
      '.js': 'javascript',
      '.jsx': 'javascript',
      '.mjs': 'javascript',
      '.ts': 'typescript',
      '.tsx': 'typescript',
      '.py': 'python',
      '.pyx': 'python',
      '.java': 'java',
      '.go': 'go',
      '.rs': 'rust',
      '.c': 'c',
      '.cpp': 'cpp',
      '.h': 'c',
      '.hpp': 'cpp'
    };

    return extensionMap[ext] || 'unknown';
  }

  /**
   * Check if file should be processed
   */
  shouldProcessFile(fileInfo) {
    // Check file size limit
    const maxFileSize = this.config.maxFileSize || 1024 * 1024; // 1MB default
    if (fileInfo.size > maxFileSize) {
      logger.debug('File too large, skipping', {
        path: fileInfo.path,
        size: fileInfo.size,
        maxSize: maxFileSize
      });
      return false;
    }

    // Check if language is supported
    const supportedLanguages = this.config.supportedLanguages || [];
    if (!supportedLanguages.includes(fileInfo.language)) {
      logger.debug('Language not supported, skipping', {
        path: fileInfo.path,
        language: fileInfo.language
      });
      return false;
    }

    // Check exclude patterns
    const excludePatterns = this.config.excludePatterns || [];
    for (const pattern of excludePatterns) {
      if (this.matchesPattern(fileInfo.path, pattern)) {
        logger.debug('File matches exclude pattern, skipping', {
          path: fileInfo.path,
          pattern
        });
        return false;
      }
    }

    return true;
  }

  /**
   * Check if path matches a glob-like pattern
   */
  matchesPattern(filePath, pattern) {
    // Simple pattern matching (could be enhanced with a proper glob library)
    if (pattern.includes('**')) {
      const regex = pattern.replace(/\*\*/g, '.*').replace(/\*/g, '[^/]*');
      return new RegExp(regex).test(filePath);
    }
    
    if (pattern.includes('*')) {
      const regex = pattern.replace(/\*/g, '[^/]*');
      return new RegExp(regex).test(filePath);
    }
    
    return filePath.includes(pattern);
  }

  /**
   * Get repository information
   */
  async getRepositoryInfo() {
    if (!this.isInitialized) {
      throw new Error('Git ingester not initialized');
    }

    try {
      const [status, remotes, branches, log] = await Promise.all([
        this.git.status(),
        this.git.getRemotes(true),
        this.git.branch(),
        this.git.log({ maxCount: 10 })
      ]);

      return {
        path: this.repositoryPath,
        currentBranch: branches.current,
        branches: branches.all,
        remotes: remotes,
        status: {
          ahead: status.ahead,
          behind: status.behind,
          staged: status.staged,
          modified: status.modified,
          created: status.created,
          deleted: status.deleted
        },
        recentCommits: log.all.map(commit => ({
          hash: commit.hash,
          date: commit.date,
          author: commit.author_name,
          message: commit.message
        }))
      };

    } catch (error) {
      logger.error('Failed to get repository info', { error: error.message });
      throw error;
    }
  }

  /**
   * Watch for changes in the repository
   */
  async watchChanges(callback) {
    if (!this.isInitialized) {
      throw new Error('Git ingester not initialized');
    }

    // This is a basic implementation - in production, you might want to use
    // more sophisticated file watching or Git hooks
    logger.info('Starting repository change monitoring');

    const checkForChanges = async () => {
      try {
        const status = await this.git.status();
        
        if (status.modified.length > 0 || status.created.length > 0) {
          const changedFiles = [...status.modified, ...status.created];
          
          for (const filePath of changedFiles) {
            try {
              const fileInfo = await this.getFileInfo(filePath);
              if (this.shouldProcessFile(fileInfo)) {
                callback('file_changed', fileInfo);
              }
            } catch (error) {
              logger.warn('Failed to process changed file', {
                filePath,
                error: error.message
              });
            }
          }
        }
      } catch (error) {
        logger.error('Error checking for changes', { error: error.message });
      }
    };

    // Check for changes every 5 seconds
    const interval = setInterval(checkForChanges, 5000);
    
    return () => {
      clearInterval(interval);
      logger.info('Stopped repository change monitoring');
    };
  }
}

export default GitIngester;
