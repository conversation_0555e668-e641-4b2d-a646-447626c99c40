/**
 * Unit tests for logging system
 */

import { jest } from '@jest/globals';

// <PERSON><PERSON> winston to avoid file system operations in tests
const mockWinston = {
  createLogger: jest.fn(() => ({
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
    child: jest.fn(() => ({
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn()
    }))
  })),
  format: {
    combine: jest.fn(),
    timestamp: jest.fn(),
    errors: jest.fn(),
    json: jest.fn(),
    printf: jest.fn(),
    colorize: jest.fn()
  },
  transports: {
    File: jest.fn(),
    Console: jest.fn()
  }
};

jest.unstable_mockModule('winston', () => ({ default: mockWinston }));

describe('Logger', () => {
  let logger, createLogger, logPerformance, logRequest, logError;

  beforeAll(async () => {
    const loggerModule = await import('../../../src/utils/logger.js');
    logger = loggerModule.default;
    createLogger = loggerModule.createLogger;
    logPerformance = loggerModule.logPerformance;
    logRequest = loggerModule.logRequest;
    logError = loggerModule.logError;
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createLogger', () => {
    test('should create a child logger with service name', () => {
      const childLogger = createLogger('TestService');
      
      expect(mockWinston.createLogger().child).toHaveBeenCalledWith({
        service: 'TestService'
      });
    });

    test('should create a child logger with service name and context', () => {
      const context = { userId: '123', requestId: 'abc' };
      const childLogger = createLogger('TestService', context);
      
      expect(mockWinston.createLogger().child).toHaveBeenCalledWith({
        service: 'TestService',
        userId: '123',
        requestId: 'abc'
      });
    });
  });

  describe('logPerformance', () => {
    test('should log performance metrics', () => {
      const mockLogger = mockWinston.createLogger();
      
      logPerformance('test_operation', 150, { component: 'AST' });
      
      expect(mockLogger.info).toHaveBeenCalledWith('Performance metric', {
        operation: 'test_operation',
        duration: 150,
        unit: 'ms',
        component: 'AST'
      });
    });

    test('should log performance metrics without metadata', () => {
      const mockLogger = mockWinston.createLogger();
      
      logPerformance('test_operation', 100);
      
      expect(mockLogger.info).toHaveBeenCalledWith('Performance metric', {
        operation: 'test_operation',
        duration: 100,
        unit: 'ms'
      });
    });
  });

  describe('logRequest', () => {
    test('should log API request details', () => {
      const mockLogger = mockWinston.createLogger();
      const mockReq = {
        method: 'POST',
        url: '/api/context/query',
        get: jest.fn((header) => {
          if (header === 'User-Agent') return 'test-agent';
          return null;
        }),
        ip: '127.0.0.1'
      };
      const mockRes = {
        statusCode: 200
      };
      
      logRequest(mockReq, mockRes, 250);
      
      expect(mockLogger.info).toHaveBeenCalledWith('API Request', {
        method: 'POST',
        url: '/api/context/query',
        statusCode: 200,
        duration: 250,
        userAgent: 'test-agent',
        ip: '127.0.0.1'
      });
    });
  });

  describe('logError', () => {
    test('should log error with context', () => {
      const mockLogger = mockWinston.createLogger();
      const error = new Error('Test error');
      const context = { component: 'AST', file: 'test.js' };
      
      logError(error, context);
      
      expect(mockLogger.error).toHaveBeenCalledWith('Error occurred', {
        error: {
          message: 'Test error',
          stack: error.stack,
          name: 'Error'
        },
        component: 'AST',
        file: 'test.js'
      });
    });

    test('should log error without context', () => {
      const mockLogger = mockWinston.createLogger();
      const error = new Error('Test error');
      
      logError(error);
      
      expect(mockLogger.error).toHaveBeenCalledWith('Error occurred', {
        error: {
          message: 'Test error',
          stack: error.stack,
          name: 'Error'
        }
      });
    });
  });

  describe('specialized logging functions', () => {
    test('should log ingestion events', async () => {
      const { logIngestion } = await import('../../../src/utils/logger.js');
      const mockLogger = mockWinston.createLogger();
      const file = { path: 'test.js', size: 100, language: 'javascript' };
      
      logIngestion('file_processed', file, { duration: 50 });
      
      expect(mockLogger.info).toHaveBeenCalledWith('Ingestion event', {
        event: 'file_processed',
        file: {
          path: 'test.js',
          size: 100,
          language: 'javascript'
        },
        duration: 50
      });
    });

    test('should log graph operations', async () => {
      const { logGraph } = await import('../../../src/utils/logger.js');
      const mockLogger = mockWinston.createLogger();
      
      logGraph('create_nodes', 10, 5, 100);
      
      expect(mockLogger.info).toHaveBeenCalledWith('Graph operation', {
        operation: 'create_nodes',
        nodeCount: 10,
        relationshipCount: 5,
        duration: 100,
        unit: 'ms'
      });
    });

    test('should log retrieval operations', async () => {
      const { logRetrieval } = await import('../../../src/utils/logger.js');
      const mockLogger = mockWinston.createLogger();
      
      logRetrieval('find functions', 5, ['graph', 'vector'], 75);
      
      expect(mockLogger.info).toHaveBeenCalledWith('Context retrieval', {
        query: 'find functions',
        resultCount: 5,
        sources: ['graph', 'vector'],
        duration: 75,
        unit: 'ms'
      });
    });
  });
});
