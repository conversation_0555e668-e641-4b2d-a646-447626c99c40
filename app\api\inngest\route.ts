/**
 * Inngest API Route
 * Serves the Inngest functions and handles webhooks
 */

import { serve } from "inngest/next"
import { inngest } from "@/src/lib/inngest/client"
import { planningWorkflow, stepExecutor } from "@/src/lib/inngest/functions/planning-workflow"

// Export the Inngest serve handler
export const { GET, POST, PUT } = serve({
  client: inngest,
  functions: [
    planningWorkflow,
    stepExecutor,
  ],
  streaming: "allow", // Enable streaming for better performance
})
