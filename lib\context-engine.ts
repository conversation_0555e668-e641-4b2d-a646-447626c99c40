import type { ProjectContext } from "@/types/planning"
import type {
  <PERSON>textN<PERSON>,
  ContextualHint,
  CrossReference,
  ContextIntegrityReport,
  ContextIntegrityIssue
} from "@/types/context-engine"
import { CrossReferenceValidator } from "./cross-reference-validator"
import { ContextEnrichmentPipeline } from "./context-enrichment-pipeline"
import { UniversalMCPIntegration } from "./universal-mcp-integration"

/**
 * Agent Types supported by the context engine
 */
export type AgentType = 'planner' | 'executor' | 'workflow' | 'project-planner' | 'task-breakdown' | 'code-generator'

/**
 * Context scope for different agent operations
 */
export interface AgentContextScope {
  agentType: AgentType
  operationId: string
  parentContext?: string
  requiredCapabilities: string[]
  contextFilters: string[]
}

/**
 * Universal Context Engine for All ADE Agents
 * Implements bulletproof context management with full decision graph tracking
 * Supports: Project Planner, Task Planner, Executor, Workflow Engine, and future agents
 */
export class ContextEngine {
  private context: ProjectContext
  private memoryStore: Map<string, any> = new Map()
  private decisionGraph: Map<string, ContextNode> = new Map()
  private contextVersions: Map<string, ProjectContext> = new Map()
  private crossReferences: Map<string, string[]> = new Map()
  private contextIntegrity: ContextIntegrityTracker
  private crossReferenceValidator: CrossReferenceValidator
  private enrichmentPipeline: ContextEnrichmentPipeline
  private universalMCP: UniversalMCPIntegration
  private agentContexts: Map<string, AgentContextScope> = new Map()
  private sharedState: Map<string, any> = new Map()

  constructor(initialContext: Partial<ProjectContext>) {
    this.context = {
      originalPrompt: "",
      projectType: "",
      features: [],
      clarifications: {},
      summary: "",
      techStack: {},
      prd: {},
      wireframes: [],
      filesystem: {},
      workflow: {},
      tasks: [],
      ...initialContext,
    }

    this.contextIntegrity = new ContextIntegrityTracker()
    this.crossReferenceValidator = new CrossReferenceValidator()
    this.enrichmentPipeline = new ContextEnrichmentPipeline()
    this.universalMCP = new UniversalMCPIntegration()
    this.initializeDecisionGraph()
    this.initializeSharedState()
  }

  /**
   * Initialize shared state for cross-agent communication
   */
  private initializeSharedState(): void {
    this.sharedState.set('project_status', 'initializing')
    this.sharedState.set('active_agents', [])
    this.sharedState.set('agent_handoffs', [])
    this.sharedState.set('global_constraints', {})
    this.sharedState.set('execution_timeline', [])
  }

  /**
   * Register an agent with the context engine
   */
  registerAgent(agentType: AgentType, operationId: string, scope: Partial<AgentContextScope> = {}): string {
    const agentId = `${agentType}_${operationId}_${Date.now()}`

    const agentScope: AgentContextScope = {
      agentType,
      operationId,
      parentContext: scope.parentContext,
      requiredCapabilities: scope.requiredCapabilities || this.getDefaultCapabilities(agentType),
      contextFilters: scope.contextFilters || this.getDefaultFilters(agentType)
    }

    this.agentContexts.set(agentId, agentScope)

    // Update active agents list
    const activeAgents = this.sharedState.get('active_agents') || []
    activeAgents.push({ agentId, agentType, operationId, timestamp: new Date().toISOString() })
    this.sharedState.set('active_agents', activeAgents)

    return agentId
  }

  /**
   * Get context tailored for a specific agent type
   */
  getAgentContext(agentId: string): any {
    const agentScope = this.agentContexts.get(agentId)
    if (!agentScope) {
      throw new Error(`Agent ${agentId} not registered`)
    }

    const baseContext = this.getRelevantContext('all')
    const filteredContext = this.filterContextForAgent(baseContext, agentScope)

    return {
      ...filteredContext,
      agentMetadata: {
        agentId,
        agentType: agentScope.agentType,
        operationId: agentScope.operationId,
        capabilities: agentScope.requiredCapabilities,
        sharedState: this.getSharedStateForAgent(agentScope.agentType)
      }
    }
  }

  /**
   * Initialize the decision graph with all planning steps and their relationships
   */
  private initializeDecisionGraph(): void {
    const steps = [
      'analyze', 'clarify', 'summary', 'techstack', 'prd',
      'context-profile', 'wireframes', 'design', 'database',
      'filesystem', 'workflow', 'tasks', 'scaffold'
    ]

    steps.forEach(step => {
      this.decisionGraph.set(step, {
        id: step,
        dependencies: this.getStepDependencies(step),
        outputs: this.getStepOutputs(step),
        contextRequirements: this.getContextRequirements(step),
        validationRules: this.getValidationRules(step),
        crossReferences: [],
        timestamp: new Date().toISOString(),
        version: '1.0.0'
      })
    })
  }

  /**
   * Deep Context Propagation: Enhanced Memory Management
   * Maintains coherent context with full decision tracking and cross-references
   */
  updateContext(step: string, data: any): void {
    // Store previous version for rollback capability
    const versionKey = `${step}_v${Date.now()}`
    this.contextVersions.set(versionKey, { ...this.context })

    // Update main context
    this.context = { ...this.context, [step]: data }

    // Enhanced memory store with full context propagation
    const contextNode = this.decisionGraph.get(step)
    if (contextNode) {
      const enrichedData = this.enrichContextData(step, data, contextNode)

      this.memoryStore.set(`step_${step}`, {
        data: enrichedData,
        originalData: data,
        timestamp: new Date().toISOString(),
        dependencies: contextNode.dependencies,
        upstreamContext: this.gatherUpstreamContext(contextNode.dependencies),
        crossReferences: this.updateCrossReferences(step, enrichedData),
        integrityHash: this.contextIntegrity.generateHash(enrichedData),
        version: versionKey
      })

      // Update decision graph node
      contextNode.timestamp = new Date().toISOString()
      contextNode.crossReferences = this.findCrossReferences(step, enrichedData)
    }

    // Validate context integrity after update
    this.validateContextIntegrity(step)
  }

  /**
   * Enrich context data with all relevant upstream decisions
   */
  private enrichContextData(step: string, data: any, contextNode: ContextNode): any {
    const upstreamContext = this.gatherUpstreamContext(contextNode.dependencies)

    return {
      ...data,
      _contextMeta: {
        step,
        timestamp: new Date().toISOString(),
        upstreamDecisions: upstreamContext,
        derivedFrom: contextNode.dependencies,
        influences: this.getDownstreamSteps(step),
        contextualHints: this.generateContextualHints(step, data, upstreamContext)
      }
    }
  }

  /**
   * Context Engineering Technique: Dependency Tracking
   * Ensures each step has access to relevant prior decisions
   */
  private getStepDependencies(step: string): string[] {
    const dependencies: Record<string, string[]> = {
      analyze: [],
      clarify: ["analyze"],
      summary: ["analyze", "clarify"],
      techstack: ["analyze", "clarify", "summary"],
      prd: ["analyze", "clarify", "summary", "techstack"],
      wireframes: ["summary", "prd"],
      filesystem: ["techstack", "prd"],
      workflow: ["analyze", "summary", "prd"],
      tasks: ["prd", "wireframes", "filesystem", "workflow"],
    }
    return dependencies[step] || []
  }

  /**
   * Enhanced Context Engineering: RAG + Enrichment Pipeline
   * Retrieves relevant external information and enriches with industry standards
   */
  async enhanceWithRAG(step: string, query: string): Promise<any> {
    // Get base context
    const baseContext = this.getRelevantContext(step)

    // Apply context enrichment pipeline
    const enrichedContext = await this.enrichmentPipeline.enrichContext(this.context, step)

    // Real RAG integration would go here - for now, return structured context
    // This could integrate with vector databases, documentation, etc.
    const ragKnowledge = {
      step,
      query,
      timestamp: new Date().toISOString(),
      note: "Real RAG integration would provide external knowledge here",
      vectorSimilarity: 0.85, // Placeholder for actual RAG similarity scores
      retrievedDocuments: [] // Placeholder for actual retrieved documents
    }

    return {
      context: baseContext,
      enrichedContext: enrichedContext.enhancedContext,
      enrichments: enrichedContext.enrichments,
      enrichmentConfidence: enrichedContext.confidence,
      enrichmentRecommendations: enrichedContext.recommendations,
      externalKnowledge: ragKnowledge,
      bestPractices: this.getBestPractices(step),
      templates: this.getTemplates(step),
      contextualHints: this.generateContextualHints(step, this.context, baseContext.dependencies)
    }
  }

  /**
   * Context Engineering Technique: Template Management
   * Provides consistent structures for document generation
   */
  private getTemplates(step: string): any {
    const templates: Record<string, any> = {
      prd: {
        sections: ["Purpose", "Features", "User Stories", "Technical Requirements", "Acceptance Criteria"],
        format: "structured_document",
      },
      summary: {
        sections: ["Overview", "Scope", "Goals", "Key Features"],
        format: "narrative",
      },
      tasks: {
        structure: ["Setup", "Frontend", "Backend", "Testing", "Deployment"],
        format: "categorized_list",
      },
    }
    return templates[step] || {}
  }

  /**
   * Context Engineering Technique: Contextual Retrieval
   * Gets relevant context for current planning step
   */
  getRelevantContext(step: string): any {
    const dependencies = this.getStepDependencies(step)
    const relevantContext: any = {}

    dependencies.forEach((dep) => {
      const stepData = this.memoryStore.get(`step_${dep}`)
      if (stepData) {
        relevantContext[dep] = stepData.data
      }
    })

    return {
      current: this.context,
      dependencies: relevantContext,
      metadata: {
        projectType: this.context.projectType,
        complexity: this.assessComplexity(),
        timestamp: new Date().toISOString(),
      },
    }
  }

  /**
   * Context Engineering Technique: Complexity Assessment
   * Dynamically assesses project complexity for better planning
   */
  private assessComplexity(): string {
    const factors = {
      features: this.context.features?.length || 0,
      integrations: Object.keys(this.context.clarifications || {}).length,
      techStackComplexity: Object.keys(this.context.techStack || {}).length,
    }

    const score = factors.features * 2 + factors.integrations + factors.techStackComplexity

    if (score < 5) return "Simple"
    if (score < 15) return "Medium"
    return "Complex"
  }

  /**
   * Context Engineering Technique: Best Practices Integration
   * Provides domain-specific best practices for each step
   */
  private getBestPractices(step: string): string[] {
    const practices: Record<string, string[]> = {
      techstack: [
        "Choose technologies with strong community support",
        "Consider long-term maintenance and scalability",
        "Align with team expertise and project timeline",
      ],
      prd: [
        "Write clear, testable acceptance criteria",
        "Include both functional and non-functional requirements",
        "Consider edge cases and error scenarios",
      ],
      filesystem: [
        "Follow framework conventions and best practices",
        "Separate concerns with clear folder structure",
        "Plan for scalability and maintainability",
      ],
    }
    return practices[step] || []
  }

  /**
   * Context Engineering Technique: Validation and Security
   * Validates context integrity and prevents injection attacks
   */
  validateContext(): { isValid: boolean; issues: string[] } {
    const issues: string[] = []

    // Validate required fields
    if (!this.context.originalPrompt?.trim()) {
      issues.push("Original prompt is required")
    }

    // Validate data integrity
    if (this.context.features && !Array.isArray(this.context.features)) {
      issues.push("Features must be an array")
    }

    // Security validation - prevent prompt injection
    const suspiciousPatterns = [/ignore\s+previous\s+instructions/i, /system\s*:/i, /\[INST\]/i]

    const textToCheck = JSON.stringify(this.context)
    for (const pattern of suspiciousPatterns) {
      if (pattern.test(textToCheck)) {
        issues.push("Potentially malicious content detected")
        break
      }
    }

    return {
      isValid: issues.length === 0,
      issues,
    }
  }

  /**
   * Context Engineering Technique: Context Serialization
   * Exports complete context for external systems
   */
  exportContext(): {
    context: ProjectContext
    metadata: any
    validation: any
  } {
    return {
      context: this.context,
      metadata: {
        complexity: this.assessComplexity(),
        completeness: this.calculateCompleteness(),
        timestamp: new Date().toISOString(),
        version: "1.0",
      },
      validation: this.validateContext(),
    }
  }

  /**
   * Context Engineering Technique: Completeness Assessment
   * Measures how complete the planning context is
   */
  private calculateCompleteness(): number {
    const requiredFields = ["originalPrompt", "projectType", "features", "summary", "techStack", "prd"]

    const completedFields = requiredFields.filter((field) => {
      const value = this.context[field as keyof ProjectContext]
      return value && (typeof value === "string" ? value.trim() : Object.keys(value).length > 0)
    })

    return Math.round((completedFields.length / requiredFields.length) * 100)
  }

  /**
   * Get step outputs - what this step produces
   */
  private getStepOutputs(step: string): string[] {
    const outputs: Record<string, string[]> = {
      analyze: ['projectType', 'features', 'complexity', 'domain'],
      clarify: ['clarifications', 'userPreferences', 'constraints'],
      summary: ['projectSummary', 'scope', 'objectives'],
      techstack: ['frontend', 'backend', 'database', 'hosting'],
      prd: ['requirements', 'userStories', 'acceptanceCriteria'],
      wireframes: ['uiComponents', 'layouts', 'userFlows'],
      filesystem: ['folderStructure', 'fileOrganization', 'conventions'],
      workflow: ['businessLogic', 'dataFlow', 'integrations'],
      tasks: ['implementationTasks', 'timeline', 'dependencies']
    }
    return outputs[step] || []
  }

  /**
   * Get context requirements for a step
   */
  private getContextRequirements(step: string): string[] {
    const requirements: Record<string, string[]> = {
      analyze: ['originalPrompt'],
      clarify: ['projectType', 'features'],
      summary: ['projectType', 'features', 'clarifications'],
      techstack: ['projectType', 'complexity', 'clarifications'],
      prd: ['summary', 'techstack', 'clarifications'],
      wireframes: ['prd', 'techstack'],
      filesystem: ['techstack', 'prd'],
      workflow: ['prd', 'techstack'],
      tasks: ['prd', 'wireframes', 'filesystem', 'workflow']
    }
    return requirements[step] || []
  }

  /**
   * Get validation rules for a step
   */
  private getValidationRules(step: string): string[] {
    const rules: Record<string, string[]> = {
      analyze: ['projectType_required', 'features_not_empty'],
      clarify: ['clarifications_complete'],
      summary: ['summary_coherent_with_analysis'],
      techstack: ['stack_compatible_with_requirements'],
      prd: ['requirements_traceable_to_features'],
      wireframes: ['wireframes_match_requirements'],
      filesystem: ['structure_follows_conventions'],
      workflow: ['workflow_supports_requirements'],
      tasks: ['tasks_cover_all_requirements']
    }
    return rules[step] || []
  }

  /**
   * Gather all upstream context for dependencies
   */
  private gatherUpstreamContext(dependencies: string[]): Record<string, any> {
    const upstreamContext: Record<string, any> = {}

    dependencies.forEach(dep => {
      const stepData = this.memoryStore.get(`step_${dep}`)
      if (stepData) {
        upstreamContext[dep] = {
          data: stepData.data,
          timestamp: stepData.timestamp,
          crossReferences: stepData.crossReferences
        }
      }
    })

    return upstreamContext
  }

  /**
   * Get downstream steps that depend on this step
   */
  private getDownstreamSteps(step: string): string[] {
    const downstream: string[] = []

    this.decisionGraph.forEach((node, nodeStep) => {
      if (node.dependencies.includes(step)) {
        downstream.push(nodeStep)
      }
    })

    return downstream
  }

  /**
   * Generate contextual hints for better decision making
   */
  private generateContextualHints(step: string, data: any, upstreamContext: Record<string, any>): ContextualHint[] {
    const hints: ContextualHint[] = []

    // Add alignment hints
    if (step === 'techstack' && upstreamContext.analyze) {
      const complexity = upstreamContext.analyze.data.complexity
      if (complexity === 'Simple' && data.backend === 'Microservices') {
        hints.push({
          type: 'alignment',
          source: 'analyze',
          target: 'techstack',
          message: 'Simple projects typically don\'t need microservices architecture',
          severity: 'warning',
          autoFix: 'Consider monolithic architecture for simple projects'
        })
      }
    }

    return hints
  }

  /**
   * Update cross-references between steps
   */
  private updateCrossReferences(step: string, data: any): string[] {
    const refs: string[] = []

    // Find references to other steps in the data
    const dataStr = JSON.stringify(data)
    this.decisionGraph.forEach((_, otherStep) => {
      if (otherStep !== step && dataStr.includes(otherStep)) {
        refs.push(otherStep)
      }
    })

    return refs
  }

  /**
   * Find cross-references in data
   */
  private findCrossReferences(step: string, data: any): string[] {
    // Implementation for finding cross-references
    return this.updateCrossReferences(step, data)
  }

  /**
   * Validate context integrity after updates
   */
  private validateContextIntegrity(step: string): void {
    const report = this.contextIntegrity.validateStep(step, this.context, this.decisionGraph)

    if (!report.isValid) {
      console.warn(`Context integrity issues in step ${step}:`, report.issues)
    }
  }

  /**
   * Get default capabilities for each agent type
   */
  private getDefaultCapabilities(agentType: AgentType): string[] {
    const capabilities: Record<AgentType, string[]> = {
      'project-planner': ['requirements_analysis', 'tech_stack_selection', 'architecture_design', 'cross_validation'],
      'planner': ['task_breakdown', 'dependency_analysis', 'timeline_estimation', 'resource_allocation'],
      'executor': ['code_generation', 'file_operations', 'tool_execution', 'error_handling'],
      'workflow': ['flow_orchestration', 'agent_coordination', 'state_management', 'handoff_management'],
      'task-breakdown': ['task_decomposition', 'priority_assignment', 'skill_mapping', 'estimation'],
      'code-generator': ['code_synthesis', 'pattern_application', 'testing_integration', 'documentation']
    }
    return capabilities[agentType] || []
  }

  /**
   * Get default context filters for each agent type
   */
  private getDefaultFilters(agentType: AgentType): string[] {
    const filters: Record<AgentType, string[]> = {
      'project-planner': ['all'], // Project planner needs full context
      'planner': ['requirements', 'architecture', 'constraints', 'timeline'],
      'executor': ['implementation_details', 'file_structure', 'dependencies', 'current_task'],
      'workflow': ['agent_states', 'handoff_points', 'execution_flow', 'error_recovery'],
      'task-breakdown': ['requirements', 'architecture', 'complexity', 'resources'],
      'code-generator': ['implementation_specs', 'patterns', 'conventions', 'testing_requirements']
    }
    return filters[agentType] || ['basic']
  }

  /**
   * Filter context based on agent scope and capabilities
   */
  private filterContextForAgent(context: any, scope: AgentContextScope): any {
    if (scope.contextFilters.includes('all')) {
      return context // Project planner gets everything
    }

    const filteredContext: any = {
      current: {},
      dependencies: {},
      metadata: context.metadata
    }

    // Apply filters based on agent type
    scope.contextFilters.forEach(filter => {
      switch (filter) {
        case 'requirements':
          filteredContext.current.prd = context.current.prd
          filteredContext.current.features = context.current.features
          filteredContext.current.clarifications = context.current.clarifications
          break
        case 'architecture':
          filteredContext.current.techStack = context.current.techStack
          filteredContext.current.filesystem = context.current.filesystem
          filteredContext.current.database = context.current.database
          break
        case 'implementation_details':
          filteredContext.current.tasks = context.current.tasks
          filteredContext.current.workflow = context.current.workflow
          filteredContext.current.filesystem = context.current.filesystem
          break
        case 'constraints':
          filteredContext.constraints = this.sharedState.get('global_constraints')
          break
        case 'timeline':
          filteredContext.timeline = this.sharedState.get('execution_timeline')
          break
        case 'agent_states':
          filteredContext.agentStates = this.sharedState.get('active_agents')
          break
        case 'basic':
          filteredContext.current.originalPrompt = context.current.originalPrompt
          filteredContext.current.projectType = context.current.projectType
          filteredContext.current.summary = context.current.summary
          break
      }
    })

    return filteredContext
  }

  /**
   * Get shared state relevant to specific agent type
   */
  private getSharedStateForAgent(agentType: AgentType): any {
    const sharedState: any = {}

    switch (agentType) {
      case 'project-planner':
        // Project planner gets full shared state
        this.sharedState.forEach((value, key) => {
          sharedState[key] = value
        })
        break
      case 'planner':
        sharedState.project_status = this.sharedState.get('project_status')
        sharedState.execution_timeline = this.sharedState.get('execution_timeline')
        break
      case 'executor':
        sharedState.active_agents = this.sharedState.get('active_agents')
        sharedState.current_task = this.sharedState.get('current_task')
        break
      case 'workflow':
        sharedState.agent_handoffs = this.sharedState.get('agent_handoffs')
        sharedState.active_agents = this.sharedState.get('active_agents')
        sharedState.execution_timeline = this.sharedState.get('execution_timeline')
        break
    }

    return sharedState
  }

  /**
   * Update shared state (used by workflow engine and other orchestrating agents)
   */
  updateSharedState(key: string, value: any, agentId?: string): void {
    this.sharedState.set(key, value)

    // Log the update for audit trail
    const updates = this.sharedState.get('state_updates') || []
    updates.push({
      key,
      value,
      agentId,
      timestamp: new Date().toISOString()
    })
    this.sharedState.set('state_updates', updates)
  }

  /**
   * Cross-reference validation for project planner
   */
  async validateCrossReferences(): Promise<any> {
    return await this.crossReferenceValidator.validateCrossReferences(this.context)
  }

  /**
   * Get enriched context for a specific agent and step
   */
  async getEnrichedAgentContext(agentId: string, step: string): Promise<any> {
    const agentScope = this.agentContexts.get(agentId)
    if (!agentScope) {
      throw new Error(`Agent ${agentId} not registered`)
    }

    // Get enhanced context with enrichment pipeline
    const enhancedContext = await this.enhanceWithRAG(step, this.context.originalPrompt)

    // Filter for agent-specific needs
    const filteredContext = this.filterContextForAgent(enhancedContext.context, agentScope)

    return {
      ...filteredContext,
      enrichments: enhancedContext.enrichments.filter(e =>
        e.applicableSteps.includes(step) || e.applicableSteps.includes('all')
      ),
      enrichmentConfidence: enhancedContext.enrichmentConfidence,
      recommendations: enhancedContext.enrichmentRecommendations,
      agentMetadata: {
        agentId,
        agentType: agentScope.agentType,
        operationId: agentScope.operationId,
        capabilities: agentScope.requiredCapabilities,
        sharedState: this.getSharedStateForAgent(agentScope.agentType)
      }
    }
  }

  /**
   * Unregister agent when operation completes
   */
  unregisterAgent(agentId: string): void {
    this.agentContexts.delete(agentId)

    const activeAgents = this.sharedState.get('active_agents') || []
    const updatedAgents = activeAgents.filter((agent: any) => agent.agentId !== agentId)
    this.sharedState.set('active_agents', updatedAgents)
  }

  /**
   * Get real-time documentation using Context7 MCP
   */
  async getDocumentation(libraryName: string, topic?: string): Promise<any> {
    try {
      return await this.universalMCP.getDocumentation(libraryName, topic)
    } catch (error) {
      console.warn('Context7 documentation lookup failed:', error)
      return null
    }
  }

  /**
   * Perform sequential thinking using MCP
   */
  async performSequentialThinking(thought: string, context: any = {}): Promise<any> {
    try {
      return await this.universalMCP.think(thought, context)
    } catch (error) {
      console.warn('Sequential thinking failed:', error)
      return null
    }
  }

  /**
   * Search documentation across multiple libraries
   */
  async searchDocumentation(query: string, libraries?: string[]): Promise<any> {
    try {
      return await this.universalMCP.executeTool('context7-docs', 'search_documentation', {
        query,
        libraries,
        maxResults: 10
      })
    } catch (error) {
      console.warn('Documentation search failed:', error)
      return null
    }
  }

  /**
   * Get MCP configuration for external use
   */
  getMCPConfiguration(): any {
    return this.universalMCP.getMCPConfiguration()
  }

  /**
   * Test all MCP connections
   */
  async testMCPConnections(): Promise<any> {
    return await this.universalMCP.testConnections()
  }

  /**
   * Get available MCP tools for all agents
   */
  getAvailableMCPTools(): any {
    return this.universalMCP.getAvailableTools()
  }

  // Removed mock RAG retrieval - real RAG integration would go in enhanceWithRAG method
}

/**
 * Context Integrity Tracker
 * Ensures context consistency and validates cross-references
 */
export class ContextIntegrityTracker {
  /**
   * Generate hash for data integrity checking
   */
  generateHash(data: any): string {
    const str = JSON.stringify(data, Object.keys(data).sort())
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return hash.toString(16)
  }

  /**
   * Validate a specific step's context integrity
   */
  validateStep(step: string, context: ProjectContext, decisionGraph: Map<string, ContextNode>): ContextIntegrityReport {
    const issues: ContextIntegrityIssue[] = []
    const node = decisionGraph.get(step)

    if (!node) {
      issues.push({
        type: 'validation_failure',
        step,
        severity: 'critical',
        message: `Step ${step} not found in decision graph`
      })
      return { isValid: false, issues, score: 0, recommendations: [] }
    }

    // Check dependencies
    node.dependencies.forEach(dep => {
      if (!context[dep as keyof ProjectContext]) {
        issues.push({
          type: 'missing_dependency',
          step,
          field: dep,
          severity: 'high',
          message: `Missing required dependency: ${dep}`,
          suggestedFix: `Ensure ${dep} step is completed before ${step}`
        })
      }
    })

    // Check for circular references
    if (this.hasCircularReference(step, node, decisionGraph)) {
      issues.push({
        type: 'circular_reference',
        step,
        severity: 'critical',
        message: `Circular reference detected in step ${step}`
      })
    }

    // Validate data consistency
    this.validateDataConsistency(step, context, issues)

    const score = Math.max(0, 100 - (issues.length * 20))
    const recommendations = this.generateRecommendations(issues)

    return {
      isValid: issues.length === 0,
      issues,
      score,
      recommendations
    }
  }

  /**
   * Check for circular references in dependencies
   */
  private hasCircularReference(step: string, node: ContextNode, decisionGraph: Map<string, ContextNode>, visited: Set<string> = new Set()): boolean {
    if (visited.has(step)) {
      return true
    }

    visited.add(step)

    for (const dep of node.dependencies) {
      const depNode = decisionGraph.get(dep)
      if (depNode && this.hasCircularReference(dep, depNode, decisionGraph, new Set(visited))) {
        return true
      }
    }

    return false
  }

  /**
   * Validate data consistency between related steps
   */
  private validateDataConsistency(step: string, context: ProjectContext, issues: ContextIntegrityIssue[]): void {
    // Example: Check if tech stack aligns with project type
    if (step === 'techstack' && context.projectType && context.techStack) {
      const projectType = context.projectType.toLowerCase()
      const techStack = context.techStack as any

      if (projectType.includes('mobile') && !techStack.frontend?.includes('React Native') && !techStack.frontend?.includes('Flutter')) {
        issues.push({
          type: 'data_mismatch',
          step,
          field: 'frontend',
          severity: 'medium',
          message: 'Mobile project should use mobile-appropriate frontend technology',
          suggestedFix: 'Consider React Native, Flutter, or native development'
        })
      }
    }

    // Example: Check if wireframes match PRD requirements
    if (step === 'wireframes' && context.prd && context.wireframes) {
      // Add specific validation logic here
    }
  }

  /**
   * Generate recommendations based on issues
   */
  private generateRecommendations(issues: ContextIntegrityIssue[]): string[] {
    const recommendations: string[] = []

    const criticalIssues = issues.filter(i => i.severity === 'critical')
    const highIssues = issues.filter(i => i.severity === 'high')

    if (criticalIssues.length > 0) {
      recommendations.push('Address critical issues immediately to prevent planning failures')
    }

    if (highIssues.length > 0) {
      recommendations.push('Resolve high-priority issues to ensure plan quality')
    }

    if (issues.some(i => i.type === 'missing_dependency')) {
      recommendations.push('Complete all prerequisite steps before proceeding')
    }

    if (issues.some(i => i.type === 'data_mismatch')) {
      recommendations.push('Review and align conflicting specifications')
    }

    return recommendations
  }
}
