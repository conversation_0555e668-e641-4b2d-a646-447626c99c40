"use client"

import React from "react"
import { Brain, Zap, Bot, Code, Database, MessageSquare, Search, FileText, Settings, Workflow } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

interface ProjectSuggestion {
  id: string
  title: string
  description: string
  icon: React.ReactNode
  category: "web-app" | "mobile" | "api" | "dashboard"
  complexity: "simple" | "medium" | "advanced"
  estimatedTime: string
  tools: string[]
  prompt: string
}

const projectSuggestions: ProjectSuggestion[] = [
  {
    id: "task-manager",
    title: "Task Management App",
    description: "Build a modern task management application with real-time collaboration and project tracking",
    icon: <Code className="w-5 h-5" />,
    category: "web-app",
    complexity: "medium",
    estimatedTime: "15 min",
    tools: ["React", "TypeScript", "Tailwind CSS", "Supabase"],
    prompt: "Create a task management web application with user authentication, real-time updates, and project collaboration features"
  },
  {
    id: "analytics-dashboard",
    title: "Analytics Dashboard",
    description: "Build an interactive dashboard with charts, metrics, and real-time data visualization",
    icon: <Database className="w-5 h-5" />,
    category: "dashboard",
    complexity: "advanced",
    estimatedTime: "20 min",
    tools: ["Next.js", "Chart.js", "PostgreSQL", "Redis"],
    prompt: "Build an analytics dashboard with interactive charts, real-time metrics, and data filtering capabilities"
  },
  {
    id: "chat-app",
    title: "Real-time Chat App",
    description: "Create a modern chat application with real-time messaging, file sharing, and user presence",
    icon: <MessageSquare className="w-5 h-5" />,
    category: "web-app",
    complexity: "medium",
    estimatedTime: "18 min",
    tools: ["React", "Socket.io", "Node.js", "MongoDB"],
    prompt: "Create a real-time chat application with user authentication, message history, and file sharing capabilities"
  },
  {
    id: "ecommerce-api",
    title: "E-commerce API",
    description: "Build a RESTful API for e-commerce with products, orders, payments, and inventory management",
    icon: <Search className="w-5 h-5" />,
    category: "api",
    complexity: "advanced",
    estimatedTime: "25 min",
    tools: ["Node.js", "Express", "PostgreSQL", "Stripe"],
    prompt: "Build a comprehensive e-commerce API with product catalog, order management, payment processing, and inventory tracking"
  },
  {
    id: "portfolio-website",
    title: "Portfolio Website",
    description: "Create a modern, responsive portfolio website with animations and contact forms",
    icon: <Workflow className="w-5 h-5" />,
    category: "web-app",
    complexity: "simple",
    estimatedTime: "10 min",
    tools: ["Next.js", "Tailwind CSS", "Framer Motion", "Vercel"],
    prompt: "Create a modern portfolio website with smooth animations, responsive design, and contact form integration"
  },
  {
    id: "blog-platform",
    title: "Blog Platform",
    description: "Build a full-featured blog platform with CMS, SEO optimization, and comment system",
    icon: <FileText className="w-5 h-5" />,
    category: "web-app",
    complexity: "medium",
    estimatedTime: "22 min",
    tools: ["Next.js", "MDX", "Prisma", "NextAuth"],
    prompt: "Build a blog platform with content management, SEO optimization, user authentication, and commenting system"
  }
]

interface ProjectSuggestionsProps {
  onSelectSuggestion: (prompt: string) => void
  className?: string
}

export default function ProjectSuggestions({ onSelectSuggestion, className = "" }: ProjectSuggestionsProps) {
  const getCategoryColor = (category: string) => {
    switch (category) {
      case "web-app":
        return "bg-blue-500/10 text-blue-400 border-blue-500/20"
      case "mobile":
        return "bg-green-500/10 text-green-400 border-green-500/20"
      case "api":
        return "bg-purple-500/10 text-purple-400 border-purple-500/20"
      case "dashboard":
        return "bg-orange-500/10 text-orange-400 border-orange-500/20"
      default:
        return "bg-gray-500/10 text-gray-400 border-gray-500/20"
    }
  }

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case "simple":
        return "bg-green-500/10 text-green-400"
      case "medium":
        return "bg-yellow-500/10 text-yellow-400"
      case "advanced":
        return "bg-red-500/10 text-red-400"
      default:
        return "bg-gray-500/10 text-gray-400"
    }
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center gap-3 mb-6">
        <Brain className="w-6 h-6 text-blue-400" />
        <h3 className="text-lg font-semibold text-white">Project Templates</h3>
        <Badge variant="secondary" className="bg-[#1a1a1a] text-gray-300">
          Full-Stack Development
        </Badge>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {projectSuggestions.map((project) => (
          <div
            key={project.id}
            className="bg-[#111111] rounded-xl border border-[#1a1a1a] p-5 hover:border-[#2a2a2a] transition-all duration-200 group cursor-pointer"
            onClick={() => onSelectSuggestion(project.prompt)}
          >
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0 w-10 h-10 bg-[#1a1a1a] rounded-lg flex items-center justify-center text-blue-400 group-hover:bg-blue-500/10 transition-colors">
                {project.icon}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-2">
                  <h4 className="font-medium text-white group-hover:text-blue-400 transition-colors">
                    {project.title}
                  </h4>
                  <Badge
                    variant="outline"
                    className={`text-xs ${getCategoryColor(project.category)}`}
                  >
                    {project.category}
                  </Badge>
                </div>

                <p className="text-sm text-gray-400 mb-3 line-clamp-2">
                  {project.description}
                </p>

                <div className="flex items-center gap-3 mb-3">
                  <Badge
                    variant="secondary"
                    className={`text-xs ${getComplexityColor(project.complexity)}`}
                  >
                    {project.complexity}
                  </Badge>
                  <span className="text-xs text-gray-500">
                    ~{project.estimatedTime}
                  </span>
                </div>

                <div className="flex flex-wrap gap-1 mb-4">
                  {project.tools.slice(0, 3).map((tool) => (
                    <Badge
                      key={tool}
                      variant="outline"
                      className="text-xs bg-[#0a0a0a] border-gray-700 text-gray-400"
                    >
                      {tool}
                    </Badge>
                  ))}
                  {project.tools.length > 3 && (
                    <Badge
                      variant="outline"
                      className="text-xs bg-[#0a0a0a] border-gray-700 text-gray-400"
                    >
                      +{project.tools.length - 3}
                    </Badge>
                  )}
                </div>

                <Button
                  size="sm"
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white text-xs"
                  onClick={(e) => {
                    e.stopPropagation()
                    onSelectSuggestion(project.prompt)
                  }}
                >
                  <Zap className="w-3 h-3 mr-2" />
                  Build Project
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-8 p-4 bg-[#111111] rounded-xl border border-[#1a1a1a]">
        <div className="flex items-center gap-3 mb-3">
          <Bot className="w-5 h-5 text-green-400" />
          <h4 className="font-medium text-white">Custom Project</h4>
        </div>
        <p className="text-sm text-gray-400 mb-4">
          Describe your own project idea and we'll help you build it
        </p>
        <Button
          variant="outline"
          className="w-full border-gray-600 text-gray-300 hover:bg-[#1a1a1a] hover:text-white"
          onClick={() => onSelectSuggestion("Create a custom web application that")}
        >
          <Settings className="w-4 h-4 mr-2" />
          Build Custom Project
        </Button>
      </div>
    </div>
  )
}
