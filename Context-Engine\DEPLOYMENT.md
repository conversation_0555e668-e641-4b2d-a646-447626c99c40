# Advanced Code Context Engine - Deployment Guide

This guide provides comprehensive instructions for deploying the Advanced Code Context Engine in various environments.

## 📋 Prerequisites

### System Requirements
- **CPU**: 4+ cores recommended
- **RAM**: 8GB minimum, 16GB recommended
- **Storage**: 50GB+ available space
- **Network**: Stable internet connection for dependencies

### Software Requirements
- **Node.js**: 18.x or higher
- **Docker**: 20.x or higher
- **Docker Compose**: 2.x or higher
- **Git**: Latest version

## 🚀 Quick Start Deployment

### 1. <PERSON>lone and Setup
```bash
# Clone the repository
git clone <repository-url>
cd advanced-code-context-engine

# Copy environment configuration
cp .env.example .env

# Edit configuration (see Configuration section below)
nano .env
```

### 2. Docker Deployment (Recommended)
```bash
# Start all services
docker-compose up -d

# Check service status
docker-compose ps

# View logs
docker-compose logs -f context-engine
```

### 3. Verify Deployment
```bash
# Health check
curl http://localhost:3000/api/health

# Detailed health check
curl http://localhost:3000/api/health/detailed
```

## ⚙️ Configuration

### Environment Variables

#### Required Configuration
```bash
# Neo4j Database
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_secure_password
NEO4J_DATABASE=neo4j

# API Security
API_KEY_SECRET=your_32_character_secret_key_here

# Redis Cache
REDIS_URL=redis://localhost:6379
```

#### Optional Configuration
```bash
# Application
NODE_ENV=production
PORT=3000
LOG_LEVEL=info

# Vector Database (Future)
PINECONE_API_KEY=your_pinecone_api_key
PINECONE_ENVIRONMENT=us-west1-gcp-free
PINECONE_INDEX_NAME=code-context-embeddings

# Processing
MAX_CONCURRENT_PROCESSES=5
BATCH_SIZE=100

# Monitoring
METRICS_ENABLED=true
HEALTH_CHECK_INTERVAL=30000
```

### Database Configuration

#### Neo4j Setup
```bash
# Using Docker (included in docker-compose.yml)
docker-compose up -d neo4j

# Manual installation
# 1. Download Neo4j Community Edition
# 2. Configure authentication
# 3. Install APOC plugin
# 4. Start Neo4j service
```

#### Redis Setup
```bash
# Using Docker (included in docker-compose.yml)
docker-compose up -d redis

# Manual installation
# 1. Install Redis server
# 2. Configure persistence
# 3. Start Redis service
```

## 🏗️ Production Deployment

### 1. Production Environment Setup

#### Docker Compose Production
```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  context-engine:
    build: .
    environment:
      - NODE_ENV=production
      - LOG_LEVEL=warn
    restart: always
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
```

#### Load Balancer Configuration (Nginx)
```nginx
# nginx.conf
upstream context_engine {
    server context-engine-1:3000;
    server context-engine-2:3000;
    server context-engine-3:3000;
}

server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://context_engine;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 2. Security Hardening

#### SSL/TLS Configuration
```bash
# Generate SSL certificates
certbot --nginx -d your-domain.com

# Update nginx configuration for HTTPS
# Redirect HTTP to HTTPS
# Configure SSL parameters
```

#### Firewall Configuration
```bash
# Allow only necessary ports
ufw allow 22    # SSH
ufw allow 80    # HTTP
ufw allow 443   # HTTPS
ufw deny 3000   # Block direct access to app
ufw deny 7474   # Block direct access to Neo4j HTTP
ufw deny 7687   # Block direct access to Neo4j Bolt
```

#### Environment Security
```bash
# Secure environment file
chmod 600 .env
chown root:root .env

# Use Docker secrets for sensitive data
echo "your_secret_password" | docker secret create neo4j_password -
```

### 3. Monitoring and Logging

#### Prometheus Configuration
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'context-engine'
    static_configs:
      - targets: ['context-engine:3000']
    metrics_path: '/api/health/metrics'
```

#### Log Management
```bash
# Configure log rotation
cat > /etc/logrotate.d/context-engine << EOF
/app/logs/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 contextengine contextengine
}
EOF
```

## 🔧 Scaling and Performance

### Horizontal Scaling

#### Multiple Application Instances
```bash
# Scale application containers
docker-compose up -d --scale context-engine=3

# Use load balancer to distribute requests
# Configure session affinity if needed
```

#### Database Scaling
```bash
# Neo4j Clustering (Enterprise)
# Configure cluster members
# Set up read replicas
# Configure load balancing

# Redis Clustering
# Configure Redis cluster
# Set up master-slave replication
```

### Performance Optimization

#### Application Tuning
```bash
# Node.js optimization
export NODE_OPTIONS="--max-old-space-size=4096"

# Enable clustering
export CLUSTER_MODE=true
export CLUSTER_WORKERS=4
```

#### Database Optimization
```cypher
// Neo4j index optimization
CREATE INDEX file_path_index FOR (f:File) ON (f.path);
CREATE INDEX function_name_index FOR (fn:Function) ON (fn.name);
CREATE INDEX class_name_index FOR (c:Class) ON (c.name);

// Configure memory settings
dbms.memory.heap.initial_size=2g
dbms.memory.heap.max_size=4g
dbms.memory.pagecache.size=2g
```

## 🔍 Monitoring and Maintenance

### Health Monitoring

#### Automated Health Checks
```bash
#!/bin/bash
# health-check.sh
HEALTH_URL="http://localhost:3000/api/health"
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" $HEALTH_URL)

if [ $RESPONSE -eq 200 ]; then
    echo "Service is healthy"
    exit 0
else
    echo "Service is unhealthy (HTTP $RESPONSE)"
    exit 1
fi
```

#### Monitoring Dashboards
```bash
# Grafana dashboard setup
# Import pre-configured dashboards
# Configure alerts for critical metrics
# Set up notification channels
```

### Backup and Recovery

#### Database Backup
```bash
#!/bin/bash
# backup-neo4j.sh
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/neo4j"

# Create backup
docker exec neo4j neo4j-admin backup \
    --backup-dir=/backups \
    --name=neo4j-backup-$DATE

# Compress backup
tar -czf $BACKUP_DIR/neo4j-backup-$DATE.tar.gz \
    $BACKUP_DIR/neo4j-backup-$DATE
```

#### Application Backup
```bash
#!/bin/bash
# backup-app.sh
DATE=$(date +%Y%m%d_%H%M%S)

# Backup configuration
cp .env /backups/config/env-$DATE
cp docker-compose.yml /backups/config/docker-compose-$DATE.yml

# Backup logs
tar -czf /backups/logs/logs-$DATE.tar.gz logs/
```

### Maintenance Tasks

#### Regular Maintenance
```bash
#!/bin/bash
# maintenance.sh

# Clean up old logs
find logs/ -name "*.log" -mtime +30 -delete

# Clean up Docker images
docker system prune -f

# Update dependencies (with testing)
npm audit fix
docker-compose build --no-cache

# Restart services if needed
docker-compose restart
```

## 🚨 Troubleshooting

### Common Issues

#### Service Won't Start
```bash
# Check logs
docker-compose logs context-engine

# Check configuration
docker-compose config

# Verify environment variables
docker-compose exec context-engine env | grep NEO4J
```

#### Database Connection Issues
```bash
# Test Neo4j connectivity
docker-compose exec context-engine \
    node -e "console.log('Testing Neo4j connection...')"

# Check Neo4j logs
docker-compose logs neo4j

# Verify credentials
docker-compose exec neo4j cypher-shell -u neo4j -p password
```

#### Performance Issues
```bash
# Check resource usage
docker stats

# Monitor query performance
curl http://localhost:3000/api/health/metrics

# Check slow queries
curl http://localhost:3000/api/context/statistics
```

### Recovery Procedures

#### Service Recovery
```bash
# Restart individual service
docker-compose restart context-engine

# Full system restart
docker-compose down
docker-compose up -d

# Restore from backup
docker-compose down
# Restore database backup
# Restore configuration
docker-compose up -d
```

## 📊 Performance Benchmarks

### Expected Performance
- **File Processing**: 10-50 files/second
- **Query Response**: <500ms for simple queries
- **Memory Usage**: 1-2GB per instance
- **CPU Usage**: 50-80% under load

### Load Testing
```bash
# Install load testing tools
npm install -g artillery

# Run load tests
artillery run load-test.yml

# Monitor during load test
watch -n 1 'curl -s http://localhost:3000/api/health/metrics'
```

## 🔐 Security Considerations

### Network Security
- Use HTTPS in production
- Implement API rate limiting
- Configure firewall rules
- Use VPN for database access

### Application Security
- Validate all inputs
- Use secure session management
- Implement proper authentication
- Regular security updates

### Data Security
- Encrypt sensitive data
- Secure backup storage
- Implement access controls
- Regular security audits

## 📞 Support and Maintenance

### Getting Help
- Check logs first: `docker-compose logs`
- Review health endpoints: `/api/health/detailed`
- Monitor performance: `/api/health/metrics`
- Check documentation: `README.md`

### Regular Updates
- Monitor for security updates
- Test updates in staging first
- Backup before major updates
- Follow semantic versioning

This deployment guide provides a comprehensive foundation for running the Advanced Code Context Engine in production environments. Adjust configurations based on your specific requirements and infrastructure.
