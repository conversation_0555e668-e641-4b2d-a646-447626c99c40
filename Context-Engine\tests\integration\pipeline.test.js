/**
 * Integration tests for processing pipeline
 */

import { jest } from '@jest/globals';
import fs from 'fs/promises';
import path from 'path';

// Mock external dependencies
jest.unstable_mockModule('../../src/utils/logger.js', () => ({
  createLogger: jest.fn(() => ({
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  })),
  logGraph: jest.fn(),
  logIngestion: jest.fn()
}));

// Mock Neo4j client
const mockNeo4jClient = {
  session: jest.fn(() => ({
    run: jest.fn(),
    close: jest.fn(),
    beginTransaction: jest.fn(),
    commitTransaction: jest.fn(),
    rollbackTransaction: jest.fn()
  })),
  run: jest.fn(),
  healthCheck: jest.fn(() => ({ status: 'healthy' })),
  getStatistics: jest.fn(() => ({ nodeCount: 0, relationshipCount: 0 }))
};

describe('Processing Pipeline Integration', () => {
  let IngestionPipeline;
  let ASTProcessor;
  let SymbolTableBuilder;
  let GraphManager;
  let pipeline;

  beforeAll(async () => {
    // Import modules
    const ingestionModule = await import('../../src/ingestion/pipeline/IngestionPipeline.js');
    const astModule = await import('../../src/core/processors/ASTProcessor.js');
    const symbolModule = await import('../../src/core/processors/SymbolTableBuilder.js');
    const graphModule = await import('../../src/core/graph/GraphManager.js');

    IngestionPipeline = ingestionModule.IngestionPipeline;
    ASTProcessor = astModule.ASTProcessor;
    SymbolTableBuilder = symbolModule.SymbolTableBuilder;
    GraphManager = graphModule.GraphManager;
  });

  beforeEach(() => {
    jest.clearAllMocks();
    
    const config = {
      neo4jClient: mockNeo4jClient,
      maxConcurrent: 2,
      batchSize: 5,
      supportedLanguages: ['javascript', 'python'],
      fileExtensions: {
        javascript: ['.js', '.jsx'],
        python: ['.py']
      }
    };

    pipeline = new IngestionPipeline(config);
  });

  describe('Pipeline Initialization', () => {
    test('should initialize all components successfully', async () => {
      await expect(pipeline.initialize()).resolves.not.toThrow();
      expect(pipeline.processors).toHaveLength(2); // AST + Symbol processors
      expect(pipeline.graphManager).toBeDefined();
    });

    test('should handle initialization errors gracefully', async () => {
      // Mock a processor that fails to initialize
      const failingProcessor = {
        initialize: jest.fn().mockRejectedValue(new Error('Init failed'))
      };
      
      pipeline.processors = [failingProcessor];
      
      await expect(pipeline.initialize()).rejects.toThrow('Init failed');
    });
  });

  describe('File Processing', () => {
    beforeEach(async () => {
      await pipeline.initialize();
    });

    test('should process JavaScript file through complete pipeline', async () => {
      const jsFile = {
        path: 'test.js',
        content: `
          class TestClass {
            constructor(name) {
              this.name = name;
            }
            
            getName() {
              return this.name;
            }
          }
          
          function createTest(name) {
            return new TestClass(name);
          }
        `,
        language: 'javascript',
        size: 200,
        lastModified: new Date(),
        version: '1.0.0'
      };

      // Mock graph manager to return success
      pipeline.graphManager.storeContext = jest.fn().mockResolvedValue({
        nodesCreated: 5,
        relationshipsCreated: 3
      });

      const result = await pipeline.processFile(jsFile);
      
      expect(result).toBeDefined();
      expect(result.file).toBe('test.js');
      expect(result.nodesCreated).toBe(5);
      expect(result.relationshipsCreated).toBe(3);
      expect(pipeline.graphManager.storeContext).toHaveBeenCalled();
    });

    test('should process Python file through complete pipeline', async () => {
      const pyFile = {
        path: 'test.py',
        content: `
          class TestClass:
              def __init__(self, name):
                  self.name = name
              
              def get_name(self):
                  return self.name
          
          def create_test(name):
              return TestClass(name)
        `,
        language: 'python',
        size: 180,
        lastModified: new Date(),
        version: '1.0.0'
      };

      pipeline.graphManager.storeContext = jest.fn().mockResolvedValue({
        nodesCreated: 4,
        relationshipsCreated: 2
      });

      const result = await pipeline.processFile(pyFile);
      
      expect(result).toBeDefined();
      expect(result.file).toBe('test.py');
      expect(result.nodesCreated).toBe(4);
      expect(result.relationshipsCreated).toBe(2);
    });

    test('should handle processing errors gracefully', async () => {
      const invalidFile = {
        path: 'invalid.js',
        content: 'function incomplete(',
        language: 'javascript',
        size: 20,
        lastModified: new Date(),
        version: '1.0.0'
      };

      // Mock graph manager to throw error
      pipeline.graphManager.storeContext = jest.fn().mockRejectedValue(
        new Error('Graph storage failed')
      );

      await expect(pipeline.processFile(invalidFile)).rejects.toThrow('Graph storage failed');
      expect(pipeline.statistics.processingErrors).toBe(1);
    });
  });

  describe('Batch Processing', () => {
    beforeEach(async () => {
      await pipeline.initialize();
    });

    test('should process multiple files in batches', async () => {
      const files = [
        {
          path: 'file1.js',
          content: 'function test1() { return 1; }',
          language: 'javascript',
          size: 30,
          lastModified: new Date(),
          version: '1.0.0'
        },
        {
          path: 'file2.js',
          content: 'function test2() { return 2; }',
          language: 'javascript',
          size: 30,
          lastModified: new Date(),
          version: '1.0.0'
        },
        {
          path: 'file3.py',
          content: 'def test3(): return 3',
          language: 'python',
          size: 25,
          lastModified: new Date(),
          version: '1.0.0'
        }
      ];

      pipeline.graphManager.storeContext = jest.fn().mockResolvedValue({
        nodesCreated: 2,
        relationshipsCreated: 1
      });

      // Mock repository processing
      const mockGitIngester = {
        initialize: jest.fn(),
        getFiles: jest.fn().mockResolvedValue(files)
      };

      // Simulate repository processing
      const results = [];
      for (const file of files) {
        try {
          const result = await pipeline.processFile(file);
          results.push(result);
        } catch (error) {
          // Handle individual file errors
        }
      }

      expect(results).toHaveLength(3);
      expect(pipeline.graphManager.storeContext).toHaveBeenCalledTimes(3);
      expect(pipeline.statistics.filesProcessed).toBe(3);
    });

    test('should handle partial batch failures', async () => {
      const files = [
        {
          path: 'good.js',
          content: 'function good() { return true; }',
          language: 'javascript',
          size: 35,
          lastModified: new Date(),
          version: '1.0.0'
        },
        {
          path: 'bad.js',
          content: 'function bad() { throw error; }',
          language: 'javascript',
          size: 35,
          lastModified: new Date(),
          version: '1.0.0'
        }
      ];

      // Mock graph manager to succeed for first file, fail for second
      pipeline.graphManager.storeContext = jest.fn()
        .mockResolvedValueOnce({ nodesCreated: 1, relationshipsCreated: 0 })
        .mockRejectedValueOnce(new Error('Storage failed'));

      const results = [];
      const errors = [];

      for (const file of files) {
        try {
          const result = await pipeline.processFile(file);
          results.push(result);
        } catch (error) {
          errors.push(error);
        }
      }

      expect(results).toHaveLength(1);
      expect(errors).toHaveLength(1);
      expect(pipeline.statistics.filesProcessed).toBe(1);
      expect(pipeline.statistics.processingErrors).toBe(1);
    });
  });

  describe('Queue Management', () => {
    beforeEach(async () => {
      await pipeline.initialize();
    });

    test('should queue files for processing', () => {
      const file = global.testUtils.createMockFile();
      
      pipeline.queueFile(file);
      
      expect(pipeline.queue).toHaveLength(1);
      expect(pipeline.statistics.filesQueued).toBe(1);
    });

    test('should process queued files when pipeline is running', async () => {
      const file = global.testUtils.createMockFile();
      
      pipeline.graphManager.storeContext = jest.fn().mockResolvedValue({
        nodesCreated: 1,
        relationshipsCreated: 0
      });

      pipeline.queueFile(file);
      await pipeline.start();
      
      // Wait a bit for processing
      await global.testUtils.delay(100);
      
      expect(pipeline.isRunning).toBe(true);
      
      await pipeline.stop();
      expect(pipeline.isRunning).toBe(false);
    });
  });

  describe('Statistics and Health', () => {
    beforeEach(async () => {
      await pipeline.initialize();
    });

    test('should track processing statistics', async () => {
      const file = global.testUtils.createMockFile();
      
      pipeline.graphManager.storeContext = jest.fn().mockResolvedValue({
        nodesCreated: 2,
        relationshipsCreated: 1
      });

      await pipeline.processFile(file);
      
      const stats = await pipeline.getStatistics();
      
      expect(stats.filesProcessed).toBe(1);
      expect(stats.processingErrors).toBe(0);
      expect(stats.uptime).toBeGreaterThan(0);
      expect(stats.processingRate).toBeGreaterThanOrEqual(0);
    });

    test('should report health status', async () => {
      const health = await pipeline.getHealth();
      
      expect(health.status).toBe('healthy');
      expect(health.isRunning).toBe(false);
      expect(health.queueSize).toBe(0);
      expect(health.processorCount).toBe(2);
    });

    test('should report stopped status when not running', async () => {
      const health = await pipeline.getHealth();
      
      expect(health.status).toBe('healthy'); // Not running but healthy
      expect(health.isRunning).toBe(false);
    });
  });

  describe('Error Recovery', () => {
    beforeEach(async () => {
      await pipeline.initialize();
    });

    test('should continue processing after individual file errors', async () => {
      const files = [
        global.testUtils.createMockFile({ path: 'file1.js' }),
        global.testUtils.createMockFile({ path: 'file2.js' }),
        global.testUtils.createMockFile({ path: 'file3.js' })
      ];

      // Mock to fail on second file
      pipeline.graphManager.storeContext = jest.fn()
        .mockResolvedValueOnce({ nodesCreated: 1, relationshipsCreated: 0 })
        .mockRejectedValueOnce(new Error('Temporary failure'))
        .mockResolvedValueOnce({ nodesCreated: 1, relationshipsCreated: 0 });

      const results = [];
      const errors = [];

      for (const file of files) {
        try {
          const result = await pipeline.processFile(file);
          results.push(result);
        } catch (error) {
          errors.push(error);
        }
      }

      expect(results).toHaveLength(2); // First and third succeeded
      expect(errors).toHaveLength(1); // Second failed
      expect(pipeline.statistics.filesProcessed).toBe(2);
      expect(pipeline.statistics.processingErrors).toBe(1);
    });
  });
});
