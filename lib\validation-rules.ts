import type { ProjectContext } from "@/types/planning"
import type { ContextIntegrityIssue } from "@/types/context-engine"
import type { ValidationRule, Validation<PERSON><PERSON>ult, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, AlignmentResult, Misalignment } from "./cross-reference-validator"

/**
 * Tech Stack Consistency Rule
 * Ensures tech stack choices are consistent and compatible
 */
export class TechStackConsistencyRule implements ValidationRule {
  id = 'tech-stack-consistency'
  category = 'techstack'
  description = 'Validates tech stack consistency and compatibility'

  async validate(context: ProjectContext): Promise<ValidationResult> {
    const issues: ContextIntegrityIssue[] = []
    const techStack = context.techStack as any

    if (!techStack) {
      issues.push({
        type: 'missing_dependency',
        step: 'techstack',
        severity: 'critical',
        message: 'Tech stack is missing'
      })
      return { isValid: false, issues }
    }

    // Check frontend-backend compatibility
    if (techStack.frontend && techStack.backend) {
      const incompatibleCombos = [
        { frontend: 'React', backend: 'PHP', reason: 'Consider Next.js for better React-PHP integration' },
        { frontend: 'Vue', backend: 'ASP.NET', reason: 'Consider Nuxt.js or different backend' }
      ]

      for (const combo of incompatibleCombos) {
        if (techStack.frontend.includes(combo.frontend) && techStack.backend.includes(combo.backend)) {
          issues.push({
            type: 'data_mismatch',
            step: 'techstack',
            field: 'frontend-backend',
            severity: 'medium',
            message: `${combo.frontend} + ${combo.backend} may have integration challenges`,
            suggestedFix: combo.reason
          })
        }
      }
    }

    // Check database compatibility
    if (techStack.database && techStack.backend) {
      const dbCompatibility = {
        'Node.js': ['MongoDB', 'PostgreSQL', 'MySQL', 'SQLite'],
        'Python': ['PostgreSQL', 'MySQL', 'SQLite', 'MongoDB'],
        'Java': ['PostgreSQL', 'MySQL', 'Oracle', 'H2'],
        'C#': ['SQL Server', 'PostgreSQL', 'MySQL']
      }

      const backend = techStack.backend
      const database = techStack.database
      const compatibleDbs = dbCompatibility[backend as keyof typeof dbCompatibility]

      if (compatibleDbs && !compatibleDbs.some(db => database.includes(db))) {
        issues.push({
          type: 'data_mismatch',
          step: 'techstack',
          field: 'database-backend',
          severity: 'high',
          message: `${database} may not be optimal for ${backend}`,
          suggestedFix: `Consider: ${compatibleDbs.join(', ')}`
        })
      }
    }

    return { isValid: issues.length === 0, issues }
  }
}

/**
 * Requirements Coverage Rule
 * Ensures all requirements are properly covered in subsequent steps
 */
export class RequirementsCoverageRule implements ValidationRule {
  id = 'requirements-coverage'
  category = 'requirements'
  description = 'Validates that all requirements are covered in implementation plans'

  async validate(context: ProjectContext): Promise<ValidationResult> {
    const issues: ContextIntegrityIssue[] = []
    const prd = context.prd as any
    const wireframes = context.wireframes
    const tasks = context.tasks as any

    if (!prd || !prd.features) {
      issues.push({
        type: 'missing_dependency',
        step: 'prd',
        severity: 'critical',
        message: 'PRD or features are missing'
      })
      return { isValid: false, issues }
    }

    // Check if wireframes cover all UI requirements
    if (wireframes && prd.features) {
      const uiFeatures = prd.features.filter((f: string) => 
        f.toLowerCase().includes('ui') || 
        f.toLowerCase().includes('interface') ||
        f.toLowerCase().includes('form') ||
        f.toLowerCase().includes('page')
      )

      if (uiFeatures.length > 0 && wireframes.length === 0) {
        issues.push({
          type: 'missing_dependency',
          step: 'wireframes',
          severity: 'high',
          message: 'UI features identified but no wireframes created'
        })
      }
    }

    // Check if tasks cover all requirements
    if (tasks && tasks.tasks && prd.features) {
      const taskDescriptions = tasks.tasks.map((t: any) => t.description || t.title || '').join(' ').toLowerCase()
      const uncoveredFeatures = prd.features.filter((feature: string) => 
        !taskDescriptions.includes(feature.toLowerCase())
      )

      if (uncoveredFeatures.length > 0) {
        issues.push({
          type: 'missing_dependency',
          step: 'tasks',
          field: 'feature-coverage',
          severity: 'medium',
          message: `Features not covered in tasks: ${uncoveredFeatures.join(', ')}`,
          suggestedFix: 'Add implementation tasks for all features'
        })
      }
    }

    return { isValid: issues.length === 0, issues }
  }
}

/**
 * Wireframe Alignment Rule
 * Ensures wireframes align with requirements and tech stack
 */
export class WireframeAlignmentRule implements ValidationRule {
  id = 'wireframe-alignment'
  category = 'wireframes'
  description = 'Validates wireframe alignment with requirements and tech stack'

  async validate(context: ProjectContext): Promise<ValidationResult> {
    const issues: ContextIntegrityIssue[] = []
    const wireframes = context.wireframes
    const techStack = context.techStack as any
    const prd = context.prd as any

    if (!wireframes || wireframes.length === 0) {
      return { isValid: true, issues } // No wireframes to validate
    }

    // Check if wireframes match tech stack capabilities
    if (techStack && techStack.frontend) {
      const frontend = techStack.frontend.toLowerCase()
      
      // Check for mobile-specific wireframes if mobile tech is chosen
      if ((frontend.includes('react native') || frontend.includes('flutter')) && wireframes) {
        const hasMobileWireframes = wireframes.some((w: any) => 
          w.type === 'mobile' || w.platform === 'mobile'
        )
        
        if (!hasMobileWireframes) {
          issues.push({
            type: 'data_mismatch',
            step: 'wireframes',
            field: 'platform',
            severity: 'medium',
            message: 'Mobile tech stack chosen but no mobile wireframes created',
            suggestedFix: 'Create mobile-specific wireframes'
          })
        }
      }
    }

    // Check if wireframes cover all user stories
    if (prd && prd.userStories && wireframes) {
      const wireframePages = wireframes.map((w: any) => w.name || w.title || '').join(' ').toLowerCase()
      const userStories = Array.isArray(prd.userStories) ? prd.userStories : []
      
      const uncoveredStories = userStories.filter((story: string) => {
        const storyKeywords = story.toLowerCase().match(/\b(page|form|dashboard|profile|login|signup)\b/g) || []
        return storyKeywords.length > 0 && !storyKeywords.some(keyword => wireframePages.includes(keyword))
      })

      if (uncoveredStories.length > 0) {
        issues.push({
          type: 'missing_dependency',
          step: 'wireframes',
          field: 'user-story-coverage',
          severity: 'medium',
          message: `User stories not covered in wireframes: ${uncoveredStories.slice(0, 3).join(', ')}`,
          suggestedFix: 'Create wireframes for all user-facing features'
        })
      }
    }

    return { isValid: issues.length === 0, issues }
  }
}

/**
 * Database Schema Consistency Rule
 * Ensures database schema supports all requirements
 */
export class DatabaseSchemaConsistencyRule implements ValidationRule {
  id = 'database-schema-consistency'
  category = 'database'
  description = 'Validates database schema consistency with requirements'

  async validate(context: ProjectContext): Promise<ValidationResult> {
    const issues: ContextIntegrityIssue[] = []
    const database = context.database as any
    const prd = context.prd as any
    const techStack = context.techStack as any

    if (!database) {
      return { isValid: true, issues } // No database to validate
    }

    // Check if database type matches tech stack
    if (techStack && techStack.database && database.databaseType) {
      const expectedType = techStack.database.toLowerCase()
      const actualType = database.databaseType.toLowerCase()
      
      const typeMapping: Record<string, string[]> = {
        'mongodb': ['document', 'nosql'],
        'postgresql': ['relational', 'sql'],
        'mysql': ['relational', 'sql'],
        'redis': ['key-value', 'cache'],
        'neo4j': ['graph']
      }

      const expectedTypes = Object.entries(typeMapping).find(([db]) => 
        expectedType.includes(db)
      )?.[1] || []

      if (expectedTypes.length > 0 && !expectedTypes.includes(actualType)) {
        issues.push({
          type: 'data_mismatch',
          step: 'database',
          field: 'database-type',
          severity: 'high',
          message: `Database type mismatch: expected ${expectedTypes.join(' or ')}, got ${actualType}`,
          suggestedFix: `Align database schema type with tech stack choice`
        })
      }
    }

    return { isValid: issues.length === 0, issues }
  }
}

/**
 * File System Convention Rule
 * Ensures file system follows tech stack conventions
 */
export class FileSystemConventionRule implements ValidationRule {
  id = 'filesystem-convention'
  category = 'filesystem'
  description = 'Validates file system follows tech stack conventions'

  async validate(context: ProjectContext): Promise<ValidationResult> {
    const issues: ContextIntegrityIssue[] = []
    const filesystem = context.filesystem as any
    const techStack = context.techStack as any

    if (!filesystem || !techStack) {
      return { isValid: true, issues }
    }

    // Check for framework-specific conventions
    if (techStack.frontend) {
      const frontend = techStack.frontend.toLowerCase()
      
      if (frontend.includes('next.js') && filesystem.structure) {
        const hasAppDir = JSON.stringify(filesystem).includes('app/') || JSON.stringify(filesystem).includes('pages/')
        if (!hasAppDir) {
          issues.push({
            type: 'validation_failure',
            step: 'filesystem',
            field: 'structure',
            severity: 'medium',
            message: 'Next.js project should have app/ or pages/ directory',
            suggestedFix: 'Add Next.js conventional directory structure'
          })
        }
      }
    }

    return { isValid: issues.length === 0, issues }
  }
}

// Additional rules would be implemented similarly...
export class WorkflowLogicRule implements ValidationRule {
  id = 'workflow-logic'
  category = 'workflow'
  description = 'Validates workflow logic consistency'

  async validate(context: ProjectContext): Promise<ValidationResult> {
    return { isValid: true, issues: [] } // Simplified implementation
  }
}

export class SecurityRequirementsRule implements ValidationRule {
  id = 'security-requirements'
  category = 'security'
  description = 'Validates security requirements are addressed'

  async validate(context: ProjectContext): Promise<ValidationResult> {
    return { isValid: true, issues: [] } // Simplified implementation
  }
}

export class PerformanceRequirementsRule implements ValidationRule {
  id = 'performance-requirements'
  category = 'performance'
  description = 'Validates performance requirements are addressed'

  async validate(context: ProjectContext): Promise<ValidationResult> {
    return { isValid: true, issues: [] } // Simplified implementation
  }
}
