/**
 * Jest test setup file
 */

import dotenv from 'dotenv';

// Load test environment variables
dotenv.config({ path: '.env.test' });

// Set test environment variables if not already set
process.env.NODE_ENV = process.env.NODE_ENV || 'test';
process.env.NEO4J_URI = process.env.NEO4J_URI || 'bolt://localhost:7687';
process.env.NEO4J_USER = process.env.NEO4J_USER || 'neo4j';
process.env.NEO4J_PASSWORD = process.env.NEO4J_PASSWORD || 'test123';
process.env.NEO4J_DATABASE = process.env.NEO4J_DATABASE || 'neo4j';
process.env.REDIS_URL = process.env.REDIS_URL || 'redis://localhost:6379';
process.env.API_KEY_SECRET = process.env.API_KEY_SECRET || 'test_secret_key_for_testing_only';
process.env.LOG_LEVEL = process.env.LOG_LEVEL || 'error';
process.env.PINECONE_API_KEY = process.env.PINECONE_API_KEY || 'test_pinecone_key';
process.env.PINECONE_ENVIRONMENT = process.env.PINECONE_ENVIRONMENT || 'test';

// Global test utilities
global.testUtils = {
  createMockFile: (overrides = {}) => ({
    path: 'test.js',
    content: 'function test() { return true; }',
    language: 'javascript',
    size: 100,
    lastModified: new Date(),
    version: '1.0.0',
    ...overrides
  }),

  createMockContext: (overrides = {}) => ({
    file: global.testUtils.createMockFile(),
    ast: {
      language: 'javascript',
      nodes: [],
      imports: [],
      exports: [],
      metadata: { hasErrors: false, nodeCount: 0 }
    },
    symbols: {
      symbols: [],
      scopes: [],
      references: [],
      imports: [],
      exports: []
    },
    ...overrides
  }),

  delay: (ms) => new Promise(resolve => setTimeout(resolve, ms))
};

// Mock console methods in test environment to reduce noise
if (process.env.NODE_ENV === 'test') {
  global.console = {
    ...console,
    log: jest.fn(),
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn()
  };
}
