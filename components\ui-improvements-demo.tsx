"use client"

import React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"

// Sample data that would typically show as JSON
const sampleData = {
  // Array of step objects (workflow)
  steps: [
    {
      step: 1,
      action: "Initialize project setup",
      description: "Set up the basic project structure and dependencies",
      result: "Project foundation ready"
    },
    {
      step: 2,
      action: "Configure development environment",
      description: "Install and configure development tools",
      result: "Development environment ready"
    }
  ],
  
  // Array of feature objects
  features: [
    {
      name: "User Authentication",
      description: "Secure login and registration system",
      priority: "high",
      estimate: "3 days"
    },
    {
      name: "Dashboard",
      description: "Main user interface with key metrics",
      priority: "medium",
      estimate: "5 days"
    }
  ],
  
  // Array of task objects
  tasks: [
    {
      task: "Build login component",
      assignee: "Frontend Developer",
      estimate: "2 days",
      dependencies: ["API setup"]
    },
    {
      task: "Create user database schema",
      assignee: "Backend Developer", 
      estimate: "1 day",
      dependencies: []
    }
  ],

  // Complex nested object
  projectStructure: {
    folders: [
      {
        name: "src",
        purpose: "Main source code directory",
        subfolders: ["components", "pages", "utils"],
        keyFiles: ["App.tsx", "main.tsx"]
      },
      {
        name: "public",
        purpose: "Static assets",
        subfolders: ["images", "icons"],
        keyFiles: ["index.html", "favicon.ico"]
      }
    ]
  }
}

export function UIImprovementsDemo() {
  return (
    <div className="min-h-screen bg-black text-white p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-8 text-center">
          UI Improvements Demo: JSON → Human Readable
        </h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Before - Raw JSON */}
          <Card className="border-red-600">
            <CardHeader>
              <CardTitle className="text-red-400">❌ Before: Raw JSON Display</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="text-white font-medium mb-2">Workflow Steps</h4>
                  <div className="bg-gray-800 p-4 rounded overflow-auto max-h-48">
                    <pre className="text-gray-300 text-xs whitespace-pre-wrap">
                      {JSON.stringify(sampleData.steps, null, 2)}
                    </pre>
                  </div>
                </div>
                
                <div>
                  <h4 className="text-white font-medium mb-2">Features</h4>
                  <div className="bg-gray-800 p-4 rounded overflow-auto max-h-48">
                    <pre className="text-gray-300 text-xs whitespace-pre-wrap">
                      {JSON.stringify(sampleData.features, null, 2)}
                    </pre>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* After - Human Readable */}
          <Card className="border-green-600">
            <CardHeader>
              <CardTitle className="text-green-400">✅ After: Human Readable Display</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="text-white font-medium mb-2">Workflow Steps</h4>
                  <div className="space-y-2">
                    {sampleData.steps.map((step, index) => (
                      <div key={index} className="flex items-start gap-3 p-3 bg-gray-800 rounded-lg">
                        <span className="flex-shrink-0 w-6 h-6 bg-red-600 text-white text-xs rounded-full flex items-center justify-center font-bold">
                          {step.step}
                        </span>
                        <div className="flex-1">
                          <div className="text-white font-medium">{step.action}</div>
                          <div className="text-gray-300 text-sm mt-1">{step.description}</div>
                          <div className="text-gray-400 text-sm mt-1">Result: {step.result}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h4 className="text-white font-medium mb-2">Features</h4>
                  <div className="space-y-3">
                    {sampleData.features.map((feature, index) => (
                      <div key={index} className="bg-gray-800 p-3 rounded-lg border border-gray-700">
                        <div className="text-white font-medium mb-1">{feature.name}</div>
                        <div className="text-gray-300 text-sm mb-2">{feature.description}</div>
                        <div className="space-y-1">
                          <div className="flex justify-between items-start text-xs">
                            <span className="text-gray-400">Priority:</span>
                            <span className="text-gray-300 ml-2">{feature.priority}</span>
                          </div>
                          <div className="flex justify-between items-start text-xs">
                            <span className="text-gray-400">Estimate:</span>
                            <span className="text-gray-300 ml-2">{feature.estimate}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="mt-8 text-center">
          <p className="text-gray-400">
            The improvements automatically detect common object patterns and render them in a user-friendly format,
            while still providing access to raw JSON when needed.
          </p>
        </div>
      </div>
    </div>
  )
}
