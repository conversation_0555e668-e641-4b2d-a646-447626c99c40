/**
 * Test Inngest Integration
 * Simple test to verify the async orchestration stack works
 */

async function testInngestIntegration() {
  console.log('🧪 Testing Inngest Integration...\n')

  try {
    // Test 1: Check if Inngest API route exists
    console.log('1. Testing Inngest API route...')
    const inngestResponse = await fetch('http://localhost:3001/api/inngest', {
      method: 'GET'
    })
    
    if (inngestResponse.ok) {
      console.log('✅ Inngest API route is accessible')
    } else {
      console.log('❌ Inngest API route failed:', inngestResponse.status)
    }

    // Test 2: Test planning service import
    console.log('\n2. Testing PlanningService import...')
    try {
      const { PlanningService } = await import('./src/modules/planning/planning.service.js')
      console.log('✅ PlanningService imported successfully')
      
      // Test service instantiation
      const service = new PlanningService({
        maxRetries: 1,
        timeoutMs: 5000,
        enableMCP: true,
        enableSequentialThinking: true,
        enableContext7: true,
        model: 'gpt-4o-mini',
        temperature: 0.7
      })
      console.log('✅ PlanningService instantiated successfully')
      
    } catch (importError) {
      console.log('❌ PlanningService import failed:', importError.message)
    }

    // Test 3: Test Inngest client
    console.log('\n3. Testing Inngest client...')
    try {
      const { inngest } = await import('./src/lib/inngest/client.js')
      console.log('✅ Inngest client imported successfully')
      console.log('   Client ID:', inngest.id)
    } catch (inngestError) {
      console.log('❌ Inngest client import failed:', inngestError.message)
    }

    // Test 4: Test environment configuration
    console.log('\n4. Testing environment configuration...')
    try {
      const { simpleConfig } = await import('./src/env.js')
      console.log('✅ Environment config imported successfully')
      console.log('   MCP enabled:', simpleConfig.ai.enableMCP)
      console.log('   Sequential thinking enabled:', simpleConfig.ai.enableSequentialThinking)
    } catch (envError) {
      console.log('❌ Environment config import failed:', envError.message)
    }

    // Test 5: Test planning API with new structure
    console.log('\n5. Testing planning API...')
    try {
      const planningResponse = await fetch('http://localhost:3001/api/planning', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          prompt: 'Test integration: Build a simple todo app',
          isInteractive: false,
          answers: {}
        })
      })

      if (planningResponse.ok) {
        const result = await planningResponse.json()
        console.log('✅ Planning API responded successfully')
        console.log('   Success:', result.success)
        console.log('   Has session ID:', !!result.sessionId)
        console.log('   Has results:', !!result.results)
      } else {
        console.log('❌ Planning API failed:', planningResponse.status)
        const error = await planningResponse.text()
        console.log('   Error:', error)
      }
    } catch (apiError) {
      console.log('❌ Planning API test failed:', apiError.message)
    }

    console.log('\n🎯 Integration Test Summary:')
    console.log('- If all tests pass ✅, the async orchestration stack is ready')
    console.log('- If some tests fail ❌, we need to fix those components')
    console.log('- The system will gracefully fallback to the old planning system if needed')

  } catch (error) {
    console.error('❌ Integration test failed:', error)
  }
}

// Run the test
testInngestIntegration().catch(console.error)
