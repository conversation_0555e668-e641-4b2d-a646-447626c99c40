import Joi from 'joi';
import { createLogger } from '../../utils/logger.js';

const logger = createLogger('ValidationMiddleware');

/**
 * Query validation schema
 */
const querySchema = Joi.object({
  query: Joi.string().min(1).max(1000).required(),
  options: Joi.object({
    language: Joi.string().valid('javascript', 'typescript', 'python', 'java', 'go', 'rust'),
    filePattern: Joi.string().max(100),
    limit: Joi.number().integer().min(1).max(100).default(10),
    minLines: Joi.number().integer().min(1),
    maxLines: Joi.number().integer().min(1),
    includeTests: Joi.boolean().default(true),
    includeNodeModules: Joi.boolean().default(false)
  }).default({})
});

/**
 * Repository validation schema
 */
const repositorySchema = Joi.object({
  repositoryPath: Joi.string().min(1).max(500).required(),
  options: Joi.object({
    fileFilter: Joi.function(),
    batchSize: Joi.number().integer().min(1).max(1000).default(100),
    maxConcurrent: Joi.number().integer().min(1).max(20).default(5),
    includeTests: Joi.boolean().default(true),
    includeNodeModules: Joi.boolean().default(false),
    languages: Joi.array().items(
      Joi.string().valid('javascript', 'typescript', 'python', 'java', 'go', 'rust')
    ),
    excludePatterns: Joi.array().items(Joi.string().max(100))
  }).default({})
});

/**
 * Validate query request
 */
export const validateQuery = (req, res, next) => {
  const { error, value } = querySchema.validate(req.body, {
    allowUnknown: false,
    stripUnknown: true
  });

  if (error) {
    logger.warn('Query validation failed', {
      error: error.details[0].message,
      path: error.details[0].path,
      value: error.details[0].context?.value
    });

    return res.status(400).json({
      success: false,
      error: {
        message: 'Invalid query request',
        details: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message,
          value: detail.context?.value
        })),
        type: 'VALIDATION_ERROR'
      }
    });
  }

  // Replace request body with validated and sanitized data
  req.body = value;
  next();
};

/**
 * Validate repository processing request
 */
export const validateRepository = (req, res, next) => {
  const { error, value } = repositorySchema.validate(req.body, {
    allowUnknown: false,
    stripUnknown: true
  });

  if (error) {
    logger.warn('Repository validation failed', {
      error: error.details[0].message,
      path: error.details[0].path,
      value: error.details[0].context?.value
    });

    return res.status(400).json({
      success: false,
      error: {
        message: 'Invalid repository processing request',
        details: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message,
          value: detail.context?.value
        })),
        type: 'VALIDATION_ERROR'
      }
    });
  }

  // Additional validation for repository path
  if (!value.repositoryPath.startsWith('/') && !value.repositoryPath.match(/^[A-Za-z]:/)) {
    return res.status(400).json({
      success: false,
      error: {
        message: 'Repository path must be an absolute path',
        type: 'VALIDATION_ERROR'
      }
    });
  }

  // Replace request body with validated and sanitized data
  req.body = value;
  next();
};

/**
 * Validate pagination parameters
 */
export const validatePagination = (req, res, next) => {
  const paginationSchema = Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    sort: Joi.string().valid('name', 'created', 'modified', 'size').default('name'),
    order: Joi.string().valid('asc', 'desc').default('asc')
  });

  const { error, value } = paginationSchema.validate(req.query, {
    allowUnknown: true,
    stripUnknown: false
  });

  if (error) {
    logger.warn('Pagination validation failed', {
      error: error.details[0].message,
      query: req.query
    });

    return res.status(400).json({
      success: false,
      error: {
        message: 'Invalid pagination parameters',
        details: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message
        })),
        type: 'VALIDATION_ERROR'
      }
    });
  }

  // Add validated pagination to request
  req.pagination = {
    page: value.page,
    limit: value.limit,
    sort: value.sort,
    order: value.order,
    offset: (value.page - 1) * value.limit
  };

  next();
};

/**
 * Validate search parameters
 */
export const validateSearch = (req, res, next) => {
  const searchSchema = Joi.object({
    q: Joi.string().min(1).max(200).required(),
    language: Joi.string().valid('javascript', 'typescript', 'python', 'java', 'go', 'rust'),
    type: Joi.string().valid('function', 'class', 'variable', 'file', 'all').default('all'),
    limit: Joi.number().integer().min(1).max(100).default(20)
  });

  const { error, value } = searchSchema.validate(req.query, {
    allowUnknown: true,
    stripUnknown: true
  });

  if (error) {
    logger.warn('Search validation failed', {
      error: error.details[0].message,
      query: req.query
    });

    return res.status(400).json({
      success: false,
      error: {
        message: 'Invalid search parameters',
        details: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message
        })),
        type: 'VALIDATION_ERROR'
      }
    });
  }

  // Replace query with validated data
  req.query = { ...req.query, ...value };
  next();
};

/**
 * Validate ID parameter
 */
export const validateId = (paramName = 'id') => {
  return (req, res, next) => {
    const id = req.params[paramName];
    
    if (!id) {
      return res.status(400).json({
        success: false,
        error: {
          message: `Missing required parameter: ${paramName}`,
          type: 'VALIDATION_ERROR'
        }
      });
    }

    // Validate that ID is a positive integer
    const parsedId = parseInt(id, 10);
    if (isNaN(parsedId) || parsedId <= 0) {
      return res.status(400).json({
        success: false,
        error: {
          message: `Invalid ${paramName}: must be a positive integer`,
          type: 'VALIDATION_ERROR'
        }
      });
    }

    // Add parsed ID to request
    req.params[paramName] = parsedId;
    next();
  };
};

/**
 * Generic validation middleware factory
 */
export const validate = (schema, source = 'body') => {
  return (req, res, next) => {
    const data = source === 'query' ? req.query : 
                  source === 'params' ? req.params : req.body;

    const { error, value } = schema.validate(data, {
      allowUnknown: false,
      stripUnknown: true
    });

    if (error) {
      logger.warn('Validation failed', {
        source,
        error: error.details[0].message,
        data
      });

      return res.status(400).json({
        success: false,
        error: {
          message: `Invalid ${source} data`,
          details: error.details.map(detail => ({
            field: detail.path.join('.'),
            message: detail.message,
            value: detail.context?.value
          })),
          type: 'VALIDATION_ERROR'
        }
      });
    }

    // Replace data with validated version
    if (source === 'query') {
      req.query = value;
    } else if (source === 'params') {
      req.params = value;
    } else {
      req.body = value;
    }

    next();
  };
};

/**
 * Sanitize string input
 */
export const sanitizeString = (str, maxLength = 1000) => {
  if (typeof str !== 'string') return '';
  
  return str
    .trim()
    .substring(0, maxLength)
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/\0/g, ''); // Remove null bytes
};

/**
 * Validate file path
 */
export const validateFilePath = (filePath) => {
  if (typeof filePath !== 'string') return false;
  
  // Check for path traversal attempts
  if (filePath.includes('..') || filePath.includes('~')) return false;
  
  // Check for invalid characters
  if (/[<>:"|?*]/.test(filePath)) return false;
  
  return true;
};

export default {
  validateQuery,
  validateRepository,
  validatePagination,
  validateSearch,
  validateId,
  validate,
  sanitizeString,
  validateFilePath
};
