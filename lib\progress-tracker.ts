/**
 * Simple Progress Tracker
 * Real-time progress updates for planning sessions
 */

interface ProgressEvent {
  sessionId: string
  step: string
  status: 'started' | 'completed' | 'failed'
  progress: number
  timestamp: string
  data?: any
}

class ProgressTracker {
  private listeners: Map<string, Set<(event: ProgressEvent) => void>> = new Map()
  private sessions: Map<string, {
    currentStep?: string
    completedSteps: string[]
    totalSteps: number
    startTime: number
  }> = new Map()

  private readonly TOTAL_STEPS = 13

  /**
   * Start tracking a new session
   */
  startSession(sessionId: string, totalSteps: number = this.TOTAL_STEPS) {
    this.sessions.set(sessionId, {
      completedSteps: [],
      totalSteps,
      startTime: Date.now()
    })
    
    console.log(`📊 Started tracking session: ${sessionId}`)
  }

  /**
   * Track step start
   */
  stepStarted(sessionId: string, step: string) {
    const session = this.sessions.get(sessionId)
    if (!session) return

    session.currentStep = step
    
    const event: ProgressEvent = {
      sessionId,
      step,
      status: 'started',
      progress: Math.round((session.completedSteps.length / session.totalSteps) * 100),
      timestamp: new Date().toISOString()
    }

    this.emit(sessionId, event)
    console.log(`🚀 Step started: ${step} (${event.progress}%)`)
  }

  /**
   * Track step completion
   */
  stepCompleted(sessionId: string, step: string, data?: any) {
    const session = this.sessions.get(sessionId)
    if (!session) return

    if (!session.completedSteps.includes(step)) {
      session.completedSteps.push(step)
    }
    session.currentStep = undefined

    const progress = Math.round((session.completedSteps.length / session.totalSteps) * 100)
    
    const event: ProgressEvent = {
      sessionId,
      step,
      status: 'completed',
      progress,
      timestamp: new Date().toISOString(),
      data
    }

    this.emit(sessionId, event)
    console.log(`✅ Step completed: ${step} (${progress}%)`)

    // Check if session is complete
    if (session.completedSteps.length >= session.totalSteps) {
      this.sessionCompleted(sessionId)
    }
  }

  /**
   * Track step failure
   */
  stepFailed(sessionId: string, step: string, error: string) {
    const session = this.sessions.get(sessionId)
    if (!session) return

    session.currentStep = undefined

    const event: ProgressEvent = {
      sessionId,
      step,
      status: 'failed',
      progress: Math.round((session.completedSteps.length / session.totalSteps) * 100),
      timestamp: new Date().toISOString(),
      data: { error }
    }

    this.emit(sessionId, event)
    console.log(`❌ Step failed: ${step} - ${error}`)
  }

  /**
   * Session completed
   */
  private sessionCompleted(sessionId: string) {
    const session = this.sessions.get(sessionId)
    if (!session) return

    const duration = Date.now() - session.startTime
    console.log(`🎉 Session completed: ${sessionId} (${duration}ms)`)
    
    // Clean up after a delay
    setTimeout(() => {
      this.sessions.delete(sessionId)
      this.listeners.delete(sessionId)
    }, 60000) // Clean up after 1 minute
  }

  /**
   * Subscribe to session events
   */
  subscribe(sessionId: string, callback: (event: ProgressEvent) => void) {
    if (!this.listeners.has(sessionId)) {
      this.listeners.set(sessionId, new Set())
    }
    this.listeners.get(sessionId)!.add(callback)

    // Return unsubscribe function
    return () => {
      const listeners = this.listeners.get(sessionId)
      if (listeners) {
        listeners.delete(callback)
        if (listeners.size === 0) {
          this.listeners.delete(sessionId)
        }
      }
    }
  }

  /**
   * Get current session status
   */
  getSessionStatus(sessionId: string) {
    const session = this.sessions.get(sessionId)
    if (!session) return null

    return {
      sessionId,
      currentStep: session.currentStep,
      completedSteps: session.completedSteps,
      totalSteps: session.totalSteps,
      progress: Math.round((session.completedSteps.length / session.totalSteps) * 100),
      duration: Date.now() - session.startTime,
      isComplete: session.completedSteps.length >= session.totalSteps
    }
  }

  /**
   * Emit event to listeners
   */
  private emit(sessionId: string, event: ProgressEvent) {
    const listeners = this.listeners.get(sessionId)
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(event)
        } catch (error) {
          console.error('Progress listener error:', error)
        }
      })
    }
  }
}

// Global instance
export const progressTracker = new ProgressTracker()

// Helper functions for easy use
export function trackSessionStart(sessionId: string) {
  progressTracker.startSession(sessionId)
}

export function trackStepStart(sessionId: string, step: string) {
  progressTracker.stepStarted(sessionId, step)
}

export function trackStepComplete(sessionId: string, step: string, data?: any) {
  progressTracker.stepCompleted(sessionId, step, data)
}

export function trackStepFailed(sessionId: string, step: string, error: string) {
  progressTracker.stepFailed(sessionId, step, error)
}

export function subscribeToProgress(sessionId: string, callback: (event: ProgressEvent) => void) {
  return progressTracker.subscribe(sessionId, callback)
}

export function getProgressStatus(sessionId: string) {
  return progressTracker.getSessionStatus(sessionId)
}
