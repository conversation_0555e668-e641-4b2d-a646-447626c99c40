import express from 'express';
import { createLogger, logRequest } from '../../utils/logger.js';
import { validateQuery, validateRepository } from '../middleware/validation.js';

const router = express.Router();
const logger = createLogger('ContextRoutes');

/**
 * Context query endpoint
 * POST /api/context/query
 */
router.post('/query', validateQuery, async (req, res) => {
  const startTime = Date.now();
  
  try {
    const { query, options = {} } = req.body;
    const contextEngine = req.app.get('contextEngine');

    logger.info('Processing context query', {
      query: query.substring(0, 100),
      options,
      userAgent: req.get('User-Agent'),
      ip: req.ip
    });

    // Get context from engine
    const result = await contextEngine.getContext(query, {
      ...options,
      originalQuery: query
    });

    const duration = Date.now() - startTime;
    logRequest(req, res, duration);

    res.json({
      success: true,
      data: result,
      meta: {
        processingTime: duration,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    const duration = Date.now() - startTime;
    logger.error('Context query failed', {
      error: error.message,
      duration,
      query: req.body.query?.substring(0, 100)
    });

    res.status(500).json({
      success: false,
      error: {
        message: error.message,
        type: 'CONTEXT_QUERY_ERROR'
      },
      meta: {
        processingTime: duration,
        timestamp: new Date().toISOString()
      }
    });
  }
});

/**
 * Repository processing endpoint
 * POST /api/context/repositories/process
 */
router.post('/repositories/process', validateRepository, async (req, res) => {
  const startTime = Date.now();
  
  try {
    const { repositoryPath, options = {} } = req.body;
    const contextEngine = req.app.get('contextEngine');

    logger.info('Starting repository processing', {
      repositoryPath,
      options,
      userAgent: req.get('User-Agent'),
      ip: req.ip
    });

    // Process repository
    const result = await contextEngine.processCodebase(repositoryPath, options);

    const duration = Date.now() - startTime;
    logRequest(req, res, duration);

    res.json({
      success: true,
      data: result,
      meta: {
        processingTime: duration,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    const duration = Date.now() - startTime;
    logger.error('Repository processing failed', {
      error: error.message,
      duration,
      repositoryPath: req.body.repositoryPath
    });

    res.status(500).json({
      success: false,
      error: {
        message: error.message,
        type: 'REPOSITORY_PROCESSING_ERROR'
      },
      meta: {
        processingTime: duration,
        timestamp: new Date().toISOString()
      }
    });
  }
});

/**
 * Get context engine statistics
 * GET /api/context/statistics
 */
router.get('/statistics', async (req, res) => {
  const startTime = Date.now();
  
  try {
    const contextEngine = req.app.get('contextEngine');
    const statistics = await contextEngine.getStatistics();

    const duration = Date.now() - startTime;
    logRequest(req, res, duration);

    res.json({
      success: true,
      data: statistics,
      meta: {
        processingTime: duration,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    const duration = Date.now() - startTime;
    logger.error('Failed to get statistics', {
      error: error.message,
      duration
    });

    res.status(500).json({
      success: false,
      error: {
        message: error.message,
        type: 'STATISTICS_ERROR'
      },
      meta: {
        processingTime: duration,
        timestamp: new Date().toISOString()
      }
    });
  }
});

/**
 * Search files endpoint
 * GET /api/context/files/search
 */
router.get('/files/search', async (req, res) => {
  const startTime = Date.now();
  
  try {
    const { q: query, language, limit = 20 } = req.query;
    
    if (!query) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Query parameter "q" is required',
          type: 'VALIDATION_ERROR'
        }
      });
    }

    const contextEngine = req.app.get('contextEngine');
    
    // Use the context engine to search for files
    const result = await contextEngine.getContext(query, {
      intent: 'file_search',
      language,
      limit: parseInt(limit)
    });

    const duration = Date.now() - startTime;
    logRequest(req, res, duration);

    res.json({
      success: true,
      data: {
        files: result.results.filter(r => r.type === 'File'),
        total: result.total,
        query: query
      },
      meta: {
        processingTime: duration,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    const duration = Date.now() - startTime;
    logger.error('File search failed', {
      error: error.message,
      duration,
      query: req.query.q
    });

    res.status(500).json({
      success: false,
      error: {
        message: error.message,
        type: 'FILE_SEARCH_ERROR'
      },
      meta: {
        processingTime: duration,
        timestamp: new Date().toISOString()
      }
    });
  }
});

/**
 * Get code element details
 * GET /api/context/elements/:id
 */
router.get('/elements/:id', async (req, res) => {
  const startTime = Date.now();
  
  try {
    const { id } = req.params;
    const contextEngine = req.app.get('contextEngine');

    // Query Neo4j directly for element details
    const query = `
      MATCH (n)
      WHERE id(n) = $id
      OPTIONAL MATCH (f:File)-[:CONTAINS|DEFINES]->(n)
      RETURN n, f
    `;

    const result = await contextEngine.neo4jClient.run(query, { 
      id: parseInt(id) 
    });

    if (result.records.length === 0) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Code element not found',
          type: 'NOT_FOUND'
        }
      });
    }

    const record = result.records[0];
    const element = record.get('n');
    const file = record.get('f');

    const duration = Date.now() - startTime;
    logRequest(req, res, duration);

    res.json({
      success: true,
      data: {
        id: element.identity.toNumber(),
        labels: element.labels,
        properties: element.properties,
        file: file ? {
          path: file.properties.path,
          language: file.properties.language
        } : null
      },
      meta: {
        processingTime: duration,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    const duration = Date.now() - startTime;
    logger.error('Failed to get element details', {
      error: error.message,
      duration,
      elementId: req.params.id
    });

    res.status(500).json({
      success: false,
      error: {
        message: error.message,
        type: 'ELEMENT_DETAILS_ERROR'
      },
      meta: {
        processingTime: duration,
        timestamp: new Date().toISOString()
      }
    });
  }
});

/**
 * Get relationships for a code element
 * GET /api/context/elements/:id/relationships
 */
router.get('/elements/:id/relationships', async (req, res) => {
  const startTime = Date.now();
  
  try {
    const { id } = req.params;
    const { direction = 'both', limit = 50 } = req.query;
    const contextEngine = req.app.get('contextEngine');

    let query;
    if (direction === 'outgoing') {
      query = `
        MATCH (n)-[r]->(m)
        WHERE id(n) = $id
        RETURN r, m
        LIMIT $limit
      `;
    } else if (direction === 'incoming') {
      query = `
        MATCH (m)-[r]->(n)
        WHERE id(n) = $id
        RETURN r, m
        LIMIT $limit
      `;
    } else {
      query = `
        MATCH (n)-[r]-(m)
        WHERE id(n) = $id
        RETURN r, m
        LIMIT $limit
      `;
    }

    const result = await contextEngine.neo4jClient.run(query, { 
      id: parseInt(id),
      limit: parseInt(limit)
    });

    const relationships = result.records.map(record => {
      const relationship = record.get('r');
      const relatedNode = record.get('m');
      
      return {
        id: relationship.identity.toNumber(),
        type: relationship.type,
        properties: relationship.properties,
        relatedNode: {
          id: relatedNode.identity.toNumber(),
          labels: relatedNode.labels,
          properties: relatedNode.properties
        }
      };
    });

    const duration = Date.now() - startTime;
    logRequest(req, res, duration);

    res.json({
      success: true,
      data: {
        relationships,
        total: relationships.length,
        direction,
        elementId: parseInt(id)
      },
      meta: {
        processingTime: duration,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    const duration = Date.now() - startTime;
    logger.error('Failed to get element relationships', {
      error: error.message,
      duration,
      elementId: req.params.id
    });

    res.status(500).json({
      success: false,
      error: {
        message: error.message,
        type: 'RELATIONSHIPS_ERROR'
      },
      meta: {
        processingTime: duration,
        timestamp: new Date().toISOString()
      }
    });
  }
});

export default router;
