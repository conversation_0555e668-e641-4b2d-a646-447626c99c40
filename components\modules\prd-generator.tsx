"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { FileText, Users, Target, Zap } from "lucide-react"
import type { ModuleProps } from "@/types/planning"

interface PRDSection {
  title: string
  content: string
  icon: any
}

export function PRDGenerator({ context, onComplete }: ModuleProps) {
  const [prd, setPrd] = useState<Record<string, string>>({})
  const [isGenerating, setIsGenerating] = useState(true)

  useEffect(() => {
    generatePRD()
  }, [])

  const generatePRD = async () => {
    setIsGenerating(true)

    try {
      // This component now receives AI-generated PRD data from the planning agent
      // The actual AI generation happens in the planning API, not here
      const prdData = context?.results?.prd || context?.prd || {}

      if (Object.keys(prdData).length === 0) {
        // If no PRD data available, show error
        setPrd({
          overview: "Error: No PRD data available. Please ensure the AI planning process completed successfully.",
          error: "Missing PRD data from AI planning agent"
        })
      } else {
        setPrd(prdData)
      }
    } catch (error) {
      console.error("PRD display failed:", error)
      setPrd({
        overview: "Error: Unable to display PRD. Please check AI service configuration.",
        error: "AI service unavailable"
      })
    }

    setIsGenerating(false)
  }

  const createPRD = (): Record<string, string> => {
    const { originalPrompt, summary, clarifications, features, techStack } = context || {}

    return {
      overview: `Product Requirements Document

Project: ${originalPrompt}

${summary}`,

      objectives: `Product Objectives:

1. Primary Goal: Deliver a ${context?.projectType} that addresses the core need described in "${originalPrompt}"

2. User Experience: Provide an intuitive and efficient interface for ${clarifications?.target_users || "users"}

3. Technical Excellence: Build a scalable solution using modern technologies (${Object.values(techStack || {}).join(", ")})

4. Performance: Ensure fast loading times and responsive interactions across ${clarifications?.platform || "all platforms"}`,

      features: `Functional Requirements:

Core Features:
${features?.map((feature, index) => `${index + 1}. ${feature}`).join("\n") || "1. Primary functionality as described"}

User Management:
${
  clarifications?.authentication === "Yes, full user accounts"
    ? "• User registration and login\n• Profile management\n• Password reset functionality"
    : clarifications?.authentication === "Yes, simple login"
      ? "• Basic authentication system\n• Session management"
      : "• No authentication required"
}

Data Management:
• ${clarifications?.data_storage || "Basic data persistence"}
• Data validation and error handling
• Backup and recovery procedures

${clarifications?.integrations ? `Third-party Integrations:\n• ${clarifications.integrations}` : ""}`,

      userStories: `User Stories:

As a ${clarifications?.target_users || "user"}, I want to:

1. Access the main functionality so that I can ${features?.[0]?.toLowerCase() || "accomplish my primary goal"}

2. Navigate the interface intuitively so that I can complete tasks efficiently

3. ${clarifications?.authentication?.includes("Yes") ? "Securely log in and manage my account so that my data is protected" : "Use the application without barriers so that I can focus on my tasks"}

4. Access the application on ${clarifications?.platform || "my preferred platform"} so that I can use it when needed

5. Receive feedback on my actions so that I understand the system's response`,

      technical: `Technical Requirements:

Architecture:
• Frontend: ${techStack?.Frontend || techStack?.Mobile || "Modern web framework"}
• Backend: ${techStack?.Backend || "Server-side application"}
• Database: ${techStack?.Database || "Appropriate data storage"}
• Hosting: ${techStack?.Hosting || "Cloud hosting platform"}
${techStack?.Authentication ? `• Authentication: ${techStack.Authentication}` : ""}

Performance Requirements:
• Page load time: < 3 seconds
• API response time: < 500ms
• Uptime: 99.9%
• Mobile responsiveness: All screen sizes

Security Requirements:
${
  clarifications?.authentication?.includes("Yes")
    ? "• Secure user authentication\n• Data encryption in transit and at rest\n• Input validation and sanitization"
    : "• Basic security measures\n• Input validation"
}

Scalability:
• Support for concurrent users
• Database optimization
• Caching strategies`,

      acceptance: `Acceptance Criteria:

Functional Criteria:
✓ All core features work as specified
✓ User interface is intuitive and responsive
✓ Data is stored and retrieved correctly
${clarifications?.authentication?.includes("Yes") ? "✓ Authentication system is secure and functional" : ""}

Technical Criteria:
✓ Application loads within performance requirements
✓ No critical bugs or security vulnerabilities
✓ Code follows best practices and is well-documented
✓ Application works on ${clarifications?.platform || "target platforms"}

User Experience Criteria:
✓ Users can complete primary tasks without confusion
✓ Error messages are clear and helpful
✓ Application provides appropriate feedback for user actions`,
    }
  }

  const handleContinue = () => {
    onComplete(prd)
  }

  const sections: PRDSection[] = [
    { title: "Overview", content: prd.overview, icon: FileText },
    { title: "Objectives", content: prd.objectives, icon: Target },
    { title: "Features", content: prd.features, icon: Zap },
    { title: "User Stories", content: prd.userStories, icon: Users },
    { title: "Technical", content: prd.technical, icon: FileText },
    { title: "Acceptance", content: prd.acceptance, icon: FileText },
  ]

  return (
    <Card className="border-slate-700" style={{backgroundColor: '#818181'}}>
      <CardHeader>
        <div className="flex items-center gap-2">
          <FileText className="w-5 h-5 text-red-400" />
          <CardTitle className="text-white">Product Requirements Document</CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        {isGenerating ? (
          <div className="flex items-center justify-center py-12">
            <div className="flex items-center gap-3">
              <FileText className="w-5 h-5 text-red-400 animate-pulse" />
              <span className="text-gray-300">Generating comprehensive PRD...</span>
            </div>
          </div>
        ) : (
          <>
            <Tabs defaultValue="overview" className="w-full">
              <TabsList className="grid w-full grid-cols-3 lg:grid-cols-6 bg-slate-700">
                {sections.map((section) => (
                  <TabsTrigger
                    key={section.title.toLowerCase()}
                    value={section.title.toLowerCase()}
                    className="text-xs"
                  >
                    {section.title}
                  </TabsTrigger>
                ))}
              </TabsList>

              {sections.map((section) => (
                <TabsContent key={section.title.toLowerCase()} value={section.title.toLowerCase()} className="mt-4">
                  <div className="bg-slate-700/50 p-4 rounded-lg">
                    <pre className="text-gray-300 whitespace-pre-wrap font-sans text-sm">{section.content}</pre>
                  </div>
                </TabsContent>
              ))}
            </Tabs>

            <Button onClick={handleContinue} className="w-full mt-6 bg-red-600 hover:bg-red-700">
              Approve PRD & Continue
            </Button>
          </>
        )}
      </CardContent>
    </Card>
  )
}
