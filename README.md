# Planning Agent

An intelligent AI-powered project planning tool that helps you transform ideas into comprehensive project plans with detailed implementation guidance.

## Features

- **AI-Powered Analysis**: Uses Claude 3.5 Sonnet via OpenRouter for intelligent project analysis
- **Interactive & Autonomous Modes**: Choose between guided questions or fully automated planning
- **Image-Based Design Analysis**: Upload reference images to automatically generate comprehensive style guides using Google Gemini 2.5 Pro
- **Comprehensive Planning**: Covers all aspects from requirements to implementation
- **Real-time Processing**: Watch as each planning step completes in real-time
- **Professional Output**: Generates detailed documentation, wireframes, and implementation guides

## New: Image Upload Feature

The Planning Agent now supports uploading reference images to automatically generate detailed design style guides:

### How it works:
1. **Upload Images**: Click the paperclip icon in the input field to upload design reference images
2. **AI Analysis**: Images are analyzed by Google Gemini 2.5 Pro to extract comprehensive design information
3. **Style Guide Generation**: A detailed style guide is automatically created covering:
   - Color palettes with hex codes and RGB values
   - Typography specifications (fonts, weights, sizes, spacing)
   - Layout and spacing patterns
   - Component styling details
   - Visual effects and design principles

### Benefits:
- **Skip Manual Design Work**: No need to manually create design guidelines
- **Accurate Analysis**: AI extracts precise color codes, measurements, and styling details
- **Professional Documentation**: Generated style guides are developer-ready
- **Visual Context**: The planning process considers your specific design preferences

## Planning Steps

1. **Analyze Requirements** - Extract project details and scope
2. **Gather Details** - Interactive clarification questions (if enabled)
3. **Generate Summary** - Comprehensive project overview
4. **Select Tech Stack** - Recommended technologies and frameworks
5. **Create Requirements** - Detailed PRD with specifications
6. **Design Wireframes** - ASCII-based UI layouts
7. **Design Guidelines** - Style guide (auto-generated from images if uploaded)
8. **Database Schema** - Data structure and relationships
9. **File Structure** - Organized project architecture
10. **Workflow Logic** - Business process definitions
11. **Implementation Tasks** - Detailed development breakdown
12. **Project Scaffold** - Ready-to-use code structure

## Setup

1. Clone the repository
2. Install dependencies: `npm install`
3. Set up environment variables:
   ```
   OPENROUTER_API_KEY=your_openrouter_api_key
   ```
4. Run the development server: `npm run dev`
5. Open [http://localhost:3000](http://localhost:3000)

## Usage

1. Enter your project idea in the input field
2. Optionally upload reference images using the paperclip icon
3. Choose between Interactive or Autonomous mode
4. Watch as the AI processes each planning step
5. Review the comprehensive results with all planning artifacts

## Technology Stack

- **Frontend**: Next.js 15, React, TypeScript, Tailwind CSS
- **AI Services**: 
  - OpenRouter API (Claude 3.5 Sonnet for planning)
  - Google Gemini 2.5 Pro (for image analysis)
- **UI Components**: Radix UI, Lucide Icons, Framer Motion
- **Styling**: Tailwind CSS with custom red theme

## Environment Variables

- `OPENROUTER_API_KEY`: Your OpenRouter API key for AI services
- `NEXT_PUBLIC_SITE_URL`: Your site URL (for OpenRouter headers)
