# Neo4j Configuration
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=password
NEO4J_DATABASE=neo4j

# Vector Database Configuration (Pinecone)
PINECONE_API_KEY=your_pinecone_api_key
PINECONE_ENVIRONMENT=us-west1-gcp-free
PINECONE_INDEX_NAME=code-context-embeddings

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# Application Configuration
NODE_ENV=development
PORT=3000
LOG_LEVEL=info

# API Configuration
API_KEY_SECRET=your_secret_key_for_api_authentication
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Processing Configuration
MAX_CONCURRENT_PROCESSES=5
BATCH_SIZE=100
EMBEDDING_MODEL=text-embedding-ada-002

# Monitoring Configuration
METRICS_ENABLED=true
HEALTH_CHECK_INTERVAL=30000

# Development Configuration
DEBUG=context-engine:*
ENABLE_CORS=true
