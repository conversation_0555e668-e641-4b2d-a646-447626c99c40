@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  color-scheme: dark;
}

body {
  @apply bg-black text-white;
}

/* Hide scrollbar but keep scroll functionality */
.scrollbar-hide {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}

/* Specifically hide scrollbar for textarea in input area */
textarea.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
textarea.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Custom scrollbar styles for textarea (only for non-hidden scrollbars) */
textarea:not(.scrollbar-hide)::-webkit-scrollbar {
  width: 4px;
}

textarea:not(.scrollbar-hide)::-webkit-scrollbar-track {
  background: transparent;
}

textarea:not(.scrollbar-hide)::-webkit-scrollbar-thumb {
  background: rgba(209, 213, 219, 0.8);
  border-radius: 2px;
}

textarea:not(.scrollbar-hide)::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.9);
}

/* Firefox scrollbar for textarea (only for non-hidden scrollbars) */
textarea:not(.scrollbar-hide) {
  scrollbar-width: thin;
  scrollbar-color: rgba(209, 213, 219, 0.8) transparent;
}

/* Custom Switch Styling for Better Visibility */
[data-state="checked"] {
  background-color: #ff2d55 !important;
}

[data-state="unchecked"] {
  background-color: #4b5563 !important;
}

/* Switch thumb styling */
[data-state="checked"] [data-state="checked"] {
  background-color: white !important;
}

[data-state="unchecked"] [data-state="unchecked"] {
  background-color: #d1d5db !important;
}

/* Dropdown/Select styling for black background */
[data-radix-select-content] {
  background-color: #000000 !important;
  border-color: #4b5563 !important;
}

[data-radix-select-item] {
  color: white !important;
}

[data-radix-select-item]:hover,
[data-radix-select-item][data-highlighted] {
  background-color: #374151 !important;
}

/* Custom scrollbar for planning tabs */
.planning-scroll::-webkit-scrollbar {
  width: 6px;
}

.planning-scroll::-webkit-scrollbar-track {
  background: #0a0a0a;
  border-radius: 3px;
}

.planning-scroll::-webkit-scrollbar-thumb {
  background: #333;
  border-radius: 3px;
}

.planning-scroll::-webkit-scrollbar-thumb:hover {
  background: #444;
}

/* Firefox scrollbar for planning tabs */
.planning-scroll {
  scrollbar-width: thin;
  scrollbar-color: #333 #0a0a0a;
  scroll-behavior: smooth;
}

/* Custom scrollbar for chat textarea */
textarea::-webkit-scrollbar {
  width: 6px;
}

textarea::-webkit-scrollbar-track {
  background: transparent;
}

textarea::-webkit-scrollbar-thumb {
  background: #666;
  border-radius: 3px;
}

textarea::-webkit-scrollbar-thumb:hover {
  background: #777;
}

/* Mobile responsive utilities */
@media (max-width: 768px) {
  /* Ensure mobile viewport is properly handled */
  .mobile-full-width {
    width: 100vw !important;
  }

  /* Prevent horizontal scroll on mobile */
  body {
    overflow-x: hidden;
  }

  /* Mobile-friendly touch targets */
  button, .clickable {
    min-height: 44px;
    min-width: 44px;
  }

  /* Adjust text sizes for mobile readability */
  .mobile-text-adjust {
    font-size: 16px; /* Prevents zoom on iOS */
  }

  /* Safe area support for mobile devices */
  .safe-area-pb {
    padding-bottom: env(safe-area-inset-bottom);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
