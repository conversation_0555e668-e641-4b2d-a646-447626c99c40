"use client"

import React, { useState, useEffect } from "react"
import { CheckCircle, Circle, Loader2, Brain, Zap, Target, Settings, ChevronDown, ChevronRight } from "lucide-react"
// import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>oolHeader, AIToolContent } from "@/components/ui/kibo-ui/ai"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

interface PlanningStep {
  id: string
  title: string
  description: string
  status: "pending" | "running" | "completed" | "error"
  progress?: number
  substeps?: PlanningStep[]
  estimatedTime?: string
  dependencies?: string[]
}

interface AgentPlanningProps {
  mode: "full" | "compact"
  steps: PlanningStep[]
  title?: string
  onStepClick?: (stepId: string) => void
  onComplete?: () => void
  className?: string
}

const defaultSteps: PlanningStep[] = [
  // Empty by default - steps will be populated by backend
]

export default function AgentPlanning({
  mode = "full",
  steps = defaultSteps,
  title = "Project Planning",
  onStepClick,
  onComplete,
  className = ""
}: AgentPlanningProps) {
  const [expandedSteps, setExpandedSteps] = useState<Set<string>>(new Set(["design"]))
  const [currentStep, setCurrentStep] = useState<string>("design")

  useEffect(() => {
    const runningStep = steps.find(step => step.status === "running")
    if (runningStep) {
      setCurrentStep(runningStep.id)
      setExpandedSteps(prev => new Set([...prev, runningStep.id]))
    }
  }, [steps])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="w-4 h-4 text-green-400" />
      case "running":
        return <Loader2 className="w-4 h-4 animate-spin text-blue-400" />
      case "error":
        return <Circle className="w-4 h-4 text-red-400" />
      default:
        return <Circle className="w-4 h-4 text-gray-400" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "text-green-400"
      case "running":
        return "text-blue-400"
      case "error":
        return "text-red-400"
      default:
        return "text-gray-400"
    }
  }

  const toggleStepExpansion = (stepId: string) => {
    setExpandedSteps(prev => {
      const newSet = new Set(prev)
      if (newSet.has(stepId)) {
        newSet.delete(stepId)
      } else {
        newSet.add(stepId)
      }
      return newSet
    })
  }

  const completedSteps = steps.filter(step => step.status === "completed").length
  const totalSteps = steps.length
  const overallProgress = totalSteps > 0 ? (completedSteps / totalSteps) * 100 : 0

  if (mode === "compact") {
    return (
      <div className={`bg-[#181818] rounded-lg p-4 space-y-3 ${className}`}>
        <div className="flex items-center gap-3">
          <Brain className="w-4 h-4 text-blue-400" />
          <span className="text-sm font-medium text-white">{title}</span>
          <Badge variant="secondary" className="text-xs bg-[#2a2a2a] text-gray-300">
            {totalSteps > 0 ? `${completedSteps}/${totalSteps}` : 'Ready'}
          </Badge>
        </div>
        
        <div className="space-y-2">
          {steps.length === 0 ? (
            <div className="text-center py-4">
              <div className="text-xs text-gray-400">Waiting for backend...</div>
            </div>
          ) : (
            steps.map((step) => (
            <div 
              key={step.id} 
              className="flex items-center gap-3 p-2 rounded-lg hover:bg-[#1f1f1f] transition-colors cursor-pointer"
              onClick={() => onStepClick?.(step.id)}
            >
              {getStatusIcon(step.status)}
              <span className={`text-sm ${getStatusColor(step.status)}`}>
                {step.title}
              </span>
              {step.status === "running" && step.progress && (
                <div className="flex-1 ml-2">
                  <div className="w-full bg-[#2a2a2a] rounded-full h-1">
                    <div
                      className="bg-blue-400 h-1 rounded-full transition-all duration-500"
                      style={{ width: `${step.progress}%` }}
                    />
                  </div>
                </div>
              )}
            </div>
          )))
          }
        </div>

        {overallProgress === 100 && onComplete && (
          <Button 
            onClick={onComplete}
            className="w-full bg-green-600 hover:bg-green-700 text-white text-sm"
          >
            View Complete Plan
          </Button>
        )}
      </div>
    )
  }

  return (
    <div className={`bg-[#0a0a0a] rounded-xl p-6 space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-3">
            <Brain className="w-6 h-6 text-blue-400" />
            <h2 className="text-xl font-semibold text-white">{title}</h2>
          </div>
          <Badge variant="secondary" className="bg-[#1a1a1a] text-gray-300">
            Full-Stack Development
          </Badge>
        </div>
        
        <div className="flex items-center gap-4">
          <div className="text-sm text-gray-400">
            Progress: {totalSteps > 0 ? `${completedSteps}/${totalSteps} steps` : 'Waiting for backend'}
          </div>
          <div className="w-32 bg-[#1a1a1a] rounded-full h-2">
            <div
              className="bg-gradient-to-r from-blue-400 to-green-400 h-2 rounded-full transition-all duration-500"
              style={{ width: `${overallProgress}%` }}
            />
          </div>
        </div>
      </div>

      {/* Planning Steps */}
      <ScrollArea className="h-[600px]">
        <div className="space-y-4 pr-4">
          {steps.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-gray-400 mb-2">Waiting for planning data...</div>
            <div className="text-sm text-gray-500">Connect to backend to see real planning steps</div>
          </div>
        ) : (
          steps.map((step, index) => (
            <div key={step.id} className="relative">
              {/* Connection Line */}
              {index < steps.length - 1 && (
                <div className="absolute left-6 top-12 w-0.5 h-8 bg-[#2a2a2a]" />
              )}
              
              <div className="bg-[#111111] rounded-xl border border-[#1a1a1a] overflow-hidden">
                <div 
                  className="p-4 cursor-pointer hover:bg-[#151515] transition-colors"
                  onClick={() => toggleStepExpansion(step.id)}
                >
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-3">
                      {getStatusIcon(step.status)}
                      <div className="flex items-center gap-2">
                        {expandedSteps.has(step.id) ? 
                          <ChevronDown className="w-4 h-4 text-gray-400" /> : 
                          <ChevronRight className="w-4 h-4 text-gray-400" />
                        }
                      </div>
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-1">
                        <h3 className={`font-medium ${getStatusColor(step.status)}`}>
                          {step.title}
                        </h3>
                        {step.estimatedTime && (
                          <Badge variant="outline" className="text-xs border-gray-600 text-gray-400">
                            {step.estimatedTime}
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-gray-400">{step.description}</p>
                      
                      {step.status === "running" && step.progress && (
                        <div className="mt-3">
                          <div className="w-full bg-[#2a2a2a] rounded-full h-2">
                            <div
                              className="bg-gradient-to-r from-blue-400 to-blue-500 h-2 rounded-full transition-all duration-500"
                              style={{ width: `${step.progress}%` }}
                            />
                          </div>
                          <p className="text-xs text-gray-400 mt-1">{step.progress}% complete</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Substeps */}
                {expandedSteps.has(step.id) && step.substeps && (
                  <div className="border-t border-[#1a1a1a] bg-[#0f0f0f] p-4">
                    <div className="space-y-3">
                      {step.substeps.map((substep) => (
                        <div key={substep.id} className="flex items-center gap-3 p-2 rounded-lg hover:bg-[#1a1a1a] transition-colors">
                          {getStatusIcon(substep.status)}
                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <span className={`text-sm font-medium ${getStatusColor(substep.status)}`}>
                                {substep.title}
                              </span>
                            </div>
                            <p className="text-xs text-gray-500 mt-1">{substep.description}</p>
                            {substep.status === "running" && substep.progress && (
                              <div className="mt-2">
                                <div className="w-full bg-[#2a2a2a] rounded-full h-1">
                                  <div
                                    className="bg-blue-400 h-1 rounded-full transition-all duration-300"
                                    style={{ width: `${substep.progress}%` }}
                                  />
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )))
        }
        </div>
      </ScrollArea>

      {/* Action Buttons */}
      {overallProgress === 100 && (
        <div className="flex items-center gap-3 pt-4 border-t border-[#1a1a1a]">
          <Button className="bg-green-600 hover:bg-green-700 text-white">
            <Zap className="w-4 h-4 mr-2" />
            Deploy Project
          </Button>
          <Button variant="outline" className="border-gray-600 text-gray-300 hover:bg-[#1a1a1a]">
            <Target className="w-4 h-4 mr-2" />
            Test Application
          </Button>
          <Button variant="outline" className="border-gray-600 text-gray-300 hover:bg-[#1a1a1a]">
            <Settings className="w-4 h-4 mr-2" />
            Configure
          </Button>
        </div>
      )}
    </div>
  )
}
