#!/usr/bin/env node

/**
 * MCP Setup Script for AG3NT System
 * Configures Context7 and Sequential Thinking MCP servers
 */

import fs from 'fs'
import path from 'path'
import { execSync } from 'child_process'

interface MCPConfig {
  mcpServers: Record<string, any>
}

/**
 * MCP Setup Manager
 */
export class MCPSetupManager {
  private configPaths = {
    claude: path.join(process.env.HOME || process.env.USERPROFILE || '', '.config', 'claude', 'claude_desktop_config.json'),
    cursor: path.join(process.env.HOME || process.env.USERPROFILE || '', '.cursor', 'mcp_config.json'),
    vscode: path.join(process.env.HOME || process.env.USERPROFILE || '', '.vscode', 'settings.json'),
    ag3nt: path.join(process.cwd(), 'config', 'mcp-config.json')
  }

  /**
   * Setup MCP for AG3NT system
   */
  async setupAG3NT(): Promise<void> {
    console.log('🚀 Setting up MCP for AG3NT System...\n')

    // 1. Create AG3NT MCP configuration
    const ag3ntConfig = this.createAG3NTConfig()
    this.writeConfig(this.configPaths.ag3nt, ag3ntConfig)
    console.log('✅ AG3NT MCP configuration created')

    // 2. Test MCP servers
    await this.testMCPServers()

    // 3. Generate integration examples
    this.generateIntegrationExamples()

    console.log('\n🎉 MCP setup complete for AG3NT!')
    console.log('\n📋 Next steps:')
    console.log('1. Run: npm run test:mcp')
    console.log('2. Check examples in: examples/universal-mcp-usage.ts')
    console.log('3. Configure your IDE using the generated configs')
  }

  /**
   * Setup MCP for external IDEs
   */
  async setupIDEs(): Promise<void> {
    console.log('🔧 Setting up MCP for external IDEs...\n')

    const configs = {
      claude: this.createClaudeConfig(),
      cursor: this.createCursorConfig(),
      vscode: this.createVSCodeConfig()
    }

    Object.entries(configs).forEach(([ide, config]) => {
      const configPath = this.configPaths[ide as keyof typeof this.configPaths]
      try {
        this.writeConfig(configPath, config)
        console.log(`✅ ${ide.toUpperCase()} MCP configuration created at: ${configPath}`)
      } catch (error) {
        console.log(`⚠️ Could not create ${ide.toUpperCase()} config: ${error}`)
      }
    })

    console.log('\n📋 IDE Setup Instructions:')
    console.log('Claude Desktop: Restart Claude Desktop to load new MCP servers')
    console.log('Cursor: Restart Cursor and check MCP status in settings')
    console.log('VS Code: Install MCP extension and restart VS Code')
  }

  /**
   * Create AG3NT MCP configuration
   */
  private createAG3NTConfig(): MCPConfig {
    return {
      mcpServers: {
        "Context7": {
          command: "npx",
          args: ["-y", "@upstash/context7-mcp@latest"],
          timeout: 30000,
          description: "Real-time documentation access for AG3NT agents",
          capabilities: ["documentation", "code_examples", "library_search"],
          enabled: true
        },
        "sequential-thinking": {
          command: "npx",
          args: ["-y", "@modelcontextprotocol/server-sequential-thinking"],
          timeout: 30000,
          description: "Advanced reasoning for AG3NT planning and execution",
          capabilities: ["reasoning", "problem_solving", "thought_branching"],
          enabled: true
        },
        "ag3nt-context": {
          command: "node",
          args: ["./lib/mcp-server.js"],
          timeout: 30000,
          description: "AG3NT-specific context and project management",
          capabilities: ["project_context", "agent_coordination", "workflow_management"],
          enabled: true,
          env: {
            "AG3NT_MODE": "mcp_server",
            "NODE_ENV": "production"
          }
        }
      }
    }
  }

  /**
   * Create Claude Desktop configuration
   */
  private createClaudeConfig(): MCPConfig {
    return {
      mcpServers: {
        "Context7": {
          command: "npx",
          args: ["-y", "@upstash/context7-mcp@latest"]
        },
        "sequential-thinking": {
          command: "npx",
          args: ["-y", "@modelcontextprotocol/server-sequential-thinking"]
        }
      }
    }
  }

  /**
   * Create Cursor configuration
   */
  private createCursorConfig(): MCPConfig {
    return {
      mcpServers: {
        "context7": {
          command: "npx",
          args: ["-y", "@upstash/context7-mcp@latest"]
        },
        "sequential-thinking": {
          command: "npx",
          args: ["-y", "@modelcontextprotocol/server-sequential-thinking"]
        }
      }
    }
  }

  /**
   * Create VS Code configuration
   */
  private createVSCodeConfig(): any {
    return {
      "mcp": {
        "servers": {
          "context7": {
            "type": "stdio",
            "command": "npx",
            "args": ["-y", "@upstash/context7-mcp@latest"]
          },
          "sequential-thinking": {
            "type": "stdio",
            "command": "npx",
            "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]
          }
        }
      }
    }
  }

  /**
   * Write configuration to file
   */
  private writeConfig(configPath: string, config: any): void {
    const dir = path.dirname(configPath)
    
    // Create directory if it doesn't exist
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true })
    }

    // Write configuration
    fs.writeFileSync(configPath, JSON.stringify(config, null, 2))
  }

  /**
   * Test MCP servers
   */
  private async testMCPServers(): Promise<void> {
    console.log('\n🧪 Testing MCP servers...')

    const tests = [
      {
        name: 'Context7',
        command: 'npx -y @upstash/context7-mcp@latest --version',
        description: 'Testing Context7 MCP server'
      },
      {
        name: 'Sequential Thinking',
        command: 'npx -y @modelcontextprotocol/server-sequential-thinking --help',
        description: 'Testing Sequential Thinking MCP server'
      }
    ]

    for (const test of tests) {
      try {
        console.log(`  Testing ${test.name}...`)
        execSync(test.command, { stdio: 'pipe', timeout: 10000 })
        console.log(`  ✅ ${test.name} is working`)
      } catch (error) {
        console.log(`  ❌ ${test.name} test failed: ${error}`)
      }
    }
  }

  /**
   * Generate integration examples
   */
  private generateIntegrationExamples(): void {
    console.log('\n📝 Generating integration examples...')

    const examples = {
      'package.json': this.generatePackageJsonScript(),
      'docker-compose.yml': this.generateDockerCompose(),
      'mcp-test.ts': this.generateTestScript()
    }

    Object.entries(examples).forEach(([filename, content]) => {
      const filePath = path.join(process.cwd(), 'examples', filename)
      const dir = path.dirname(filePath)
      
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true })
      }
      
      fs.writeFileSync(filePath, content)
      console.log(`  ✅ Created: ${filePath}`)
    })
  }

  /**
   * Generate package.json scripts
   */
  private generatePackageJsonScript(): string {
    return JSON.stringify({
      scripts: {
        "mcp:context7": "npx -y @upstash/context7-mcp@latest",
        "mcp:thinking": "npx -y @modelcontextprotocol/server-sequential-thinking",
        "mcp:test": "node -e \"console.log('Testing MCP servers...'); require('./examples/universal-mcp-usage.ts').demonstrateUniversalMCP()\"",
        "mcp:inspector": "npx -y @modelcontextprotocol/inspector npx @upstash/context7-mcp@latest"
      }
    }, null, 2)
  }

  /**
   * Generate Docker Compose for MCP servers
   */
  private generateDockerCompose(): string {
    return `version: '3.8'
services:
  context7-mcp:
    image: node:18-alpine
    command: npx -y @upstash/context7-mcp@latest
    ports:
      - "3001:3000"
    environment:
      - NODE_ENV=production
    restart: unless-stopped

  sequential-thinking-mcp:
    image: node:18-alpine
    command: npx -y @modelcontextprotocol/server-sequential-thinking
    ports:
      - "3002:3000"
    environment:
      - NODE_ENV=production
    restart: unless-stopped

  ag3nt-system:
    build: .
    ports:
      - "3000:3000"
    environment:
      - MCP_CONTEXT7_URL=http://context7-mcp:3000
      - MCP_THINKING_URL=http://sequential-thinking-mcp:3000
    depends_on:
      - context7-mcp
      - sequential-thinking-mcp
    restart: unless-stopped
`
  }

  /**
   * Generate test script
   */
  private generateTestScript(): string {
    return `import { UniversalMCPIntegration } from '../lib/universal-mcp-integration'

async function testMCP() {
  console.log('🧪 Testing Universal MCP Integration...')
  
  const mcp = new UniversalMCPIntegration()
  
  // Test Context7
  try {
    const docs = await mcp.getDocumentation('React', 'hooks')
    console.log('✅ Context7 working:', !!docs)
  } catch (error) {
    console.log('❌ Context7 failed:', error.message)
  }
  
  // Test Sequential Thinking
  try {
    const thought = await mcp.think('Testing sequential thinking capability')
    console.log('✅ Sequential Thinking working:', !!thought)
  } catch (error) {
    console.log('❌ Sequential Thinking failed:', error.message)
  }
  
  // Test connections
  const connections = await mcp.testConnections()
  console.log('🔌 Connection Status:', connections)
}

testMCP().catch(console.error)
`
  }
}

/**
 * CLI Interface
 */
async function main() {
  const args = process.argv.slice(2)
  const command = args[0] || 'setup'

  const setupManager = new MCPSetupManager()

  switch (command) {
    case 'setup':
    case 'ag3nt':
      await setupManager.setupAG3NT()
      break
    case 'ides':
      await setupManager.setupIDEs()
      break
    case 'all':
      await setupManager.setupAG3NT()
      await setupManager.setupIDEs()
      break
    default:
      console.log('Usage: npm run setup:mcp [command]')
      console.log('Commands:')
      console.log('  setup, ag3nt  - Setup MCP for AG3NT system (default)')
      console.log('  ides          - Setup MCP for external IDEs')
      console.log('  all           - Setup MCP for both AG3NT and IDEs')
  }
}

if (require.main === module) {
  main().catch(console.error)
}

export { MCPSetupManager }
`
