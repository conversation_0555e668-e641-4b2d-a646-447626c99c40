/**
 * Universal MCP Integration for AG3NT System
 * Integrates Context7 and Sequential Thinking MCP servers for all agents
 */

import { MCPIntegration } from "./mcp-integration"

export interface UniversalMCPConfig {
  context7: {
    enabled: boolean
    command: string
    args: string[]
    timeout?: number
  }
  sequentialThinking: {
    enabled: boolean
    command: string
    args: string[]
    timeout?: number
  }
  customServers: MCPServerConfig[]
}

export interface MCPServerConfig {
  name: string
  command: string
  args: string[]
  capabilities: string[]
  enabled: boolean
  timeout?: number
  env?: Record<string, string>
}

/**
 * Universal MCP Integration Manager
 * Provides Context7 and Sequential Thinking to all AG3NT agents
 */
export class UniversalMCPIntegration extends MCPIntegration {
  private config: UniversalMCPConfig

  constructor(config?: Partial<UniversalMCPConfig>) {
    super()
    
    this.config = {
      context7: {
        enabled: true,
        command: "npx",
        args: ["-y", "@upstash/context7-mcp@latest"],
        timeout: 30000
      },
      sequentialThinking: {
        enabled: true,
        command: "npx",
        args: ["-y", "@modelcontextprotocol/server-sequential-thinking"],
        timeout: 30000
      },
      customServers: [],
      ...config
    }

    this.initializeUniversalServers()
  }

  /**
   * Initialize universal MCP servers for all AG3NT agents
   */
  private initializeUniversalServers(): void {
    // Context7 Documentation Server
    if (this.config.context7.enabled) {
      this.registerServer({
        name: 'context7-docs',
        version: '1.0.0',
        capabilities: { tools: true, resources: true },
        tools: [
          {
            name: 'resolve_library_id',
            description: 'Resolve a library name to Context7-compatible library ID',
            inputSchema: {
              type: 'object',
              properties: {
                libraryName: { 
                  type: 'string',
                  description: 'Library name to search for and retrieve a Context7-compatible library ID'
                }
              },
              required: ['libraryName']
            },
            handler: this.resolveLibraryId.bind(this)
          },
          {
            name: 'get_library_docs',
            description: 'Fetch up-to-date documentation for a library',
            inputSchema: {
              type: 'object',
              properties: {
                context7CompatibleLibraryID: {
                  type: 'string',
                  description: 'Exact Context7-compatible library ID (e.g., /mongodb/docs, /vercel/next.js)'
                },
                topic: {
                  type: 'string',
                  description: 'Topic to focus documentation on (e.g., hooks, routing)'
                },
                tokens: {
                  type: 'number',
                  description: 'Maximum number of tokens of documentation to retrieve (default: 10000)',
                  default: 10000
                }
              },
              required: ['context7CompatibleLibraryID']
            },
            handler: this.getLibraryDocs.bind(this)
          },
          {
            name: 'search_documentation',
            description: 'Search for documentation across multiple libraries',
            inputSchema: {
              type: 'object',
              properties: {
                query: { type: 'string', description: 'Search query for documentation' },
                libraries: { 
                  type: 'array', 
                  items: { type: 'string' },
                  description: 'Specific libraries to search in (optional)'
                },
                maxResults: { type: 'number', default: 5 }
              },
              required: ['query']
            },
            handler: this.searchDocumentation.bind(this)
          }
        ],
        resources: [
          {
            uri: 'context7://documentation',
            name: 'Real-time Documentation Access',
            description: 'Access to up-to-date documentation for thousands of libraries'
          }
        ]
      })
    }

    // Sequential Thinking Server
    if (this.config.sequentialThinking.enabled) {
      this.registerServer({
        name: 'sequential-thinking',
        version: '1.0.0',
        capabilities: { tools: true },
        tools: [
          {
            name: 'sequential_thinking',
            description: 'Advanced reasoning through sequential thought processes',
            inputSchema: {
              type: 'object',
              properties: {
                thought: {
                  type: 'string',
                  description: 'Current thinking step'
                },
                nextThoughtNeeded: {
                  type: 'boolean',
                  description: 'Whether another thought step is needed'
                },
                thoughtNumber: {
                  type: 'integer',
                  minimum: 1,
                  description: 'Current thought number'
                },
                totalThoughts: {
                  type: 'integer',
                  minimum: 1,
                  description: 'Estimated total thoughts needed'
                },
                isRevision: {
                  type: 'boolean',
                  description: 'Whether this revises previous thinking'
                },
                revisesThought: {
                  type: 'integer',
                  minimum: 1,
                  description: 'Which thought is being reconsidered'
                },
                branchFromThought: {
                  type: 'integer',
                  minimum: 1,
                  description: 'Branching point thought number'
                },
                branchId: {
                  type: 'string',
                  description: 'Branch identifier'
                },
                needsMoreThoughts: {
                  type: 'boolean',
                  description: 'If more thoughts are needed'
                }
              },
              required: ['thought', 'nextThoughtNeeded', 'thoughtNumber', 'totalThoughts']
            },
            handler: this.sequentialThinking.bind(this)
          }
        ],
        resources: []
      })
    }

    // Register custom servers
    this.config.customServers.forEach(serverConfig => {
      if (serverConfig.enabled) {
        this.registerCustomServer(serverConfig)
      }
    })
  }

  /**
   * Context7: Resolve library ID
   */
  private async resolveLibraryId(params: any): Promise<any> {
    let { libraryName } = params

    // Handle cases where libraryName might be an object or non-string
    if (typeof libraryName !== 'string') {
      if (libraryName && typeof libraryName === 'object') {
        // If it's an object, try to extract a string value
        libraryName = libraryName.framework || libraryName.name || libraryName.value || JSON.stringify(libraryName)
      } else {
        // Convert to string as fallback
        libraryName = String(libraryName || 'unknown')
      }
    }

    // Ensure we have a valid string
    const safeLibraryName = libraryName.toString().toLowerCase().replace(/[^a-z0-9-]/g, '-')

    // In a real implementation, this would call the actual Context7 MCP server
    // For now, return a structured response
    return {
      libraryId: `/context7/${safeLibraryName}`,
      name: libraryName,
      description: `Documentation for ${libraryName}`,
      trustScore: 9.0,
      codeSnippets: 1000,
      lastUpdated: new Date().toISOString()
    }
  }

  /**
   * Context7: Get library documentation
   */
  private async getLibraryDocs(params: any): Promise<any> {
    const { context7CompatibleLibraryID, topic, tokens = 10000 } = params
    
    // In a real implementation, this would call the actual Context7 MCP server
    return {
      libraryId: context7CompatibleLibraryID,
      topic: topic || 'general',
      documentation: `# ${context7CompatibleLibraryID} Documentation\n\nUp-to-date documentation content would be here...`,
      codeExamples: [
        {
          title: 'Basic Usage',
          code: '// Example code would be here',
          language: 'typescript'
        }
      ],
      tokens: Math.min(tokens, 10000),
      lastUpdated: new Date().toISOString()
    }
  }

  /**
   * Context7: Search documentation
   */
  private async searchDocumentation(params: any): Promise<any> {
    const { query, libraries, maxResults = 5 } = params
    
    return {
      query,
      results: [
        {
          libraryId: '/example/library',
          title: 'Example Documentation',
          snippet: 'Relevant documentation snippet...',
          relevanceScore: 0.95
        }
      ],
      totalResults: 1,
      searchTime: Date.now()
    }
  }

  /**
   * Sequential Thinking: Process thought
   */
  private async sequentialThinking(params: any): Promise<any> {
    const { 
      thought, 
      nextThoughtNeeded, 
      thoughtNumber, 
      totalThoughts,
      isRevision = false,
      revisesThought,
      branchFromThought,
      branchId,
      needsMoreThoughts = false
    } = params

    // In a real implementation, this would call the actual Sequential Thinking MCP server
    return {
      thoughtNumber,
      totalThoughts,
      nextThoughtNeeded,
      branches: branchId ? [{ id: branchId, fromThought: branchFromThought }] : [],
      thoughtHistoryLength: thoughtNumber,
      isRevision,
      revisesThought,
      needsMoreThoughts,
      timestamp: new Date().toISOString()
    }
  }

  /**
   * Register a custom MCP server
   */
  private registerCustomServer(config: MCPServerConfig): void {
    this.registerServer({
      name: config.name,
      version: '1.0.0',
      capabilities: { tools: true },
      tools: [], // Would be populated based on the actual server
      resources: []
    })
  }

  /**
   * Get documentation for a specific library (convenience method)
   */
  async getDocumentation(libraryName: string, topic?: string): Promise<any> {
    // First resolve the library ID
    const libraryInfo = await this.executeTool('context7-docs', 'resolve_library_id', {
      libraryName
    })

    // Then get the documentation
    return await this.executeTool('context7-docs', 'get_library_docs', {
      context7CompatibleLibraryID: libraryInfo.libraryId,
      topic
    })
  }

  /**
   * Perform sequential thinking (convenience method)
   */
  async think(thought: string, context: any = {}): Promise<any> {
    return await this.executeTool('sequential-thinking', 'sequential_thinking', {
      thought,
      nextThoughtNeeded: true,
      thoughtNumber: context.thoughtNumber || 1,
      totalThoughts: context.totalThoughts || 5,
      ...context
    })
  }

  /**
   * Get MCP configuration for external tools
   */
  getMCPConfiguration(): any {
    return {
      mcpServers: {
        "Context7": {
          command: this.config.context7.command,
          args: this.config.context7.args,
          timeout: this.config.context7.timeout
        },
        "sequential-thinking": {
          command: this.config.sequentialThinking.command,
          args: this.config.sequentialThinking.args,
          timeout: this.config.sequentialThinking.timeout
        },
        ...Object.fromEntries(
          this.config.customServers.map(server => [
            server.name,
            {
              command: server.command,
              args: server.args,
              timeout: server.timeout,
              env: server.env
            }
          ])
        )
      }
    }
  }

  /**
   * Test MCP connections
   */
  async testConnections(): Promise<any> {
    const results: any = {}

    // Test Context7
    if (this.config.context7.enabled) {
      try {
        const testResult = await this.getDocumentation('react', 'hooks')
        results.context7 = { status: 'connected', test: testResult }
      } catch (error) {
        results.context7 = { status: 'error', error: error.message }
      }
    }

    // Test Sequential Thinking
    if (this.config.sequentialThinking.enabled) {
      try {
        const testResult = await this.think('Testing sequential thinking capability')
        results.sequentialThinking = { status: 'connected', test: testResult }
      } catch (error) {
        results.sequentialThinking = { status: 'error', error: error.message }
      }
    }

    return results
  }
}
