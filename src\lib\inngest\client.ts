/**
 * Inngest Client Configuration
 * Serverless task queue with retries, idempotency, and observability
 */

import { Inngest } from "inngest"
import { env } from "@/src/env"

// Create the main Inngest client
export const inngest = new Inngest({
  id: "ag3nt-planning",
  name: "AG3NT Planning System",
  retryFunction: async (attempt: number) => {
    // Exponential backoff with jitter
    const baseDelay = Math.min(1000 * Math.pow(2, attempt), 30000)
    const jitter = Math.random() * 1000
    return baseDelay + jitter
  },
  ...(env.isDev && {
    eventKey: env.INNGEST_EVENT_KEY,
    baseUrl: "http://localhost:3000",
  }),
})

/**
 * Event Types for Type Safety
 */
export type PlanningEvents = {
  "planning/session.started": {
    data: {
      sessionId: string
      prompt: string
      userId?: string
      isInteractive: boolean
      timestamp: string
    }
  }
  
  "planning/step.requested": {
    data: {
      sessionId: string
      step: string
      context: Record<string, any>
      answer?: string
      timestamp: string
    }
  }
  
  "planning/step.completed": {
    data: {
      sessionId: string
      step: string
      results: Record<string, any>
      duration: number
      mcpEnhanced: boolean
      timestamp: string
    }
  }
  
  "planning/step.failed": {
    data: {
      sessionId: string
      step: string
      error: string
      attempt: number
      timestamp: string
    }
  }
  
  "planning/session.completed": {
    data: {
      sessionId: string
      results: Record<string, any>
      totalDuration: number
      stepsCompleted: number
      timestamp: string
    }
  }
  
  "planning/session.failed": {
    data: {
      sessionId: string
      error: string
      stepsCompleted: number
      timestamp: string
    }
  }
  
  "planning/mcp.enrichment": {
    data: {
      sessionId: string
      step: string
      enrichments: Array<{
        title: string
        description: string
        impact: string
      }>
      timestamp: string
    }
  }
  
  "planning/ai.request": {
    data: {
      sessionId: string
      step: string
      model: string
      tokens: number
      cost: number
      timestamp: string
    }
  }
}

/**
 * Helper to send events with type safety
 */
export async function sendPlanningEvent<T extends keyof PlanningEvents>(
  event: T,
  data: PlanningEvents[T]["data"]
) {
  return await inngest.send({
    name: event,
    data,
  })
}

/**
 * Workflow State Management
 */
export interface WorkflowState {
  sessionId: string
  status: "running" | "completed" | "failed" | "paused"
  currentStep?: string
  completedSteps: string[]
  results: Record<string, any>
  errors: Array<{
    step: string
    error: string
    timestamp: string
  }>
  startedAt: string
  completedAt?: string
  metadata: {
    prompt: string
    isInteractive: boolean
    userId?: string
    mcpEnabled: boolean
    totalSteps: number
  }
}

/**
 * Step Configuration
 */
export interface StepConfig {
  name: string
  timeout: number
  maxRetries: number
  dependencies: string[]
  parallel: boolean
  mcpEnabled: boolean
  priority: "low" | "normal" | "high"
}

export const PLANNING_STEPS: Record<string, StepConfig> = {
  analyze: {
    name: "analyze",
    timeout: 30000,
    maxRetries: 3,
    dependencies: [],
    parallel: false,
    mcpEnabled: true,
    priority: "high"
  },
  clarify: {
    name: "clarify", 
    timeout: 20000,
    maxRetries: 2,
    dependencies: ["analyze"],
    parallel: false,
    mcpEnabled: true,
    priority: "normal"
  },
  summary: {
    name: "summary",
    timeout: 25000,
    maxRetries: 2,
    dependencies: ["analyze", "clarify"],
    parallel: false,
    mcpEnabled: true,
    priority: "normal"
  },
  techstack: {
    name: "techstack",
    timeout: 35000,
    maxRetries: 3,
    dependencies: ["analyze", "clarify"],
    parallel: false,
    mcpEnabled: true,
    priority: "high"
  },
  prd: {
    name: "prd",
    timeout: 45000,
    maxRetries: 3,
    dependencies: ["summary", "techstack"],
    parallel: false,
    mcpEnabled: true,
    priority: "high"
  },
  wireframes: {
    name: "wireframes",
    timeout: 40000,
    maxRetries: 2,
    dependencies: ["prd"],
    parallel: true,
    mcpEnabled: true,
    priority: "normal"
  },
  database: {
    name: "database",
    timeout: 35000,
    maxRetries: 3,
    dependencies: ["prd", "techstack"],
    parallel: true,
    mcpEnabled: true,
    priority: "high"
  },
  design: {
    name: "design",
    timeout: 30000,
    maxRetries: 2,
    dependencies: ["wireframes"],
    parallel: true,
    mcpEnabled: false,
    priority: "low"
  },
  filesystem: {
    name: "filesystem",
    timeout: 30000,
    maxRetries: 2,
    dependencies: ["techstack", "database"],
    parallel: true,
    mcpEnabled: true,
    priority: "normal"
  },
  workflow: {
    name: "workflow",
    timeout: 35000,
    maxRetries: 2,
    dependencies: ["prd", "filesystem"],
    parallel: false,
    mcpEnabled: true,
    priority: "normal"
  },
  tasks: {
    name: "tasks",
    timeout: 50000,
    maxRetries: 3,
    dependencies: ["workflow", "database", "wireframes"],
    parallel: false,
    mcpEnabled: true,
    priority: "high"
  },
  scaffold: {
    name: "scaffold",
    timeout: 60000,
    maxRetries: 3,
    dependencies: ["tasks", "filesystem", "design"],
    parallel: false,
    mcpEnabled: true,
    priority: "high"
  }
}

/**
 * Utility functions
 */
export function getNextSteps(completedSteps: string[]): string[] {
  return Object.values(PLANNING_STEPS)
    .filter(step => {
      // Check if all dependencies are completed
      return step.dependencies.every(dep => completedSteps.includes(dep)) &&
             !completedSteps.includes(step.name)
    })
    .map(step => step.name)
}

export function canRunInParallel(steps: string[]): string[][] {
  const parallelGroups: string[][] = []
  const remaining = [...steps]
  
  while (remaining.length > 0) {
    const parallelGroup = remaining.filter(step => 
      PLANNING_STEPS[step]?.parallel
    )
    
    if (parallelGroup.length > 0) {
      parallelGroups.push(parallelGroup)
      parallelGroup.forEach(step => {
        const index = remaining.indexOf(step)
        if (index > -1) remaining.splice(index, 1)
      })
    } else {
      // Take the first non-parallel step
      parallelGroups.push([remaining.shift()!])
    }
  }
  
  return parallelGroups
}
