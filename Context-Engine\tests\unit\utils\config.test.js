/**
 * Unit tests for configuration management
 */

import { jest } from '@jest/globals';

// Mock the config module to avoid validation errors during import
jest.unstable_mockModule('../../../src/utils/config.js', () => ({
  config: {
    env: 'test',
    port: 3000,
    logLevel: 'error',
    neo4j: {
      uri: 'bolt://localhost:7687',
      user: 'neo4j',
      password: 'password',
      database: 'neo4j'
    },
    processing: {
      maxConcurrent: 2,
      batchSize: 10,
      supportedLanguages: ['javascript', 'typescript', 'python'],
      fileExtensions: {
        javascript: ['.js', '.jsx', '.mjs'],
        typescript: ['.ts', '.tsx'],
        python: ['.py']
      }
    },
    api: {
      cors: { enabled: true }
    },
    redis: {
      url: 'redis://localhost:6379',
      password: ''
    }
  }
}));

describe('Configuration Management', () => {
  let originalEnv;

  beforeEach(() => {
    // Save original environment
    originalEnv = { ...process.env };
    
    // Clear module cache to ensure fresh imports
    jest.resetModules();
  });

  afterEach(() => {
    // Restore original environment
    process.env = originalEnv;
  });

  describe('config validation', () => {
    test('should load valid configuration', async () => {
      // Set required environment variables
      process.env.NEO4J_URI = 'bolt://localhost:7687';
      process.env.NEO4J_USER = 'neo4j';
      process.env.NEO4J_PASSWORD = 'password';
      process.env.API_KEY_SECRET = 'test_secret_key_32_characters_long';

      const { config } = await import('../../../src/utils/config.js');
      
      expect(config).toBeDefined();
      expect(config.neo4j.uri).toBe('bolt://localhost:7687');
      expect(config.neo4j.user).toBe('neo4j');
      expect(config.env).toBe('test');
    });

    test('should throw error for missing required variables', async () => {
      // Remove required environment variables
      delete process.env.NEO4J_URI;
      delete process.env.NEO4J_USER;
      delete process.env.NEO4J_PASSWORD;
      delete process.env.API_KEY_SECRET;

      await expect(async () => {
        await import('../../../src/utils/config.js');
      }).rejects.toThrow('Config validation error');
    });

    test('should use default values for optional variables', async () => {
      // Set only required variables
      process.env.NEO4J_URI = 'bolt://localhost:7687';
      process.env.NEO4J_USER = 'neo4j';
      process.env.NEO4J_PASSWORD = 'password';
      process.env.API_KEY_SECRET = 'test_secret_key_32_characters_long';

      const { config } = await import('../../../src/utils/config.js');
      
      expect(config.port).toBe(3000);
      expect(config.logLevel).toBe('error'); // From test env
      expect(config.processing.maxConcurrent).toBe(2); // From test env
    });

    test('should validate API key secret length', async () => {
      process.env.NEO4J_URI = 'bolt://localhost:7687';
      process.env.NEO4J_USER = 'neo4j';
      process.env.NEO4J_PASSWORD = 'password';
      process.env.API_KEY_SECRET = 'short'; // Too short

      await expect(async () => {
        await import('../../../src/utils/config.js');
      }).rejects.toThrow('Config validation error');
    });
  });

  describe('environment-specific configuration', () => {
    test('should load development configuration', async () => {
      process.env.NODE_ENV = 'development';
      process.env.NEO4J_URI = 'bolt://localhost:7687';
      process.env.NEO4J_USER = 'neo4j';
      process.env.NEO4J_PASSWORD = 'password';
      process.env.API_KEY_SECRET = 'test_secret_key_32_characters_long';

      const { config } = await import('../../../src/utils/config.js');
      
      expect(config.env).toBe('development');
      expect(config.api.cors.enabled).toBe(true);
    });

    test('should load production configuration', async () => {
      process.env.NODE_ENV = 'production';
      process.env.NEO4J_URI = 'bolt://localhost:7687';
      process.env.NEO4J_USER = 'neo4j';
      process.env.NEO4J_PASSWORD = 'password';
      process.env.API_KEY_SECRET = 'test_secret_key_32_characters_long';
      process.env.ENABLE_CORS = 'false';

      const { config } = await import('../../../src/utils/config.js');
      
      expect(config.env).toBe('production');
      expect(config.api.cors.enabled).toBe(false);
    });
  });

  describe('database configuration', () => {
    test('should configure Neo4j settings', async () => {
      process.env.NEO4J_URI = 'bolt://test:7687';
      process.env.NEO4J_USER = 'testuser';
      process.env.NEO4J_PASSWORD = 'testpass';
      process.env.NEO4J_DATABASE = 'testdb';
      process.env.API_KEY_SECRET = 'test_secret_key_32_characters_long';

      const { config } = await import('../../../src/utils/config.js');
      
      expect(config.neo4j.uri).toBe('bolt://test:7687');
      expect(config.neo4j.user).toBe('testuser');
      expect(config.neo4j.password).toBe('testpass');
      expect(config.neo4j.database).toBe('testdb');
      expect(config.neo4j.maxConnectionPoolSize).toBe(50);
    });

    test('should configure Redis settings', async () => {
      process.env.NEO4J_URI = 'bolt://localhost:7687';
      process.env.NEO4J_USER = 'neo4j';
      process.env.NEO4J_PASSWORD = 'password';
      process.env.API_KEY_SECRET = 'test_secret_key_32_characters_long';
      process.env.REDIS_URL = 'redis://test:6379';
      process.env.REDIS_PASSWORD = 'testpass';

      const { config } = await import('../../../src/utils/config.js');
      
      expect(config.redis.url).toBe('redis://test:6379');
      expect(config.redis.password).toBe('testpass');
    });
  });

  describe('processing configuration', () => {
    test('should configure processing settings', async () => {
      process.env.NEO4J_URI = 'bolt://localhost:7687';
      process.env.NEO4J_USER = 'neo4j';
      process.env.NEO4J_PASSWORD = 'password';
      process.env.API_KEY_SECRET = 'test_secret_key_32_characters_long';
      process.env.MAX_CONCURRENT_PROCESSES = '10';
      process.env.BATCH_SIZE = '50';

      const { config } = await import('../../../src/utils/config.js');
      
      expect(config.processing.maxConcurrent).toBe(10);
      expect(config.processing.batchSize).toBe(50);
      expect(config.processing.supportedLanguages).toContain('javascript');
      expect(config.processing.supportedLanguages).toContain('python');
    });

    test('should configure file extensions mapping', async () => {
      process.env.NEO4J_URI = 'bolt://localhost:7687';
      process.env.NEO4J_USER = 'neo4j';
      process.env.NEO4J_PASSWORD = 'password';
      process.env.API_KEY_SECRET = 'test_secret_key_32_characters_long';

      const { config } = await import('../../../src/utils/config.js');
      
      expect(config.processing.fileExtensions.javascript).toContain('.js');
      expect(config.processing.fileExtensions.typescript).toContain('.ts');
      expect(config.processing.fileExtensions.python).toContain('.py');
    });
  });
});
