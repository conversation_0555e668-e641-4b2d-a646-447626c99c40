import { createLogger } from '../../utils/logger.js';

const logger = createLogger('SymbolTableBuilder');

/**
 * Symbol table builder for extracting identifiers, scopes, and references
 */
export class SymbolTableBuilder {
  constructor(config) {
    this.config = config;
    this.isInitialized = false;
  }

  /**
   * Initialize the symbol table builder
   */
  async initialize() {
    try {
      logger.info('Initializing symbol table builder');
      this.isInitialized = true;
      logger.info('Symbol table builder initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize symbol table builder', { error: error.message });
      throw error;
    }
  }

  /**
   * Process context and build symbol table
   */
  async process(context) {
    if (!this.isInitialized) {
      throw new Error('Symbol table builder not initialized');
    }

    const { file, ast } = context;
    const startTime = Date.now();

    try {
      logger.debug('Building symbol table for file', {
        path: file.path,
        language: file.language
      });

      if (!ast) {
        logger.warn('No AST available for symbol table building', {
          path: file.path
        });
        return { ...context, symbols: null };
      }

      // Build symbol table from AST
      const symbols = this.buildSymbolTable(ast, file);

      const duration = Date.now() - startTime;
      logger.debug('Symbol table building completed', {
        path: file.path,
        duration,
        symbolCount: symbols.symbols.length,
        scopeCount: symbols.scopes.length
      });

      return {
        ...context,
        symbols
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error('Symbol table building failed', {
        path: file.path,
        duration,
        error: error.message
      });
      
      return { ...context, symbols: null };
    }
  }

  /**
   * Build symbol table from AST
   */
  buildSymbolTable(ast, file) {
    const symbolTable = {
      file: file.path,
      language: file.language,
      symbols: [],
      scopes: [],
      references: [],
      exports: [],
      imports: []
    };

    // Process imports
    symbolTable.imports = this.processImports(ast.imports, file);

    // Process exports
    symbolTable.exports = this.processExports(ast.exports, file);

    // Process AST nodes to extract symbols
    this.processASTNodes(ast.nodes, symbolTable, file);

    // Build scope hierarchy
    this.buildScopeHierarchy(symbolTable);

    // Resolve references
    this.resolveReferences(symbolTable);

    return symbolTable;
  }

  /**
   * Process import statements
   */
  processImports(imports, file) {
    return imports.map(importNode => ({
      id: this.generateId('import', importNode.source),
      type: 'import',
      source: importNode.source,
      specifiers: importNode.specifiers || [],
      isDefault: importNode.isDefault || false,
      location: {
        file: file.path,
        line: importNode.startLine || 1
      }
    }));
  }

  /**
   * Process export statements
   */
  processExports(exports, file) {
    return exports.map(exportNode => ({
      id: this.generateId('export', exportNode.name || 'default'),
      type: 'export',
      name: exportNode.name,
      isDefault: exportNode.isDefault || false,
      specifiers: exportNode.specifiers || [],
      location: {
        file: file.path,
        line: exportNode.startLine || 1
      }
    }));
  }

  /**
   * Process AST nodes to extract symbols
   */
  processASTNodes(nodes, symbolTable, file) {
    for (const node of nodes) {
      switch (node.nodeType) {
        case 'function':
          this.processFunctionNode(node, symbolTable, file);
          break;
        case 'class':
          this.processClassNode(node, symbolTable, file);
          break;
        case 'variable':
          this.processVariableNode(node, symbolTable, file);
          break;
        default:
          this.processGenericNode(node, symbolTable, file);
      }
    }
  }

  /**
   * Process function nodes
   */
  processFunctionNode(node, symbolTable, file) {
    const functionSymbol = {
      id: this.generateId('function', node.name, node.startLine),
      type: 'function',
      name: node.name,
      signature: node.signature,
      parameters: node.parameters || [],
      scope: this.createScope('function', node.name, node.startLine, node.endLine),
      location: {
        file: file.path,
        startLine: node.startLine,
        endLine: node.endLine,
        startColumn: node.startColumn,
        endColumn: node.endColumn
      },
      metadata: {
        isAsync: node.isAsync || false,
        isGenerator: node.isGenerator || false,
        visibility: this.inferVisibility(node),
        complexity: this.calculateComplexity(node)
      }
    };

    symbolTable.symbols.push(functionSymbol);
    symbolTable.scopes.push(functionSymbol.scope);

    // Process function parameters as symbols
    if (node.parameters) {
      node.parameters.forEach((param, index) => {
        const paramSymbol = {
          id: this.generateId('parameter', param.name, node.startLine, index),
          type: 'parameter',
          name: param.name,
          dataType: param.type || 'unknown',
          scope: functionSymbol.scope.id,
          location: {
            file: file.path,
            startLine: node.startLine,
            endLine: node.startLine
          },
          metadata: {
            index: index,
            function: functionSymbol.id
          }
        };
        symbolTable.symbols.push(paramSymbol);
      });
    }
  }

  /**
   * Process class nodes
   */
  processClassNode(node, symbolTable, file) {
    const classSymbol = {
      id: this.generateId('class', node.name, node.startLine),
      type: 'class',
      name: node.name,
      superclass: node.superclass,
      scope: this.createScope('class', node.name, node.startLine, node.endLine),
      location: {
        file: file.path,
        startLine: node.startLine,
        endLine: node.endLine,
        startColumn: node.startColumn,
        endColumn: node.endColumn
      },
      metadata: {
        methods: node.methods || [],
        properties: node.properties || [],
        visibility: this.inferVisibility(node)
      }
    };

    symbolTable.symbols.push(classSymbol);
    symbolTable.scopes.push(classSymbol.scope);

    // Process class methods
    if (node.methods) {
      node.methods.forEach(method => {
        const methodSymbol = {
          id: this.generateId('method', method.name, method.startLine),
          type: 'method',
          name: method.name,
          signature: method.signature,
          scope: classSymbol.scope.id,
          parentClass: classSymbol.id,
          location: {
            file: file.path,
            startLine: method.startLine,
            endLine: method.endLine
          },
          metadata: {
            visibility: method.visibility || 'public',
            isStatic: method.isStatic || false,
            isAbstract: method.isAbstract || false
          }
        };
        symbolTable.symbols.push(methodSymbol);
      });
    }
  }

  /**
   * Process variable nodes
   */
  processVariableNode(node, symbolTable, file) {
    const variableSymbol = {
      id: this.generateId('variable', node.name, node.startLine),
      type: 'variable',
      name: node.name,
      dataType: node.type || 'unknown',
      scope: this.inferScope(node, symbolTable),
      location: {
        file: file.path,
        startLine: node.startLine,
        endLine: node.endLine,
        startColumn: node.startColumn,
        endColumn: node.endColumn
      },
      metadata: {
        hasInitializer: node.hasInitializer || false,
        isConstant: node.isConstant || false,
        visibility: this.inferVisibility(node)
      }
    };

    symbolTable.symbols.push(variableSymbol);
  }

  /**
   * Process generic nodes
   */
  processGenericNode(node, symbolTable, file) {
    if (node.name && node.name !== 'unknown') {
      const genericSymbol = {
        id: this.generateId('identifier', node.name, node.startLine),
        type: 'identifier',
        name: node.name,
        nodeType: node.nodeType,
        scope: this.inferScope(node, symbolTable),
        location: {
          file: file.path,
          startLine: node.startLine,
          endLine: node.endLine
        }
      };

      symbolTable.symbols.push(genericSymbol);
    }
  }

  /**
   * Create a scope object
   */
  createScope(type, name, startLine, endLine) {
    return {
      id: this.generateId('scope', type, name, startLine),
      type: type,
      name: name,
      startLine: startLine,
      endLine: endLine,
      parent: null, // Will be set in buildScopeHierarchy
      children: []
    };
  }

  /**
   * Build scope hierarchy
   */
  buildScopeHierarchy(symbolTable) {
    // Sort scopes by start line to establish hierarchy
    const sortedScopes = symbolTable.scopes.sort((a, b) => a.startLine - b.startLine);

    for (let i = 0; i < sortedScopes.length; i++) {
      const currentScope = sortedScopes[i];
      
      // Find parent scope (the innermost scope that contains this one)
      for (let j = i - 1; j >= 0; j--) {
        const potentialParent = sortedScopes[j];
        
        if (potentialParent.startLine <= currentScope.startLine &&
            potentialParent.endLine >= currentScope.endLine) {
          currentScope.parent = potentialParent.id;
          potentialParent.children.push(currentScope.id);
          break;
        }
      }
    }
  }

  /**
   * Resolve symbol references
   */
  resolveReferences(symbolTable) {
    // This is a simplified implementation
    // In a full implementation, you would analyze the AST for identifier usage
    // and create reference relationships between symbols
    
    const symbolMap = new Map();
    symbolTable.symbols.forEach(symbol => {
      symbolMap.set(symbol.name, symbol);
    });

    // Add references based on symbol usage patterns
    symbolTable.symbols.forEach(symbol => {
      if (symbol.type === 'function' && symbol.parameters) {
        symbol.parameters.forEach(param => {
          symbolTable.references.push({
            from: symbol.id,
            to: param.name,
            type: 'parameter_reference',
            location: symbol.location
          });
        });
      }
    });
  }

  /**
   * Generate unique identifier for symbols
   */
  generateId(type, name, line, extra = '') {
    const parts = [type, name, line, extra].filter(p => p !== '').join('_');
    return `${parts}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Infer scope for a symbol
   */
  inferScope(node, symbolTable) {
    // Find the most specific scope that contains this node
    for (const scope of symbolTable.scopes) {
      if (scope.startLine <= node.startLine && scope.endLine >= node.endLine) {
        return scope.id;
      }
    }
    return 'global';
  }

  /**
   * Infer visibility (public, private, protected)
   */
  inferVisibility(node) {
    if (node.name && node.name.startsWith('_')) {
      return node.name.startsWith('__') ? 'private' : 'protected';
    }
    return 'public';
  }

  /**
   * Calculate basic complexity metric
   */
  calculateComplexity(node) {
    // Simple complexity calculation based on node size
    const textLength = node.text ? node.text.length : 0;
    const lineCount = (node.endLine || 0) - (node.startLine || 0) + 1;
    
    if (lineCount <= 10) return 'low';
    if (lineCount <= 50) return 'medium';
    return 'high';
  }
}

export default SymbolTableBuilder;
