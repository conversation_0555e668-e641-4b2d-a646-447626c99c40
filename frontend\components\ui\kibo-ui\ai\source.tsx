'use client';

import { BookIcon, ChevronDownIcon } from 'lucide-react';
import type { ComponentProps } from 'react';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { cn } from '@/lib/utils';

export type AISourcesProps = ComponentProps<typeof Collapsible>;

export const AISources = ({ className, ...props }: AISourcesProps) => (
  <Collapsible className={cn('space-y-2', className)} {...props} />
);

export type AISourcesTriggerProps = ComponentProps<
  typeof CollapsibleTrigger
> & {
  count: number;
};

export const AISourcesTrigger = ({
  className,
  count,
  children,
  ...props
}: AISourcesTriggerProps) => (
  <CollapsibleTrigger
    className={cn(
      'flex w-full items-center justify-between rounded-lg border p-3 text-left text-sm font-medium transition-colors hover:bg-muted/50',
      className
    )}
    {...props}
  >
    {children ?? (
      <>
        <span className="flex items-center gap-2 text-muted-foreground">
          <BookIcon className="h-4 w-4" />
          Used {count} sources
        </span>
        <ChevronDownIcon className="h-4 w-4 transition-transform data-[state=open]:rotate-180" />
      </>
    )}
  </CollapsibleTrigger>
);

export type AISourcesContentProps = ComponentProps<typeof CollapsibleContent>;

export const AISourcesContent = ({
  className,
  ...props
}: AISourcesContentProps) => (
  <CollapsibleContent
    className={cn('overflow-hidden text-sm', className)}
    {...props}
  />
);

export type AISourceProps = ComponentProps<'a'>;

export const AISource = ({
  href,
  title,
  children,
  className,
  ...props
}: AISourceProps) => (
  <a
    href={href}
    className={cn(
      'flex items-center gap-3 rounded-lg border p-3 text-sm transition-colors hover:bg-muted/50',
      className
    )}
    target="_blank"
    rel="noopener noreferrer"
    {...props}
  >
    {children ?? (
      <>
        <BookIcon className="h-4 w-4 shrink-0 text-muted-foreground" />
        <span className="truncate">{title}</span>
      </>
    )}
  </a>
);
