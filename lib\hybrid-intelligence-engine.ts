/**
 * Hybrid Intelligence Engine
 * Combines Neo4j structural intelligence with Agentic RAG semantic intelligence
 * Powers the unified context engine with both precise and semantic understanding
 */

import neo4j from 'neo4j-driver'
import type { AgenticRAGEngine, SemanticSearchResult } from './agentic-rag-engine'
import type { CodebaseContext } from './unified-context-engine'

export interface HybridQuery {
  naturalLanguage: string
  structuralConstraints?: {
    maxDepth?: number
    relationshipTypes?: string[]
    nodeTypes?: string[]
  }
  semanticConstraints?: {
    similarity?: number
    concepts?: string[]
    excludePatterns?: string[]
  }
  fusionStrategy: 'semantic-first' | 'structural-first' | 'balanced' | 'adaptive'
}

export interface HybridResult {
  structuralMatches: StructuralMatch[]
  semanticMatches: any[]
  fusedResults: FusedMatch[]
  confidence: number
  explanation: string
  queryStrategy: string
}

export interface StructuralMatch {
  nodeId: string
  nodeType: string
  properties: any
  relationships: RelationshipInfo[]
  path?: string[]
  distance?: number
}

export interface RelationshipInfo {
  type: string
  direction: 'incoming' | 'outgoing'
  targetNode: string
  properties?: any
}

export interface FusedMatch {
  id: string
  type: 'file' | 'function' | 'class' | 'module'
  structuralScore: number
  semanticScore: number
  combinedScore: number
  explanation: string
  context: {
    structuralContext: any
    semanticContext: any
    relationships: string[]
    concepts: string[]
  }
}

/**
 * Hybrid Intelligence Engine combining Neo4j and Agentic RAG
 */
export class HybridIntelligenceEngine {
  private neo4jDriver: neo4j.Driver
  private agenticRAG: AgenticRAGEngine
  private fusionStrategies: Map<string, any> = new Map()
  private queryOptimizer: QueryOptimizer

  constructor(neo4jDriver: neo4j.Driver, agenticRAG: AgenticRAGEngine) {
    this.neo4jDriver = neo4jDriver
    this.agenticRAG = agenticRAG
    this.queryOptimizer = new QueryOptimizer()
    this.initializeFusionStrategies()
  }

  /**
   * Execute hybrid query combining structural and semantic intelligence
   */
  async executeHybridQuery(query: HybridQuery): Promise<HybridResult> {
    // Step 1: Optimize query strategy
    const optimizedStrategy = await this.queryOptimizer.optimizeQuery(query)
    
    // Step 2: Execute based on fusion strategy
    switch (optimizedStrategy.strategy) {
      case 'semantic-first':
        return await this.executeSemanticFirst(query, optimizedStrategy)
      case 'structural-first':
        return await this.executeStructuralFirst(query, optimizedStrategy)
      case 'balanced':
        return await this.executeBalanced(query, optimizedStrategy)
      case 'adaptive':
        return await this.executeAdaptive(query, optimizedStrategy)
      default:
        return await this.executeBalanced(query, optimizedStrategy)
    }
  }

  /**
   * Semantic-first approach: Use RAG to find candidates, then Neo4j for relationships
   */
  private async executeSemanticFirst(query: HybridQuery, strategy: any): Promise<HybridResult> {
    // 1. Get semantic matches from RAG
    const semanticResults = await this.agenticRAG.search({
      query: query.naturalLanguage,
      intent: strategy.intent,
      context: strategy.context,
      options: { maxResults: 20, semanticThreshold: 0.7 }
    })

    // 2. Enhance with structural information from Neo4j
    const structuralMatches: StructuralMatch[] = []
    
    for (const semanticMatch of semanticResults.results) {
      const structuralInfo = await this.getStructuralInfo(semanticMatch.embedding.metadata.file)
      if (structuralInfo) {
        structuralMatches.push(structuralInfo)
      }
    }

    // 3. Fuse results
    const fusedResults = this.fuseResults(semanticResults.results, structuralMatches, 'semantic-first')

    return {
      structuralMatches,
      semanticMatches: semanticResults.results,
      fusedResults,
      confidence: semanticResults.confidence * 0.01,
      explanation: `Semantic-first search found ${semanticResults.results.length} semantic matches, enhanced with structural relationships`,
      queryStrategy: 'semantic-first'
    }
  }

  /**
   * Structural-first approach: Use Neo4j to find structure, then RAG for semantic relevance
   */
  private async executeStructuralFirst(query: HybridQuery, strategy: any): Promise<HybridResult> {
    // 1. Extract structural query from natural language
    const structuralQuery = this.extractStructuralQuery(query.naturalLanguage)
    
    // 2. Execute Neo4j query
    const structuralMatches = await this.executeNeo4jQuery(structuralQuery, query.structuralConstraints)

    // 3. Filter with semantic relevance
    const semanticResults = await this.filterWithSemanticRelevance(
      structuralMatches, 
      query.naturalLanguage,
      query.semanticConstraints
    )

    // 4. Fuse results
    const fusedResults = this.fuseResults(semanticResults, structuralMatches, 'structural-first')

    return {
      structuralMatches,
      semanticMatches: semanticResults,
      fusedResults,
      confidence: this.calculateStructuralConfidence(structuralMatches),
      explanation: `Structural-first search found ${structuralMatches.length} structural matches, filtered by semantic relevance`,
      queryStrategy: 'structural-first'
    }
  }

  /**
   * Balanced approach: Execute both in parallel and merge intelligently
   */
  private async executeBalanced(query: HybridQuery, strategy: any): Promise<HybridResult> {
    // Execute both approaches in parallel
    const [semanticResults, structuralMatches] = await Promise.all([
      this.agenticRAG.search({
        query: query.naturalLanguage,
        intent: strategy.intent,
        context: strategy.context,
        options: { maxResults: 15, semanticThreshold: 0.6 }
      }),
      this.executeNeo4jQuery(
        this.extractStructuralQuery(query.naturalLanguage),
        query.structuralConstraints
      )
    ])

    // Intelligent fusion with equal weighting
    const fusedResults = this.fuseResults(
      semanticResults.results, 
      structuralMatches, 
      'balanced',
      { semanticWeight: 0.5, structuralWeight: 0.5 }
    )

    return {
      structuralMatches,
      semanticMatches: semanticResults.results,
      fusedResults,
      confidence: (semanticResults.confidence * 0.01 + this.calculateStructuralConfidence(structuralMatches)) / 2,
      explanation: `Balanced search combining ${semanticResults.results.length} semantic and ${structuralMatches.length} structural matches`,
      queryStrategy: 'balanced'
    }
  }

  /**
   * Adaptive approach: Choose strategy based on query characteristics
   */
  private async executeAdaptive(query: HybridQuery, strategy: any): Promise<HybridResult> {
    // Analyze query to determine best approach
    const queryAnalysis = this.analyzeQueryCharacteristics(query.naturalLanguage)
    
    if (queryAnalysis.isStructurallyOriented) {
      return await this.executeStructuralFirst(query, strategy)
    } else if (queryAnalysis.isSemanticallyConcept) {
      return await this.executeSemanticFirst(query, strategy)
    } else {
      return await this.executeBalanced(query, strategy)
    }
  }

  /**
   * Get structural information for a file from Neo4j
   */
  private async getStructuralInfo(filePath: string): Promise<StructuralMatch | null> {
    const session = this.neo4jDriver.session()
    
    try {
      const result = await session.run(`
        MATCH (f:File {path: $filePath})
        OPTIONAL MATCH (f)-[r]->(related)
        RETURN f, collect({type: type(r), direction: 'outgoing', target: related}) as relationships
      `, { filePath })

      if (result.records.length === 0) return null

      const record = result.records[0]
      const fileNode = record.get('f')
      const relationships = record.get('relationships')

      return {
        nodeId: fileNode.identity.toString(),
        nodeType: 'File',
        properties: fileNode.properties,
        relationships: relationships.map((rel: any) => ({
          type: rel.type,
          direction: rel.direction,
          targetNode: rel.target?.identity?.toString() || 'unknown'
        }))
      }
    } finally {
      await session.close()
    }
  }

  /**
   * Execute Neo4j query for structural matches
   */
  private async executeNeo4jQuery(
    structuralQuery: string, 
    constraints?: any
  ): Promise<StructuralMatch[]> {
    const session = this.neo4jDriver.session()
    
    try {
      const result = await session.run(structuralQuery, constraints || {})
      
      return result.records.map(record => {
        const node = record.get('n') || record.get('f') || record.get('s')
        return {
          nodeId: node.identity.toString(),
          nodeType: node.labels[0] || 'Unknown',
          properties: node.properties,
          relationships: []
        }
      })
    } finally {
      await session.close()
    }
  }

  /**
   * Fuse semantic and structural results intelligently
   */
  private fuseResults(
    semanticMatches: any[], 
    structuralMatches: StructuralMatch[], 
    strategy: string,
    weights?: { semanticWeight: number; structuralWeight: number }
  ): FusedMatch[] {
    const fusedResults: FusedMatch[] = []
    const { semanticWeight = 0.6, structuralWeight = 0.4 } = weights || {}

    // Create lookup maps
    const semanticMap = new Map(semanticMatches.map(m => [m.embedding.metadata.file, m]))
    const structuralMap = new Map(structuralMatches.map(m => [m.properties.path, m]))

    // Get all unique files
    const allFiles = new Set([
      ...semanticMatches.map(m => m.embedding.metadata.file),
      ...structuralMatches.map(m => m.properties.path)
    ])

    for (const file of allFiles) {
      const semanticMatch = semanticMap.get(file)
      const structuralMatch = structuralMap.get(file)

      const semanticScore = semanticMatch ? semanticMatch.score : 0
      const structuralScore = structuralMatch ? this.calculateStructuralRelevance(structuralMatch) : 0
      
      const combinedScore = (semanticScore * semanticWeight) + (structuralScore * structuralWeight)

      if (combinedScore > 0.3) { // Minimum threshold
        fusedResults.push({
          id: file,
          type: this.determineFileType(file),
          semanticScore,
          structuralScore,
          combinedScore,
          explanation: this.generateFusionExplanation(semanticMatch, structuralMatch, strategy),
          context: {
            structuralContext: structuralMatch?.properties || {},
            semanticContext: semanticMatch?.embedding?.metadata || {},
            relationships: structuralMatch?.relationships.map(r => r.type) || [],
            concepts: semanticMatch?.embedding?.metadata?.semanticTags || []
          }
        })
      }
    }

    return fusedResults.sort((a, b) => b.combinedScore - a.combinedScore)
  }

  // Helper methods
  private initializeFusionStrategies(): void {
    this.fusionStrategies.set('dependency-analysis', {
      preferStructural: true,
      semanticBoost: 0.2
    })
    
    this.fusionStrategies.set('concept-search', {
      preferSemantic: true,
      structuralBoost: 0.3
    })
  }

  private extractStructuralQuery(naturalLanguage: string): string {
    const lowerQuery = naturalLanguage.toLowerCase()
    
    if (lowerQuery.includes('depends on') || lowerQuery.includes('dependency')) {
      return `
        MATCH (f1:File)-[:DEPENDS_ON]->(f2:File)
        WHERE f1.path CONTAINS $searchTerm OR f2.path CONTAINS $searchTerm
        RETURN f1 as n
        LIMIT 20
      `
    } else if (lowerQuery.includes('calls') || lowerQuery.includes('function')) {
      return `
        MATCH (s:Symbol {type: 'function'})
        WHERE s.name CONTAINS $searchTerm
        RETURN s as n
        LIMIT 20
      `
    } else {
      return `
        MATCH (n)
        WHERE n.path CONTAINS $searchTerm OR n.name CONTAINS $searchTerm
        RETURN n
        LIMIT 20
      `
    }
  }

  private async filterWithSemanticRelevance(
    structuralMatches: StructuralMatch[], 
    query: string,
    constraints?: any
  ): Promise<any[]> {
    // This would use the agentic RAG to filter structural results by semantic relevance
    return structuralMatches.slice(0, 10) // Simplified
  }

  private calculateStructuralConfidence(matches: StructuralMatch[]): number {
    if (matches.length === 0) return 0
    return Math.min(matches.length / 10, 1.0)
  }

  private analyzeQueryCharacteristics(query: string): any {
    const lowerQuery = query.toLowerCase()
    return {
      isStructurallyOriented: lowerQuery.includes('depends') || lowerQuery.includes('calls') || lowerQuery.includes('inherits'),
      isSemanticallyConcept: lowerQuery.includes('similar') || lowerQuery.includes('like') || lowerQuery.includes('related')
    }
  }

  private calculateStructuralRelevance(match: StructuralMatch): number {
    // Calculate relevance based on structural properties
    let score = 0.5
    
    if (match.relationships.length > 0) score += 0.2
    if (match.properties.complexity > 5) score += 0.1
    
    return Math.min(score, 1.0)
  }

  private determineFileType(filePath: string): 'file' | 'function' | 'class' | 'module' {
    if (filePath.includes('class')) return 'class'
    if (filePath.includes('function')) return 'function'
    if (filePath.includes('module')) return 'module'
    return 'file'
  }

  private generateFusionExplanation(semanticMatch: any, structuralMatch: any, strategy: string): string {
    if (semanticMatch && structuralMatch) {
      return `Combined semantic relevance (${Math.round(semanticMatch.score * 100)}%) with structural importance (${structuralMatch.relationships.length} relationships)`
    } else if (semanticMatch) {
      return `High semantic relevance (${Math.round(semanticMatch.score * 100)}%) for the query`
    } else if (structuralMatch) {
      return `Structurally important with ${structuralMatch.relationships.length} relationships`
    }
    return 'Matched through fusion strategy'
  }
}

/**
 * Query Optimizer for hybrid intelligence
 */
class QueryOptimizer {
  async optimizeQuery(query: HybridQuery): Promise<any> {
    // Analyze query and determine optimal strategy
    const analysis = {
      strategy: query.fusionStrategy,
      intent: this.extractIntent(query.naturalLanguage),
      context: this.extractContext(query.naturalLanguage)
    }
    
    return analysis
  }

  private extractIntent(query: string): any {
    const lowerQuery = query.toLowerCase()
    
    if (lowerQuery.includes('find') || lowerQuery.includes('search')) {
      return { type: 'find', target: 'function' }
    } else if (lowerQuery.includes('understand') || lowerQuery.includes('explain')) {
      return { type: 'understand', target: 'module' }
    }
    
    return { type: 'find', target: 'function' }
  }

  private extractContext(query: string): any {
    return {
      workingMemory: [],
      recentFiles: []
    }
  }
}
