import React, { useState, useMemo } from "react"

// TODO: Replace this with the real source of the user prompt in your app
const MOCK_PROMPT =
  "This is a placeholder for the user prompt. Replace with actual prop or context."

interface UserPromptSummaryProps {
  prompt?: string
  maxLength?: number
  className?: string
}

export function UserPromptSummary({
  prompt,
  maxLength = 150,
  className = ""
}: UserPromptSummaryProps) {
  const [expanded, setExpanded] = useState(false)
  const text = prompt || MOCK_PROMPT

  // Smart truncation that breaks at word boundaries
  const shortText = useMemo(() => {
    if (text.length <= maxLength) return text

    // Find the last space before the maxLength to avoid cutting words
    const truncated = text.slice(0, maxLength)
    const lastSpaceIndex = truncated.lastIndexOf(' ')

    // If we found a space and it's not too close to the beginning, use it
    if (lastSpaceIndex > maxLength * 0.7) {
      return truncated.slice(0, lastSpaceIndex) + "..."
    }

    // Otherwise, just truncate at maxLength
    return truncated + "..."
  }, [text, maxLength])

  // Calculate character count for display
  const charCount = text.length

  return (
    <div className={`mb-4 w-full max-w-2xl mx-auto bg-slate-800/80 border border-slate-700 rounded-lg p-4 flex flex-col items-start ${className}`}>
      <div className="flex items-center justify-between w-full mb-2">
        <span className="font-semibold text-gray-300 text-xs">Project Prompt</span>
        <span className="text-xs text-gray-500">
          {charCount.toLocaleString()} characters
        </span>
      </div>

      <div className={`text-sm text-gray-100 break-words w-full ${expanded ? 'max-h-96 overflow-y-auto' : ''}`}>
        {expanded ? (
          <pre className="whitespace-pre-wrap font-sans">{text}</pre>
        ) : (
          shortText
        )}
      </div>

      {text.length > maxLength && (
        <button
          className="mt-2 text-blue-400 hover:text-blue-300 hover:underline text-xs focus:outline-none transition-colors"
          onClick={() => setExpanded((e) => !e)}
        >
          {expanded ? "Show Less" : "Show More"}
        </button>
      )}
    </div>
  )
}
