import { createLogger } from '../../utils/logger.js';

const logger = createLogger('CacheManager');

/**
 * Multi-level cache manager for improved performance
 */
export class CacheManager {
  constructor(config) {
    this.config = config;
    this.caches = new Map();
    this.isInitialized = false;
  }

  /**
   * Initialize the cache manager
   */
  async initialize() {
    try {
      logger.info('Initializing cache manager');

      // Initialize different cache levels
      this.setupMemoryCache();
      this.setupRedisCache();
      this.setupQueryCache();
      this.setupResultCache();

      this.isInitialized = true;
      logger.info('Cache manager initialized successfully');

    } catch (error) {
      logger.error('Failed to initialize cache manager', { error: error.message });
      throw error;
    }
  }

  /**
   * Setup in-memory cache (L1)
   */
  setupMemoryCache() {
    const memoryConfig = this.config.memory || {
      maxSize: 1000,
      ttl: 300000 // 5 minutes
    };

    this.caches.set('memory', {
      type: 'memory',
      data: new Map(),
      metadata: new Map(),
      config: memoryConfig,
      stats: {
        hits: 0,
        misses: 0,
        evictions: 0,
        size: 0
      }
    });

    logger.debug('Memory cache initialized', { config: memoryConfig });
  }

  /**
   * Setup Redis cache (L2) - placeholder for Redis integration
   */
  setupRedisCache() {
    // In a full implementation, this would connect to Redis
    const redisConfig = this.config.redis || {
      enabled: false,
      ttl: 3600000 // 1 hour
    };

    this.caches.set('redis', {
      type: 'redis',
      enabled: redisConfig.enabled,
      config: redisConfig,
      stats: {
        hits: 0,
        misses: 0,
        errors: 0
      }
    });

    logger.debug('Redis cache configured', { enabled: redisConfig.enabled });
  }

  /**
   * Setup query result cache
   */
  setupQueryCache() {
    const queryConfig = this.config.query || {
      maxSize: 500,
      ttl: 600000 // 10 minutes
    };

    this.caches.set('query', {
      type: 'query',
      data: new Map(),
      metadata: new Map(),
      config: queryConfig,
      stats: {
        hits: 0,
        misses: 0,
        evictions: 0,
        size: 0
      }
    });

    logger.debug('Query cache initialized', { config: queryConfig });
  }

  /**
   * Setup result cache for processed data
   */
  setupResultCache() {
    const resultConfig = this.config.result || {
      maxSize: 200,
      ttl: 1800000 // 30 minutes
    };

    this.caches.set('result', {
      type: 'result',
      data: new Map(),
      metadata: new Map(),
      config: resultConfig,
      stats: {
        hits: 0,
        misses: 0,
        evictions: 0,
        size: 0
      }
    });

    logger.debug('Result cache initialized', { config: resultConfig });
  }

  /**
   * Get value from cache with multi-level lookup
   */
  async get(key, cacheType = 'memory') {
    if (!this.isInitialized) {
      throw new Error('Cache manager not initialized');
    }

    const cache = this.caches.get(cacheType);
    if (!cache) {
      throw new Error(`Cache type '${cacheType}' not found`);
    }

    // Check memory cache first
    const memoryResult = this.getFromMemoryCache(key, cache);
    if (memoryResult !== null) {
      cache.stats.hits++;
      return memoryResult;
    }

    // Check Redis cache if enabled
    if (cache.type === 'redis' && cache.enabled) {
      const redisResult = await this.getFromRedisCache(key);
      if (redisResult !== null) {
        // Store in memory cache for faster access
        this.setInMemoryCache(key, redisResult, cache);
        cache.stats.hits++;
        return redisResult;
      }
    }

    cache.stats.misses++;
    return null;
  }

  /**
   * Set value in cache with multi-level storage
   */
  async set(key, value, cacheType = 'memory', options = {}) {
    if (!this.isInitialized) {
      throw new Error('Cache manager not initialized');
    }

    const cache = this.caches.get(cacheType);
    if (!cache) {
      throw new Error(`Cache type '${cacheType}' not found`);
    }

    const ttl = options.ttl || cache.config.ttl;

    // Store in memory cache
    this.setInMemoryCache(key, value, cache, ttl);

    // Store in Redis cache if enabled
    if (cache.type === 'redis' && cache.enabled) {
      await this.setInRedisCache(key, value, ttl);
    }

    logger.debug('Cache entry stored', {
      key: key.substring(0, 50),
      cacheType,
      ttl,
      size: JSON.stringify(value).length
    });
  }

  /**
   * Get from memory cache
   */
  getFromMemoryCache(key, cache) {
    const value = cache.data.get(key);
    if (value === undefined) {
      return null;
    }

    const metadata = cache.metadata.get(key);
    if (metadata && Date.now() > metadata.expiresAt) {
      // Expired entry
      cache.data.delete(key);
      cache.metadata.delete(key);
      cache.stats.size--;
      return null;
    }

    // Update access time
    if (metadata) {
      metadata.lastAccessed = Date.now();
      metadata.accessCount++;
    }

    return value;
  }

  /**
   * Set in memory cache
   */
  setInMemoryCache(key, value, cache, ttl = null) {
    const actualTtl = ttl || cache.config.ttl;

    // Check if cache is full and evict if necessary
    if (cache.data.size >= cache.config.maxSize) {
      this.evictLRU(cache);
    }

    cache.data.set(key, value);
    cache.metadata.set(key, {
      createdAt: Date.now(),
      expiresAt: Date.now() + actualTtl,
      lastAccessed: Date.now(),
      accessCount: 1,
      size: JSON.stringify(value).length
    });

    cache.stats.size = cache.data.size;
  }

  /**
   * Get from Redis cache (placeholder)
   */
  async getFromRedisCache(key) {
    // In a full implementation, this would query Redis
    // For now, return null to simulate cache miss
    return null;
  }

  /**
   * Set in Redis cache (placeholder)
   */
  async setInRedisCache(key, value, ttl) {
    // In a full implementation, this would store in Redis
    // For now, just log the operation
    logger.debug('Redis cache set (simulated)', { key: key.substring(0, 50), ttl });
  }

  /**
   * Evict least recently used entry
   */
  evictLRU(cache) {
    let lruKey = null;
    let lruTime = Date.now();

    for (const [key, metadata] of cache.metadata.entries()) {
      if (metadata.lastAccessed < lruTime) {
        lruTime = metadata.lastAccessed;
        lruKey = key;
      }
    }

    if (lruKey) {
      cache.data.delete(lruKey);
      cache.metadata.delete(lruKey);
      cache.stats.evictions++;
      cache.stats.size--;

      logger.debug('Cache entry evicted (LRU)', {
        key: lruKey.substring(0, 50),
        cacheType: cache.type
      });
    }
  }

  /**
   * Delete from cache
   */
  async delete(key, cacheType = 'memory') {
    const cache = this.caches.get(cacheType);
    if (!cache) {
      return false;
    }

    const deleted = cache.data.delete(key);
    cache.metadata.delete(key);
    
    if (deleted) {
      cache.stats.size--;
    }

    // Also delete from Redis if enabled
    if (cache.type === 'redis' && cache.enabled) {
      await this.deleteFromRedisCache(key);
    }

    return deleted;
  }

  /**
   * Delete from Redis cache (placeholder)
   */
  async deleteFromRedisCache(key) {
    // In a full implementation, this would delete from Redis
    logger.debug('Redis cache delete (simulated)', { key: key.substring(0, 50) });
  }

  /**
   * Clear cache
   */
  async clear(cacheType = null) {
    if (cacheType) {
      const cache = this.caches.get(cacheType);
      if (cache) {
        cache.data.clear();
        cache.metadata.clear();
        cache.stats.size = 0;
        cache.stats.evictions = 0;
      }
    } else {
      // Clear all caches
      for (const cache of this.caches.values()) {
        cache.data.clear();
        cache.metadata.clear();
        cache.stats.size = 0;
        cache.stats.evictions = 0;
      }
    }

    logger.info('Cache cleared', { cacheType: cacheType || 'all' });
  }

  /**
   * Get cache statistics
   */
  getStats(cacheType = null) {
    if (cacheType) {
      const cache = this.caches.get(cacheType);
      return cache ? { [cacheType]: cache.stats } : null;
    }

    const stats = {};
    for (const [type, cache] of this.caches.entries()) {
      stats[type] = {
        ...cache.stats,
        hitRate: cache.stats.hits + cache.stats.misses > 0
          ? cache.stats.hits / (cache.stats.hits + cache.stats.misses)
          : 0
      };
    }

    return stats;
  }

  /**
   * Cleanup expired entries
   */
  cleanup() {
    const now = Date.now();
    let totalCleaned = 0;

    for (const [cacheType, cache] of this.caches.entries()) {
      if (cache.type === 'redis') continue; // Redis handles its own expiration

      const expiredKeys = [];
      
      for (const [key, metadata] of cache.metadata.entries()) {
        if (now > metadata.expiresAt) {
          expiredKeys.push(key);
        }
      }

      for (const key of expiredKeys) {
        cache.data.delete(key);
        cache.metadata.delete(key);
      }

      cache.stats.size = cache.data.size;
      totalCleaned += expiredKeys.length;

      if (expiredKeys.length > 0) {
        logger.debug('Expired cache entries cleaned', {
          cacheType,
          count: expiredKeys.length
        });
      }
    }

    return totalCleaned;
  }

  /**
   * Get cache health status
   */
  getHealth() {
    const health = {
      status: 'healthy',
      caches: {}
    };

    for (const [type, cache] of this.caches.entries()) {
      const stats = cache.stats;
      const hitRate = stats.hits + stats.misses > 0
        ? stats.hits / (stats.hits + stats.misses)
        : 0;

      health.caches[type] = {
        status: cache.enabled !== false ? 'active' : 'disabled',
        size: stats.size,
        hitRate,
        evictions: stats.evictions
      };

      // Check for potential issues
      if (hitRate < 0.1 && stats.hits + stats.misses > 100) {
        health.caches[type].warning = 'Low hit rate';
      }

      if (stats.evictions > stats.hits) {
        health.caches[type].warning = 'High eviction rate';
      }
    }

    return health;
  }

  /**
   * Shutdown cache manager
   */
  async shutdown() {
    logger.info('Shutting down cache manager');
    
    // Clear all caches
    await this.clear();
    
    // Reset state
    this.caches.clear();
    this.isInitialized = false;
    
    logger.info('Cache manager shutdown completed');
  }
}

export default CacheManager;
