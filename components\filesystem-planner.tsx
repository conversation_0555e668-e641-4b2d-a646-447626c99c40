"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { FolderTree, Folder, File, Image as ImageIcon } from "lucide-react"
import type { ModuleProps } from "@/types/planning"

interface FileSystemNode {
  name: string
  type: "folder" | "file" | "image"
  children?: FileSystemNode[]
  description?: string
  path?: string // Optional path or URL for images
}

export function FilesystemPlanner({ context, onComplete }: ModuleProps) {
  const [filesystem, setFilesystem] = useState<FileSystemNode[]>([])
  const [isGenerating, setIsGenerating] = useState(true)

  useEffect(() => {
    generateFilesystem()
  }, [])

  const generateFilesystem = async () => {
    setIsGenerating(true)

    // Simulate AI generation
    await new Promise((resolve) => setTimeout(resolve, 2000))

    const generatedFilesystem = createFilesystem()
    setFilesystem(generatedFilesystem)
    setIsGenerating(false)
  }

  const createFilesystem = (): FileSystemNode[] => {
    const { techStack, clarifications } = context || {}
    const isReact = techStack?.Frontend === "React" || techStack?.Frontend === "Next.js"
    const isNode = techStack?.Backend === "Node.js"
    const needsAuth = clarifications?.authentication?.includes("Yes")
    // Check if there are uploaded images in the context (this would be passed from the planning agent)
    const hasImages = !!(context as any)?.hasImages || !!(context as any)?.designStyleGuide

    const structure: FileSystemNode[] = [
      {
        name: "project-root",
        type: "folder",
        children: [
          {
            name: "README.md",
            type: "file",
            description: "Project documentation and setup instructions",
          },
          {
            name: "package.json",
            type: "file",
            description: "Node.js dependencies and scripts",
          },
          {
            name: ".env.example",
            type: "file",
            description: "Environment variables template",
          },
          {
            name: ".gitignore",
            type: "file",
            description: "Git ignore rules",
          },
        ],
      },
    ]

    if (isReact) {
      structure[0].children?.push({
        name: "src",
        type: "folder",
        children: [
          {
            name: "components",
            type: "folder",
            children: [
              { name: "Header.jsx", type: "file", description: "Main header component" },
              { name: "Footer.jsx", type: "file", description: "Footer component" },
              { name: "Layout.jsx", type: "file", description: "Main layout wrapper" },
            ],
          },
          {
            name: "pages",
            type: "folder",
            children: [
              { name: "Home.jsx", type: "file", description: "Homepage component" },
              { name: "About.jsx", type: "file", description: "About page component" },
            ],
          },
          {
            name: "hooks",
            type: "folder",
            children: [{ name: "useApi.js", type: "file", description: "Custom API hook" }],
          },
          {
            name: "utils",
            type: "folder",
            children: [
              { name: "helpers.js", type: "file", description: "Utility functions" },
              { name: "constants.js", type: "file", description: "Application constants" },
            ],
          },
          {
            name: "styles",
            type: "folder",
            children: [
              { name: "globals.css", type: "file", description: "Global styles" },
              { name: "components.css", type: "file", description: "Component-specific styles" },
            ],
          },
          { name: "App.jsx", type: "file", description: "Main application component" },
          { name: "index.js", type: "file", description: "Application entry point" },
        ],
      })

      if (needsAuth) {
        structure[0].children
          ?.find((child) => child.name === "src")
          ?.children?.push({
            name: "auth",
            type: "folder",
            children: [
              { name: "Login.jsx", type: "file", description: "Login component" },
              { name: "Register.jsx", type: "file", description: "Registration component" },
              { name: "AuthContext.js", type: "file", description: "Authentication context" },
            ],
          })
      }
    }

    if (isNode) {
      structure[0].children?.push({
        name: "server",
        type: "folder",
        children: [
          { name: "index.js", type: "file", description: "Server entry point" },
          {
            name: "routes",
            type: "folder",
            children: [
              { name: "api.js", type: "file", description: "API routes" },
              ...(needsAuth ? [{ name: "auth.js", type: "file" as const, description: "Authentication routes" }] : []),
            ],
          },
          {
            name: "middleware",
            type: "folder",
            children: [
              { name: "cors.js", type: "file", description: "CORS middleware" },
              ...(needsAuth ? [{ name: "auth.js", type: "file" as const, description: "Auth middleware" }] : []),
            ],
          },
          {
            name: "models",
            type: "folder",
            children: [
              ...(needsAuth ? [{ name: "User.js", type: "file" as const, description: "User data model" }] : []),
              { name: "index.js", type: "file" as const, description: "Model exports" },
            ],
          },
          {
            name: "config",
            type: "folder",
            children: [
              { name: "database.js", type: "file", description: "Database configuration" },
              { name: "server.js", type: "file", description: "Server configuration" },
            ],
          },
        ],
      })
    }

    if (isReact) {
      const publicChildren: FileSystemNode[] = [
        { name: "index.html", type: "file" as const, description: "Main HTML template" },
        { name: "favicon.ico", type: "file" as const, description: "Site favicon" },
        {
          name: "assets",
          type: "folder" as const,
          children: [
            {
              name: "images",
              type: "folder" as const,
              description: "Image assets",
              children: hasImages ? [{ name: "design-image.png", type: "image" as const, description: "User-provided design image (from Gemini)", path: "/assets/images/design-image.png" }] : [],
            },
            { name: "icons", type: "folder" as const, description: "Icon assets" },
          ],
        },
      ]

      structure[0].children?.push({
        name: "public",
        type: "folder",
        children: publicChildren,
      })
    }

    structure[0].children?.push({
      name: "tests",
      type: "folder",
      children: [
        { name: "unit", type: "folder", description: "Unit tests" },
        { name: "integration", type: "folder", description: "Integration tests" },
        { name: "setup.js", type: "file", description: "Test setup configuration" },
      ],
    })

    return structure
  }

  const renderFileTree = (nodes: FileSystemNode[], depth = 0) => {
    return nodes.map((node, index) => (
      <div key={index} className="text-sm">
        <div className={`flex items-center gap-2 py-1 ${depth > 0 ? `ml-${depth * 4}` : ""}`}>
          {node.type === "folder" ? (
            <Folder className="w-4 h-4 text-blue-400" />
          ) : node.type === "image" ? (
            <ImageIcon className="w-4 h-4 text-green-400" />
          ) : (
            <File className="w-4 h-4 text-gray-400" />
          )}
          <span className="text-gray-300 font-mono">{node.name}</span>
          {node.description && <span className="text-xs text-gray-500 ml-2">- {node.description}</span>}
          {node.type === "image" && node.path && (
            <img
              src={node.path}
              alt={node.name}
              className="ml-2 w-8 h-8 object-cover rounded"
            />
          )}
        </div>
        {node.children && renderFileTree(node.children, depth + 1)}
      </div>
    ))
  }

  const handleContinue = () => {
    onComplete(filesystem)
  }

  return (
    <Card className="border-slate-700" style={{backgroundColor: '#818181'}}>
      <CardHeader>
        <div className="flex items-center gap-2">
          <FolderTree className="w-5 h-5 text-purple-400" />
          <CardTitle className="text-white">Project File Structure</CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        {isGenerating ? (
          <div className="flex items-center justify-center py-12">
            <div className="flex items-center gap-3">
              <FolderTree className="w-5 h-5 text-purple-400 animate-pulse" />
              <span className="text-gray-300">Planning project file structure...</span>
            </div>
          </div>
        ) : (
          <>
            <div className="bg-slate-900 p-4 rounded-lg max-h-96 overflow-y-auto">{renderFileTree(filesystem)}</div>

            <Button onClick={handleContinue} className="w-full mt-6 bg-purple-600 hover:bg-purple-700">
              Approve File Structure & Continue
            </Button>
          </>
        )}
      </CardContent>
    </Card>
  )
}
