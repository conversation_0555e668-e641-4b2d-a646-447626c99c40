/**
 * Integration tests for API endpoints
 */

import { jest } from '@jest/globals';
import request from 'supertest';

// Mock the entire context engine to avoid database dependencies
const mockContextEngine = {
  isInitialized: true,
  getContext: jest.fn(),
  processCodebase: jest.fn(),
  getStatistics: jest.fn(),
  getHealth: jest.fn(),
  neo4jClient: {
    run: jest.fn()
  }
};

// Mock the Application class
jest.unstable_mockModule('../../src/index.js', () => ({
  Application: class MockApplication {
    constructor() {
      this.app = null;
    }
    
    async initialize() {
      const express = (await import('express')).default;
      this.app = express();
      
      // Set up basic middleware
      this.app.use(express.json());
      this.app.set('contextEngine', mockContextEngine);
      
      // Import and setup routes
      const contextRoutes = await import('../../src/api/routes/contextRoutes.js');
      const healthRoutes = await import('../../src/api/routes/healthRoutes.js');
      
      this.app.use('/api/context', contextRoutes.default);
      this.app.use('/api/health', healthRoutes.default);
      
      // Root endpoint
      this.app.get('/', (req, res) => {
        res.json({ status: 'ok' });
      });
    }
  }
}));

describe('API Integration Tests', () => {
  let app;
  let Application;

  beforeAll(async () => {
    const module = await import('../../src/index.js');
    Application = module.Application;
    
    const appInstance = new Application();
    await appInstance.initialize();
    app = appInstance.app;
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Health Endpoints', () => {
    test('GET /api/health should return health status', async () => {
      mockContextEngine.getHealth.mockResolvedValue({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: 1000,
        components: {
          neo4j: { status: 'healthy' },
          ingestion: { status: 'healthy' },
          retrieval: { status: 'healthy' }
        }
      });

      const response = await request(app)
        .get('/api/health')
        .expect(200);

      expect(response.body.status).toBe('healthy');
      expect(response.body.components).toBeDefined();
      expect(mockContextEngine.getHealth).toHaveBeenCalled();
    });

    test('GET /api/health should return 503 when unhealthy', async () => {
      mockContextEngine.getHealth.mockResolvedValue({
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        components: {
          neo4j: { status: 'unhealthy' }
        }
      });

      const response = await request(app)
        .get('/api/health')
        .expect(503);

      expect(response.body.status).toBe('unhealthy');
    });

    test('GET /api/health/detailed should return detailed health info', async () => {
      mockContextEngine.getHealth.mockResolvedValue({
        status: 'healthy',
        components: {}
      });
      
      mockContextEngine.getStatistics.mockResolvedValue({
        database: { nodeCount: 100 },
        ingestion: { filesProcessed: 50 }
      });

      const response = await request(app)
        .get('/api/health/detailed')
        .expect(200);

      expect(response.body.statistics).toBeDefined();
      expect(response.body.system).toBeDefined();
    });

    test('GET /api/health/ready should check readiness', async () => {
      mockContextEngine.getHealth.mockResolvedValue({
        status: 'healthy',
        components: {
          neo4j: { status: 'healthy' }
        }
      });

      const response = await request(app)
        .get('/api/health/ready')
        .expect(200);

      expect(response.body.ready).toBe(true);
    });

    test('GET /api/health/live should always return alive', async () => {
      const response = await request(app)
        .get('/api/health/live')
        .expect(200);

      expect(response.body.alive).toBe(true);
      expect(response.body.uptime).toBeDefined();
    });
  });

  describe('Context Query Endpoints', () => {
    test('POST /api/context/query should process valid query', async () => {
      const mockResult = {
        results: [
          {
            id: 1,
            type: 'function',
            name: 'testFunction',
            file: { path: 'test.js', language: 'javascript' },
            score: 0.9
          }
        ],
        total: 1,
        sources: ['graph'],
        processingTime: 150
      };

      mockContextEngine.getContext.mockResolvedValue(mockResult);

      const response = await request(app)
        .post('/api/context/query')
        .send({
          query: 'find test functions',
          options: { language: 'javascript', limit: 10 }
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.results).toHaveLength(1);
      expect(response.body.data.results[0].name).toBe('testFunction');
      expect(mockContextEngine.getContext).toHaveBeenCalledWith(
        'find test functions',
        expect.objectContaining({
          language: 'javascript',
          limit: 10,
          originalQuery: 'find test functions'
        })
      );
    });

    test('POST /api/context/query should validate request body', async () => {
      const response = await request(app)
        .post('/api/context/query')
        .send({
          // Missing required 'query' field
          options: { language: 'javascript' }
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.type).toBe('VALIDATION_ERROR');
    });

    test('POST /api/context/query should handle engine errors', async () => {
      mockContextEngine.getContext.mockRejectedValue(new Error('Engine error'));

      const response = await request(app)
        .post('/api/context/query')
        .send({
          query: 'test query'
        })
        .expect(500);

      expect(response.body.success).toBe(false);
      expect(response.body.error.type).toBe('CONTEXT_QUERY_ERROR');
    });
  });

  describe('Repository Processing Endpoints', () => {
    test('POST /api/context/repositories/process should process repository', async () => {
      const mockResult = {
        repositoryPath: '/test/repo',
        filesProcessed: 25,
        nodesCreated: 100,
        relationshipsCreated: 50,
        duration: 5000
      };

      mockContextEngine.processCodebase.mockResolvedValue(mockResult);

      const response = await request(app)
        .post('/api/context/repositories/process')
        .send({
          repositoryPath: '/test/repo',
          options: { batchSize: 50 }
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.filesProcessed).toBe(25);
      expect(response.body.data.nodesCreated).toBe(100);
      expect(mockContextEngine.processCodebase).toHaveBeenCalledWith(
        '/test/repo',
        { batchSize: 50 }
      );
    });

    test('POST /api/context/repositories/process should validate repository path', async () => {
      const response = await request(app)
        .post('/api/context/repositories/process')
        .send({
          repositoryPath: 'relative/path', // Should be absolute
          options: {}
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.message).toContain('absolute path');
    });
  });

  describe('Statistics Endpoints', () => {
    test('GET /api/context/statistics should return engine statistics', async () => {
      const mockStats = {
        timestamp: new Date().toISOString(),
        uptime: 10000,
        database: {
          nodeCount: 1000,
          relationshipCount: 500,
          fileCount: 100
        },
        ingestion: {
          filesProcessed: 100,
          processingRate: 2.5
        }
      };

      mockContextEngine.getStatistics.mockResolvedValue(mockStats);

      const response = await request(app)
        .get('/api/context/statistics')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.database.nodeCount).toBe(1000);
      expect(response.body.data.ingestion.filesProcessed).toBe(100);
    });
  });

  describe('File Search Endpoints', () => {
    test('GET /api/context/files/search should search files', async () => {
      const mockResult = {
        results: [
          {
            id: 1,
            type: 'File',
            name: 'test.js',
            file: { path: 'src/test.js', language: 'javascript' }
          }
        ],
        total: 1
      };

      mockContextEngine.getContext.mockResolvedValue(mockResult);

      const response = await request(app)
        .get('/api/context/files/search')
        .query({ q: 'test', language: 'javascript', limit: 20 })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.files).toHaveLength(1);
      expect(response.body.data.query).toBe('test');
    });

    test('GET /api/context/files/search should require query parameter', async () => {
      const response = await request(app)
        .get('/api/context/files/search')
        .query({ language: 'javascript' }) // Missing 'q' parameter
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.message).toContain('Query parameter "q" is required');
    });
  });

  describe('Element Details Endpoints', () => {
    test('GET /api/context/elements/:id should return element details', async () => {
      const mockResult = {
        records: [{
          get: jest.fn((key) => {
            if (key === 'n') {
              return {
                identity: { toNumber: () => 123 },
                labels: ['Function'],
                properties: { name: 'testFunction', startLine: 1 }
              };
            }
            if (key === 'f') {
              return {
                properties: { path: 'test.js', language: 'javascript' }
              };
            }
          })
        }]
      };

      mockContextEngine.neo4jClient.run.mockResolvedValue(mockResult);

      const response = await request(app)
        .get('/api/context/elements/123')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.id).toBe(123);
      expect(response.body.data.labels).toContain('Function');
      expect(response.body.data.properties.name).toBe('testFunction');
    });

    test('GET /api/context/elements/:id should return 404 for non-existent element', async () => {
      mockContextEngine.neo4jClient.run.mockResolvedValue({ records: [] });

      const response = await request(app)
        .get('/api/context/elements/999')
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error.type).toBe('NOT_FOUND');
    });

    test('GET /api/context/elements/:id should validate ID parameter', async () => {
      const response = await request(app)
        .get('/api/context/elements/invalid')
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.type).toBe('VALIDATION_ERROR');
    });
  });

  describe('Root Endpoint', () => {
    test('GET / should return basic info', async () => {
      const response = await request(app)
        .get('/')
        .expect(200);

      expect(response.body.status).toBe('ok');
    });
  });

  describe('Error Handling', () => {
    test('should handle 404 for unknown endpoints', async () => {
      const response = await request(app)
        .get('/api/unknown')
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error.type).toBe('NOT_FOUND');
    });
  });
});
