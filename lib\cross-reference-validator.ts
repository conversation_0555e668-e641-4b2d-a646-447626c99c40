import type { ProjectContext } from "@/types/planning"
import type { ContextIntegrityReport, ContextIntegrityIssue } from "@/types/context-engine"
import {
  TechStackConsistencyRule,
  RequirementsCoverageRule,
  WireframeAlignmentRule,
  DatabaseSchemaConsistencyRule,
  FileSystemConventionRule,
  WorkflowLogicRule,
  SecurityRequirementsRule,
  PerformanceRequirementsRule
} from "./validation-rules"
import {
  TechStackPRDAlignmentChecker,
  WireframePRDAlignmentChecker,
  DatabasePRDAlignmentChecker,
  FileSystemTechStackAlignmentChecker,
  WorkflowRequirementsAlignmentChecker,
  TasksImplementationAlignmentChecker
} from "./alignment-checkers"

/**
 * Cross-Reference Validator Engine
 * Auto-checks that wireframes, tech stack, architecture, and PRD align without contradiction
 */
export class CrossReferenceValidator {
  private validationRules: ValidationRule[] = []
  private alignmentCheckers: AlignmentChecker[] = []

  constructor() {
    this.initializeValidationRules()
    this.initializeAlignmentCheckers()
  }

  /**
   * Validate all cross-references in the project context
   */
  async validateCrossReferences(context: ProjectContext): Promise<CrossReferenceReport> {
    const issues: CrossReferenceIssue[] = []
    const alignmentScore = { total: 0, passed: 0 }

    // Run all validation rules
    for (const rule of this.validationRules) {
      try {
        const ruleResult = await rule.validate(context)
        if (!ruleResult.isValid) {
          issues.push(...ruleResult.issues.map(issue => ({
            ...issue,
            rule: rule.id,
            category: rule.category
          })))
        }
        alignmentScore.total++
        if (ruleResult.isValid) alignmentScore.passed++
      } catch (error) {
        issues.push({
          type: 'validation_error',
          severity: 'high',
          message: `Validation rule ${rule.id} failed: ${error}`,
          rule: rule.id,
          category: rule.category,
          fromStep: '',
          toStep: '',
          field: ''
        })
      }
    }

    // Run alignment checkers
    for (const checker of this.alignmentCheckers) {
      try {
        const alignmentResult = await checker.check(context)
        if (!alignmentResult.isAligned) {
          issues.push(...alignmentResult.misalignments.map(misalignment => ({
            type: 'alignment_mismatch',
            severity: misalignment.severity,
            message: misalignment.message,
            rule: checker.id,
            category: 'alignment',
            fromStep: misalignment.fromStep,
            toStep: misalignment.toStep,
            field: misalignment.field,
            suggestedFix: misalignment.suggestedFix
          })))
        }
        alignmentScore.total++
        if (alignmentResult.isAligned) alignmentScore.passed++
      } catch (error) {
        issues.push({
          type: 'alignment_error',
          severity: 'high',
          message: `Alignment checker ${checker.id} failed: ${error}`,
          rule: checker.id,
          category: 'alignment',
          fromStep: '',
          toStep: '',
          field: ''
        })
      }
    }

    const score = alignmentScore.total > 0 ? Math.round((alignmentScore.passed / alignmentScore.total) * 100) : 0
    const recommendations = this.generateRecommendations(issues)

    return {
      isValid: issues.length === 0,
      score,
      issues,
      recommendations,
      summary: {
        totalChecks: alignmentScore.total,
        passedChecks: alignmentScore.passed,
        criticalIssues: issues.filter(i => i.severity === 'critical').length,
        highIssues: issues.filter(i => i.severity === 'high').length,
        mediumIssues: issues.filter(i => i.severity === 'medium').length,
        lowIssues: issues.filter(i => i.severity === 'low').length
      }
    }
  }

  /**
   * Initialize validation rules
   */
  private initializeValidationRules(): void {
    this.validationRules = [
      new TechStackConsistencyRule(),
      new RequirementsCoverageRule(),
      new WireframeAlignmentRule(),
      new DatabaseSchemaConsistencyRule(),
      new FileSystemConventionRule(),
      new WorkflowLogicRule(),
      new SecurityRequirementsRule(),
      new PerformanceRequirementsRule()
    ]
  }

  /**
   * Initialize alignment checkers
   */
  private initializeAlignmentCheckers(): void {
    this.alignmentCheckers = [
      new TechStackPRDAlignmentChecker(),
      new WireframePRDAlignmentChecker(),
      new DatabasePRDAlignmentChecker(),
      new FileSystemTechStackAlignmentChecker(),
      new WorkflowRequirementsAlignmentChecker(),
      new TasksImplementationAlignmentChecker()
    ]
  }

  /**
   * Generate recommendations based on validation issues
   */
  private generateRecommendations(issues: CrossReferenceIssue[]): string[] {
    const recommendations: string[] = []
    const issuesByCategory = this.groupIssuesByCategory(issues)

    // Critical issues recommendations
    if (issuesByCategory.critical.length > 0) {
      recommendations.push('🚨 Critical alignment issues detected - immediate attention required')
      recommendations.push('Review and resolve all critical misalignments before proceeding')
    }

    // Tech stack recommendations
    if (issuesByCategory.techstack.length > 0) {
      recommendations.push('🔧 Tech stack misalignments detected - ensure technology choices support all requirements')
    }

    // Requirements recommendations
    if (issuesByCategory.requirements.length > 0) {
      recommendations.push('📋 Requirements coverage gaps found - ensure all features are properly specified')
    }

    // Wireframe recommendations
    if (issuesByCategory.wireframes.length > 0) {
      recommendations.push('🎨 Wireframe misalignments detected - ensure UI designs match requirements')
    }

    // Database recommendations
    if (issuesByCategory.database.length > 0) {
      recommendations.push('🗄️ Database schema issues found - ensure data model supports all features')
    }

    // General recommendations
    if (issues.length > 5) {
      recommendations.push('Consider reviewing the overall project scope and complexity')
    }

    if (recommendations.length === 0) {
      recommendations.push('✅ All cross-references are properly aligned - excellent planning quality!')
    }

    return recommendations
  }

  /**
   * Group issues by category for better analysis
   */
  private groupIssuesByCategory(issues: CrossReferenceIssue[]): Record<string, CrossReferenceIssue[]> {
    const groups: Record<string, CrossReferenceIssue[]> = {
      critical: [],
      techstack: [],
      requirements: [],
      wireframes: [],
      database: [],
      filesystem: [],
      workflow: [],
      security: [],
      performance: []
    }

    issues.forEach(issue => {
      if (issue.severity === 'critical') {
        groups.critical.push(issue)
      }
      
      const category = issue.category.toLowerCase()
      if (groups[category]) {
        groups[category].push(issue)
      }
    })

    return groups
  }
}

// Type definitions for the validator
export interface ValidationRule {
  id: string
  category: string
  description: string
  validate(context: ProjectContext): Promise<ValidationResult>
}

export interface ValidationResult {
  isValid: boolean
  issues: ContextIntegrityIssue[]
}

export interface AlignmentChecker {
  id: string
  description: string
  check(context: ProjectContext): Promise<AlignmentResult>
}

export interface AlignmentResult {
  isAligned: boolean
  misalignments: Misalignment[]
}

export interface Misalignment {
  fromStep: string
  toStep: string
  field: string
  message: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  suggestedFix?: string
}

export interface CrossReferenceIssue extends ContextIntegrityIssue {
  rule: string
  category: string
  fromStep: string
  toStep: string
  field: string
  suggestedFix?: string
}

export interface CrossReferenceReport {
  isValid: boolean
  score: number
  issues: CrossReferenceIssue[]
  recommendations: string[]
  summary: {
    totalChecks: number
    passedChecks: number
    criticalIssues: number
    highIssues: number
    mediumIssues: number
    lowIssues: number
  }
}
