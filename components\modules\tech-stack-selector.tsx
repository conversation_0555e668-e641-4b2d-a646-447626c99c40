"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Code, Cpu, Database, Cloud, Smartphone } from "lucide-react"
import type { ModuleProps } from "@/types/planning"

interface TechOption {
  category: string
  options: string[]
  recommended: string
  icon: any
}

export function TechStackSelector({ context, onComplete }: ModuleProps) {
  const [techStack, setTechStack] = useState<Record<string, string>>({})
  const [techOptions, setTechOptions] = useState<TechOption[]>([])
  const [isGenerating, setIsGenerating] = useState(true)

  useEffect(() => {
    generateTechOptions()
  }, [])

  const generateTechOptions = async () => {
    setIsGenerating(true)

    // Simulate AI analysis
    await new Promise((resolve) => setTimeout(resolve, 1500))

    const options = createTechOptions()
    setTechOptions(options)

    // Set recommended options as default
    const defaultStack: Record<string, string> = {}
    options.forEach((option) => {
      defaultStack[option.category] = option.recommended
    })
    setTechStack(defaultStack)

    setIsGenerating(false)
  }

  const createTechOptions = (): TechOption[] => {
    const isWebApp =
      context?.clarifications?.platform?.includes("web") || context?.clarifications?.platform === "Both web and mobile"
    const isMobile =
      context?.clarifications?.platform?.includes("mobile") ||
      context?.clarifications?.platform === "Both web and mobile"
    const needsAuth = context?.clarifications?.authentication?.includes("Yes")

    const options: TechOption[] = []

    if (isWebApp) {
      options.push({
        category: "Frontend",
        options: ["React", "Vue.js", "Angular", "Svelte", "Next.js"],
        recommended: "React",
        icon: Code,
      })

      options.push({
        category: "Backend",
        options: ["Node.js", "Python (Django)", "Python (FastAPI)", "Ruby on Rails", "Go"],
        recommended: "Node.js",
        icon: Cpu,
      })
    }

    if (isMobile) {
      options.push({
        category: "Mobile",
        options: ["React Native", "Flutter", "Native iOS/Android", "Ionic"],
        recommended: "React Native",
        icon: Smartphone,
      })
    }

    options.push({
      category: "Database",
      options: ["PostgreSQL", "MongoDB", "MySQL", "SQLite", "Firebase"],
      recommended: needsAuth ? "PostgreSQL" : "MongoDB",
      icon: Database,
    })

    options.push({
      category: "Hosting",
      options: ["Vercel", "Netlify", "AWS", "Google Cloud", "Heroku"],
      recommended: "Vercel",
      icon: Cloud,
    })

    if (needsAuth) {
      options.push({
        category: "Authentication",
        options: ["Auth0", "Firebase Auth", "NextAuth.js", "Supabase Auth", "Custom JWT"],
        recommended: "NextAuth.js",
        icon: Code,
      })
    }

    return options
  }

  const handleTechChange = (category: string, tech: string) => {
    setTechStack((prev) => ({ ...prev, [category]: tech }))
  }

  const handleContinue = () => {
    onComplete(techStack)
  }

  return (
    <Card className="border-slate-700" style={{backgroundColor: '#818181'}}>
      <CardHeader>
        <div className="flex items-center gap-2">
          <Code className="w-5 h-5 text-red-400" />
          <CardTitle className="text-white">Technology Stack Selection</CardTitle>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {isGenerating ? (
          <div className="flex items-center justify-center py-8">
            <div className="flex items-center gap-3">
              <Code className="w-5 h-5 text-red-400 animate-pulse" />
              <span className="text-gray-300">Analyzing requirements and suggesting technologies...</span>
            </div>
          </div>
        ) : (
          <>
            <div className="grid gap-6">
              {techOptions.map((option) => {
                const Icon = option.icon
                return (
                  <div key={option.category} className="space-y-3">
                    <div className="flex items-center gap-2">
                      <Icon className="w-4 h-4 text-red-400" />
                      <h4 className="text-white font-semibold">{option.category}</h4>
                      <Badge variant="outline" className="border-green-400 text-green-300 text-xs">
                        Recommended: {option.recommended}
                      </Badge>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {option.options.map((tech) => (
                        <Button
                          key={tech}
                          onClick={() => handleTechChange(option.category, tech)}
                          variant={techStack[option.category] === tech ? "default" : "outline"}
                          size="sm"
                          className={
                            techStack[option.category] === tech
                              ? "bg-red-600 hover:bg-red-700"
                              : "border-slate-600 text-gray-300 hover:bg-slate-700"
                          }
                        >
                          {tech}
                        </Button>
                      ))}
                    </div>
                  </div>
                )
              })}
            </div>

            <div className="bg-slate-700/50 p-4 rounded-lg">
              <h4 className="text-white font-semibold mb-3">Selected Tech Stack</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {Object.entries(techStack).map(([category, tech]) => (
                  <div key={category} className="flex justify-between items-center">
                    <span className="text-gray-300">{category}:</span>
                    <Badge className="bg-purple-600 text-white">{tech}</Badge>
                  </div>
                ))}
              </div>
            </div>

            <Button onClick={handleContinue} className="w-full bg-purple-600 hover:bg-purple-700">
              Continue with Selected Stack
            </Button>
          </>
        )}
      </CardContent>
    </Card>
  )
}
