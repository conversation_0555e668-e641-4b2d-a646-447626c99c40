/**
 * Intelligent Query Processor for Agentic RAG
 * Processes natural language queries and converts them to actionable search strategies
 */

import type { AgenticQuery, CodeIntent, CodebaseMemory } from './agentic-rag-engine'

export interface ProcessedQuery {
  originalQuery: string
  intent: CodeIntent
  semanticTokens: string[]
  contextualHints: string[]
  searchStrategy: SearchStrategy
  filters: QueryFilter[]
  expansions: string[]
  confidence: number
}

export interface SearchStrategy {
  primary: 'semantic' | 'structural' | 'dependency' | 'hybrid'
  secondary?: 'semantic' | 'structural' | 'dependency'
  weights: {
    semantic: number
    structural: number
    dependency: number
    recency: number
  }
  scope: 'local' | 'module' | 'global'
}

export interface QueryFilter {
  type: 'file-type' | 'complexity' | 'recency' | 'semantic-tag' | 'path-pattern'
  value: string | number
  operator: 'equals' | 'contains' | 'greater-than' | 'less-than' | 'matches'
}

export interface QueryExpansion {
  original: string
  expanded: string[]
  synonyms: string[]
  relatedConcepts: string[]
  technicalTerms: string[]
}

/**
 * Intelligent Query Processor
 */
export class QueryProcessor {
  private memory: CodebaseMemory
  private queryPatterns: Map<string, any> = new Map()
  private technicalVocabulary: Map<string, string[]> = new Map()
  private queryHistory: Array<{ query: string; results: any; timestamp: Date }> = []

  constructor(memory: CodebaseMemory) {
    this.memory = memory
    this.initializeQueryPatterns()
    this.initializeTechnicalVocabulary()
  }

  /**
   * Process a natural language query into structured search parameters
   */
  async processQuery(query: AgenticQuery): Promise<ProcessedQuery> {
    // Extract semantic tokens
    const semanticTokens = this.extractSemanticTokens(query.query)
    
    // Analyze intent with enhanced context
    const enhancedIntent = await this.enhanceIntent(query.intent, query.context)
    
    // Generate contextual hints
    const contextualHints = this.generateContextualHints(query, semanticTokens)
    
    // Determine search strategy
    const searchStrategy = this.determineSearchStrategy(enhancedIntent, query.context)
    
    // Apply intelligent filters
    const filters = this.generateIntelligentFilters(query, semanticTokens)
    
    // Generate query expansions
    const expansions = this.generateQueryExpansions(query.query, semanticTokens)
    
    // Calculate confidence
    const confidence = this.calculateQueryConfidence(query, semanticTokens)

    const processedQuery: ProcessedQuery = {
      originalQuery: query.query,
      intent: enhancedIntent,
      semanticTokens,
      contextualHints,
      searchStrategy,
      filters,
      expansions,
      confidence
    }

    // Store in query history for learning
    this.updateQueryHistory(processedQuery)

    return processedQuery
  }

  /**
   * Expand query with synonyms and related terms
   */
  async expandQuery(query: string, context: any = {}): Promise<QueryExpansion> {
    const tokens = this.tokenizeQuery(query)
    const expanded: string[] = []
    const synonyms: string[] = []
    const relatedConcepts: string[] = []
    const technicalTerms: string[] = []

    for (const token of tokens) {
      // Find synonyms
      const tokenSynonyms = this.findSynonyms(token)
      synonyms.push(...tokenSynonyms)
      
      // Find related technical terms
      const techTerms = this.findTechnicalTerms(token)
      technicalTerms.push(...techTerms)
      
      // Find related concepts
      const concepts = this.findRelatedConcepts(token, context)
      relatedConcepts.push(...concepts)
    }

    // Generate expanded queries
    expanded.push(...this.generateExpandedQueries(query, synonyms, technicalTerms))

    return {
      original: query,
      expanded: [...new Set(expanded)],
      synonyms: [...new Set(synonyms)],
      relatedConcepts: [...new Set(relatedConcepts)],
      technicalTerms: [...new Set(technicalTerms)]
    }
  }

  /**
   * Suggest query improvements based on results
   */
  async suggestQueryImprovements(
    originalQuery: string, 
    results: any[], 
    userFeedback?: { helpful: boolean; tooMany: boolean; tooFew: boolean }
  ): Promise<{
    suggestions: string[]
    alternativeQueries: string[]
    refinements: string[]
  }> {
    
    const suggestions: string[] = []
    const alternativeQueries: string[] = []
    const refinements: string[] = []

    // Analyze results and feedback
    if (userFeedback?.tooMany || results.length > 20) {
      suggestions.push('Try adding more specific terms to narrow your search')
      refinements.push(`${originalQuery} in specific file`)
      refinements.push(`${originalQuery} with high complexity`)
    }

    if (userFeedback?.tooFew || results.length < 3) {
      suggestions.push('Try broadening your search terms')
      alternativeQueries.push(...this.generateBroaderQueries(originalQuery))
    }

    if (!userFeedback?.helpful) {
      suggestions.push('Consider using different technical terms')
      alternativeQueries.push(...this.generateAlternativeQueries(originalQuery))
    }

    return { suggestions, alternativeQueries, refinements }
  }

  /**
   * Learn from query patterns and improve processing
   */
  async learnFromQueryPattern(
    query: string, 
    results: any[], 
    userInteractions: { clicked: string[]; ignored: string[]; rated: Array<{ item: string; rating: number }> }
  ): Promise<void> {
    
    // Extract successful patterns
    const successfulPatterns = this.extractSuccessfulPatterns(query, userInteractions.clicked)
    
    // Update query patterns
    successfulPatterns.forEach(pattern => {
      const existing = this.queryPatterns.get(pattern.type) || { examples: [], weight: 1 }
      existing.examples.push(pattern.example)
      existing.weight = Math.min(existing.weight + 0.1, 2.0)
      this.queryPatterns.set(pattern.type, existing)
    })

    // Learn from negative feedback
    const ignoredPatterns = this.extractIgnoredPatterns(query, userInteractions.ignored)
    ignoredPatterns.forEach(pattern => {
      const existing = this.queryPatterns.get(pattern.type) || { examples: [], weight: 1 }
      existing.weight = Math.max(existing.weight - 0.05, 0.1)
      this.queryPatterns.set(pattern.type, existing)
    })

    // Update memory with learned patterns
    this.memory.longTerm.set(`query_pattern_${Date.now()}`, {
      query,
      successfulPatterns,
      ignoredPatterns,
      timestamp: new Date().toISOString()
    })
  }

  // Private helper methods
  private initializeQueryPatterns(): void {
    this.queryPatterns.set('find-function', {
      keywords: ['find', 'search', 'function', 'method'],
      intent: 'find',
      target: 'function',
      weight: 1.0
    })

    this.queryPatterns.set('understand-component', {
      keywords: ['understand', 'explain', 'how', 'component', 'class'],
      intent: 'understand',
      target: 'class',
      weight: 1.0
    })

    this.queryPatterns.set('debug-issue', {
      keywords: ['debug', 'fix', 'error', 'bug', 'issue'],
      intent: 'debug',
      target: 'bug',
      weight: 1.0
    })

    this.queryPatterns.set('modify-code', {
      keywords: ['modify', 'change', 'update', 'refactor'],
      intent: 'modify',
      target: 'function',
      weight: 1.0
    })
  }

  private initializeTechnicalVocabulary(): void {
    this.technicalVocabulary.set('react', [
      'component', 'hook', 'state', 'props', 'jsx', 'tsx', 'render', 'effect'
    ])

    this.technicalVocabulary.set('api', [
      'endpoint', 'route', 'handler', 'middleware', 'request', 'response', 'http', 'rest'
    ])

    this.technicalVocabulary.set('database', [
      'query', 'model', 'schema', 'migration', 'orm', 'sql', 'nosql', 'crud'
    ])

    this.technicalVocabulary.set('testing', [
      'test', 'spec', 'mock', 'stub', 'assertion', 'coverage', 'unit', 'integration'
    ])
  }

  private extractSemanticTokens(query: string): string[] {
    const tokens = this.tokenizeQuery(query)
    const semanticTokens: string[] = []

    tokens.forEach(token => {
      // Filter out stop words and keep meaningful terms
      if (token.length > 2 && !this.isStopWord(token)) {
        semanticTokens.push(token.toLowerCase())
      }
    })

    return semanticTokens
  }

  private async enhanceIntent(intent: CodeIntent, context: any): Promise<CodeIntent> {
    const enhancedIntent = { ...intent }

    // Enhance based on context
    if (context.currentFile) {
      const fileType = this.detectFileType(context.currentFile)
      if (fileType) {
        enhancedIntent.context.push(fileType)
      }
    }

    // Enhance based on recent activity
    if (context.recentFiles) {
      const recentPatterns = this.analyzeRecentPatterns(context.recentFiles)
      enhancedIntent.context.push(...recentPatterns)
    }

    return enhancedIntent
  }

  private generateContextualHints(query: AgenticQuery, semanticTokens: string[]): string[] {
    const hints: string[] = []

    // Add hints based on semantic tokens
    semanticTokens.forEach(token => {
      if (this.technicalVocabulary.has(token)) {
        hints.push(`Related to ${token} development`)
      }
    })

    // Add hints based on context
    if (query.context.currentFile) {
      hints.push(`Consider current file context: ${query.context.currentFile}`)
    }

    // Add hints based on working memory
    if (query.context.workingMemory && query.context.workingMemory.length > 0) {
      hints.push('Leveraging recent exploration context')
    }

    return hints
  }

  private determineSearchStrategy(intent: CodeIntent, context: any): SearchStrategy {
    const strategy: SearchStrategy = {
      primary: 'semantic',
      weights: {
        semantic: 0.6,
        structural: 0.2,
        dependency: 0.1,
        recency: 0.1
      },
      scope: 'global'
    }

    // Adjust strategy based on intent
    switch (intent.type) {
      case 'find':
        strategy.primary = 'semantic'
        strategy.weights.semantic = 0.7
        break
      case 'understand':
        strategy.primary = 'dependency'
        strategy.secondary = 'semantic'
        strategy.weights.dependency = 0.5
        strategy.weights.semantic = 0.3
        break
      case 'debug':
        strategy.primary = 'hybrid'
        strategy.weights.structural = 0.4
        strategy.weights.semantic = 0.4
        break
      case 'modify':
        strategy.primary = 'structural'
        strategy.weights.structural = 0.5
        strategy.weights.dependency = 0.3
        break
    }

    // Adjust scope based on context
    if (context.currentFile) {
      strategy.scope = 'module'
      strategy.weights.recency = 0.2
    }

    return strategy
  }

  private generateIntelligentFilters(query: AgenticQuery, semanticTokens: string[]): QueryFilter[] {
    const filters: QueryFilter[] = []

    // Add filters based on intent
    if (query.intent.target === 'function') {
      filters.push({
        type: 'semantic-tag',
        value: 'function',
        operator: 'contains'
      })
    }

    // Add filters based on semantic tokens
    semanticTokens.forEach(token => {
      if (token.includes('test')) {
        filters.push({
          type: 'path-pattern',
          value: 'test',
          operator: 'contains'
        })
      }
    })

    // Add complexity filters for understanding queries
    if (query.intent.type === 'understand') {
      filters.push({
        type: 'complexity',
        value: 5,
        operator: 'greater-than'
      })
    }

    return filters
  }

  private generateQueryExpansions(query: string, semanticTokens: string[]): string[] {
    const expansions: string[] = []

    // Generate variations with synonyms
    semanticTokens.forEach(token => {
      const synonyms = this.findSynonyms(token)
      synonyms.forEach(synonym => {
        expansions.push(query.replace(token, synonym))
      })
    })

    // Generate technical variations
    semanticTokens.forEach(token => {
      const techTerms = this.findTechnicalTerms(token)
      techTerms.forEach(term => {
        expansions.push(`${query} ${term}`)
      })
    })

    return expansions.slice(0, 5) // Limit expansions
  }

  private calculateQueryConfidence(query: AgenticQuery, semanticTokens: string[]): number {
    let confidence = 0.5

    // Higher confidence for specific technical terms
    const techTermCount = semanticTokens.filter(token => 
      Array.from(this.technicalVocabulary.values()).flat().includes(token)
    ).length
    confidence += techTermCount * 0.1

    // Higher confidence for clear intent
    if (query.intent.type !== 'find') {
      confidence += 0.2
    }

    // Higher confidence with context
    if (query.context.currentFile) {
      confidence += 0.1
    }

    return Math.min(confidence, 1.0)
  }

  private updateQueryHistory(processedQuery: ProcessedQuery): void {
    this.queryHistory.push({
      query: processedQuery.originalQuery,
      results: processedQuery,
      timestamp: new Date()
    })

    // Keep only recent history
    if (this.queryHistory.length > 100) {
      this.queryHistory = this.queryHistory.slice(-50)
    }
  }

  private tokenizeQuery(query: string): string[] {
    return query
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(token => token.length > 0)
  }

  private isStopWord(word: string): boolean {
    const stopWords = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by']
    return stopWords.includes(word.toLowerCase())
  }

  private findSynonyms(token: string): string[] {
    const synonymMap: Record<string, string[]> = {
      'function': ['method', 'procedure', 'routine'],
      'class': ['component', 'object', 'entity'],
      'find': ['search', 'locate', 'discover'],
      'fix': ['repair', 'resolve', 'correct'],
      'create': ['make', 'build', 'generate']
    }

    return synonymMap[token.toLowerCase()] || []
  }

  private findTechnicalTerms(token: string): string[] {
    for (const [category, terms] of this.technicalVocabulary) {
      if (terms.includes(token.toLowerCase())) {
        return terms.filter(term => term !== token.toLowerCase()).slice(0, 3)
      }
    }
    return []
  }

  private findRelatedConcepts(token: string, context: any): string[] {
    const concepts: string[] = []

    // Add concepts based on current context
    if (context.currentFile) {
      const fileType = this.detectFileType(context.currentFile)
      if (fileType && this.technicalVocabulary.has(fileType)) {
        concepts.push(...this.technicalVocabulary.get(fileType)!.slice(0, 2))
      }
    }

    return concepts
  }

  private generateExpandedQueries(original: string, synonyms: string[], technicalTerms: string[]): string[] {
    const expanded: string[] = []

    // Add queries with synonyms
    synonyms.slice(0, 2).forEach(synonym => {
      expanded.push(original.replace(/\b\w+\b/, synonym))
    })

    // Add queries with technical terms
    technicalTerms.slice(0, 2).forEach(term => {
      expanded.push(`${original} ${term}`)
    })

    return expanded
  }

  private generateBroaderQueries(query: string): string[] {
    const tokens = this.tokenizeQuery(query)
    const broader: string[] = []

    // Remove specific terms to broaden
    if (tokens.length > 1) {
      broader.push(tokens.slice(0, -1).join(' '))
      broader.push(tokens.slice(1).join(' '))
    }

    // Add general terms
    broader.push(`${query} example`)
    broader.push(`${query} pattern`)

    return broader
  }

  private generateAlternativeQueries(query: string): string[] {
    const alternatives: string[] = []
    const tokens = this.tokenizeQuery(query)

    // Generate alternatives with synonyms
    tokens.forEach(token => {
      const synonyms = this.findSynonyms(token)
      if (synonyms.length > 0) {
        alternatives.push(query.replace(token, synonyms[0]))
      }
    })

    return alternatives
  }

  private extractSuccessfulPatterns(query: string, clickedItems: string[]): any[] {
    const patterns: any[] = []

    if (clickedItems.length > 0) {
      patterns.push({
        type: 'successful_query',
        example: query,
        resultTypes: clickedItems.map(item => this.detectItemType(item))
      })
    }

    return patterns
  }

  private extractIgnoredPatterns(query: string, ignoredItems: string[]): any[] {
    const patterns: any[] = []

    if (ignoredItems.length > 0) {
      patterns.push({
        type: 'ignored_query',
        example: query,
        resultTypes: ignoredItems.map(item => this.detectItemType(item))
      })
    }

    return patterns
  }

  private detectFileType(filePath: string): string | null {
    if (filePath.includes('component') || filePath.includes('.tsx') || filePath.includes('.jsx')) {
      return 'react'
    } else if (filePath.includes('api') || filePath.includes('route')) {
      return 'api'
    } else if (filePath.includes('test') || filePath.includes('spec')) {
      return 'testing'
    } else if (filePath.includes('model') || filePath.includes('schema')) {
      return 'database'
    }

    return null
  }

  private analyzeRecentPatterns(recentFiles: string[]): string[] {
    const patterns: string[] = []

    const fileTypes = recentFiles.map(file => this.detectFileType(file)).filter(Boolean)
    const uniqueTypes = [...new Set(fileTypes)]

    uniqueTypes.forEach(type => {
      if (type) {
        patterns.push(`recent_${type}_activity`)
      }
    })

    return patterns
  }

  private detectItemType(item: string): string {
    if (item.includes('function')) return 'function'
    if (item.includes('class')) return 'class'
    if (item.includes('file')) return 'file'
    return 'unknown'
  }
}
