/**
 * Type-safe environment variables with @t3-oss/env-nextjs
 * Validates env vars at build time and provides IntelliSense
 */

import { createEnv } from "@t3-oss/env-nextjs"
import { z } from "zod"

export const env = createEnv({
  /**
   * Server-side environment variables
   */
  server: {
    NODE_ENV: z.enum(["development", "test", "production"]).default("development"),
    
    // Database
    DATABASE_URL: z.string().url().optional(),
    
    // AI Services
    OPENROUTER_API_KEY: z.string().min(1),
    OPENAI_API_KEY: z.string().optional(),
    ANTHROPIC_API_KEY: z.string().optional(),
    
    // External Services
    UPLOADTHING_SECRET: z.string().optional(),
    UPLOADTHING_APP_ID: z.string().optional(),
    
    // Monitoring & Analytics
    SENTRY_DSN: z.string().url().optional(),
    SENTRY_ORG: z.string().optional(),
    SENTRY_PROJECT: z.string().optional(),
    
    // Redis/Upstash
    UPSTASH_REDIS_REST_URL: z.string().url().optional(),
    UPSTASH_REDIS_REST_TOKEN: z.string().optional(),
    
    // Inngest
    INNGEST_EVENT_KEY: z.string().optional(),
    INNGEST_SIGNING_KEY: z.string().optional(),
    
    // Security
    NEXTAUTH_SECRET: z.string().min(1).optional(),
    NEXTAUTH_URL: z.string().url().optional(),
    
    // Feature Flags
    ENABLE_MCP: z.string().transform(val => val === "true").default("true"),
    ENABLE_SEQUENTIAL_THINKING: z.string().transform(val => val === "true").default("true"),
    ENABLE_CONTEXT7: z.string().transform(val => val === "true").default("true"),
    
    // Rate Limiting
    RATE_LIMIT_REQUESTS_PER_MINUTE: z.string().transform(Number).default("60"),
    RATE_LIMIT_REQUESTS_PER_HOUR: z.string().transform(Number).default("1000"),
  },

  /**
   * Client-side environment variables
   */
  client: {
    NEXT_PUBLIC_APP_URL: z.string().url().optional(),
    NEXT_PUBLIC_POSTHOG_KEY: z.string().optional(),
    NEXT_PUBLIC_POSTHOG_HOST: z.string().url().optional(),
    NEXT_PUBLIC_SENTRY_DSN: z.string().url().optional(),
    NEXT_PUBLIC_UPLOADTHING_APP_ID: z.string().optional(),
    NEXT_PUBLIC_ENABLE_ANALYTICS: z.string().transform(val => val === "true").default("false"),
    NEXT_PUBLIC_ENABLE_ERROR_REPORTING: z.string().transform(val => val === "true").default("false"),
  },

  /**
   * Runtime environment variables
   */
  runtimeEnv: {
    NODE_ENV: process.env.NODE_ENV,
    
    // Server
    DATABASE_URL: process.env.DATABASE_URL,
    OPENROUTER_API_KEY: process.env.OPENROUTER_API_KEY,
    OPENAI_API_KEY: process.env.OPENAI_API_KEY,
    ANTHROPIC_API_KEY: process.env.ANTHROPIC_API_KEY,
    UPLOADTHING_SECRET: process.env.UPLOADTHING_SECRET,
    UPLOADTHING_APP_ID: process.env.UPLOADTHING_APP_ID,
    SENTRY_DSN: process.env.SENTRY_DSN,
    SENTRY_ORG: process.env.SENTRY_ORG,
    SENTRY_PROJECT: process.env.SENTRY_PROJECT,
    UPSTASH_REDIS_REST_URL: process.env.UPSTASH_REDIS_REST_URL,
    UPSTASH_REDIS_REST_TOKEN: process.env.UPSTASH_REDIS_REST_TOKEN,
    INNGEST_EVENT_KEY: process.env.INNGEST_EVENT_KEY,
    INNGEST_SIGNING_KEY: process.env.INNGEST_SIGNING_KEY,
    NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET,
    NEXTAUTH_URL: process.env.NEXTAUTH_URL,
    ENABLE_MCP: process.env.ENABLE_MCP,
    ENABLE_SEQUENTIAL_THINKING: process.env.ENABLE_SEQUENTIAL_THINKING,
    ENABLE_CONTEXT7: process.env.ENABLE_CONTEXT7,
    RATE_LIMIT_REQUESTS_PER_MINUTE: process.env.RATE_LIMIT_REQUESTS_PER_MINUTE,
    RATE_LIMIT_REQUESTS_PER_HOUR: process.env.RATE_LIMIT_REQUESTS_PER_HOUR,
    
    // Client
    NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
    NEXT_PUBLIC_POSTHOG_KEY: process.env.NEXT_PUBLIC_POSTHOG_KEY,
    NEXT_PUBLIC_POSTHOG_HOST: process.env.NEXT_PUBLIC_POSTHOG_HOST,
    NEXT_PUBLIC_SENTRY_DSN: process.env.NEXT_PUBLIC_SENTRY_DSN,
    NEXT_PUBLIC_UPLOADTHING_APP_ID: process.env.NEXT_PUBLIC_UPLOADTHING_APP_ID,
    NEXT_PUBLIC_ENABLE_ANALYTICS: process.env.NEXT_PUBLIC_ENABLE_ANALYTICS,
    NEXT_PUBLIC_ENABLE_ERROR_REPORTING: process.env.NEXT_PUBLIC_ENABLE_ERROR_REPORTING,
  },

  /**
   * Skip validation during build
   */
  skipValidation: !!process.env.SKIP_ENV_VALIDATION,

  /**
   * Makes it so that empty strings are treated as undefined.
   */
  emptyStringAsUndefined: true,
})

/**
 * Configuration derived from environment variables
 */
export const config = {
  app: {
    name: "AG3NT",
    url: env.NEXT_PUBLIC_APP_URL || "http://localhost:3000",
    description: "AI-powered project planning agent",
  },

  ai: {
    defaultModel: "anthropic/claude-sonnet-4",
    temperature: 0.7,
    maxTokens: 4000,
    enableMCP: env.ENABLE_MCP,
    enableSequentialThinking: env.ENABLE_SEQUENTIAL_THINKING,
    enableContext7: env.ENABLE_CONTEXT7,
  },

  rateLimit: {
    requestsPerMinute: env.RATE_LIMIT_REQUESTS_PER_MINUTE,
    requestsPerHour: env.RATE_LIMIT_REQUESTS_PER_HOUR,
  },

  features: {
    analytics: env.NEXT_PUBLIC_ENABLE_ANALYTICS,
    errorReporting: env.NEXT_PUBLIC_ENABLE_ERROR_REPORTING,
    fileUploads: !!env.UPLOADTHING_SECRET,
    redis: !!env.UPSTASH_REDIS_REST_URL,
    inngest: !!env.INNGEST_EVENT_KEY,
  },

  isDev: env.NODE_ENV === "development",
  isProd: env.NODE_ENV === "production",
  isTest: env.NODE_ENV === "test",
} as const

// Temporary simple config for immediate use
export const simpleConfig = {
  ai: {
    defaultModel: "anthropic/claude-sonnet-4",
    temperature: 0.7,
    maxTokens: 4000,
    enableMCP: true,
    enableSequentialThinking: true,
    enableContext7: true,
  }
}
