/**
 * Simple test for Unified Context Engine (without database dependencies)
 * Tests the core functionality without requiring Neo4j or Redis
 */

import { testConfig } from './lib/unified-context-config'

// Mock the database dependencies for testing
const mockNeo4jDriver = {
  driver: () => ({
    session: () => ({
      run: async () => ({ records: [] }),
      close: async () => {},
      beginTransaction: async () => ({}),
      commitTransaction: async () => ({}),
      rollbackTransaction: async () => ({})
    }),
    close: async () => {}
  }),
  auth: {
    basic: () => ({})
  }
}

const mockRedis = class {
  constructor() {}
  async ping() { return 'PONG' }
  async setex() { return 'OK' }
  async quit() { return 'OK' }
}

// Mock the imports
jest.mock('neo4j-driver', () => mockNeo4jDriver)
jest.mock('ioredis', () => mockRedis)

async function testContextEngineCore() {
  console.log('🧪 Testing Unified Context Engine Core Functionality...\n')

  try {
    // Import after mocking
    const { UnifiedContextEngine } = await import('./lib/unified-context-engine')

    // Test 1: Create unified engine with test config
    console.log('1. Creating unified context engine...')
    const engine = new UnifiedContextEngine({
      originalPrompt: "Build a modern React dashboard with real-time data visualization",
      projectType: "Web Application",
      features: ["Dashboard", "Real-time Updates", "Data Visualization", "User Authentication"],
      techStack: { 
        frontend: { framework: "React", language: "TypeScript" },
        backend: { framework: "Node.js", language: "TypeScript" },
        database: "PostgreSQL"
      }
    }, testConfig)

    // Simulate initialization event
    setTimeout(() => engine.emit('initialized'), 100)

    // Wait for initialization
    await new Promise((resolve, reject) => {
      engine.once('initialized', resolve)
      engine.once('error', reject)
      setTimeout(() => reject(new Error('Initialization timeout')), 5000)
    })
    
    console.log('✅ Unified context engine created successfully')

    // Test 2: Register agents
    console.log('\n2. Registering agents...')
    const plannerAgentId = engine.registerAgent('project-planner', 'test-planning', {
      requiredCapabilities: ['requirements_analysis', 'tech_stack_selection'],
      contextFilters: ['all']
    })
    
    const taskAgentId = engine.registerAgent('task-breakdown', 'test-tasks', {
      requiredCapabilities: ['task_breakdown', 'dependency_analysis'],
      contextFilters: ['requirements', 'architecture']
    })
    
    console.log(`✅ Registered planner agent: ${plannerAgentId}`)
    console.log(`✅ Registered task agent: ${taskAgentId}`)

    // Test 3: Update context
    console.log('\n3. Updating context...')
    engine.updateContext('analyze', {
      projectType: 'Web Application',
      complexity: 'Medium',
      domain: 'Data Visualization',
      requirements: ['Real-time updates', 'Interactive charts', 'User management']
    })
    
    engine.updateContext('techstack', {
      frontend: { framework: 'React', version: '18.x', language: 'TypeScript' },
      backend: { framework: 'Express', version: '4.x', language: 'TypeScript' },
      database: { type: 'PostgreSQL', version: '15.x' },
      realtime: { technology: 'WebSocket', library: 'Socket.io' }
    })
    
    console.log('✅ Context updated successfully')

    // Test 4: Get agent context
    console.log('\n4. Getting agent context...')
    const plannerContext = await engine.getAgentContext(plannerAgentId)
    const taskContext = await engine.getAgentContext(taskAgentId)
    
    console.log('✅ Planner context retrieved:', {
      hasCurrentContext: !!plannerContext.current,
      hasDependencies: !!plannerContext.dependencies,
      hasMetadata: !!plannerContext.metadata,
      agentType: plannerContext.agentMetadata?.agentType
    })
    
    console.log('✅ Task context retrieved:', {
      hasCurrentContext: !!taskContext.current,
      hasDependencies: !!taskContext.dependencies,
      hasMetadata: !!taskContext.metadata,
      agentType: taskContext.agentMetadata?.agentType
    })

    // Test 5: Enhanced context with RAG
    console.log('\n5. Testing enhanced context with RAG...')
    const enhancedContext = await engine.enhanceWithRAG('techstack', 'React dashboard with TypeScript')
    
    console.log('✅ Enhanced context retrieved:', {
      hasBaseContext: !!enhancedContext.context,
      hasEnrichments: !!enhancedContext.enrichments,
      hasExternalKnowledge: !!enhancedContext.externalKnowledge,
      hasBestPractices: !!enhancedContext.bestPractices,
      hasTemplates: !!enhancedContext.templates
    })

    // Test 6: Sequential thinking
    console.log('\n6. Testing sequential thinking...')
    const thought = await engine.performSequentialThinking(
      'How should we structure the React dashboard for optimal performance?',
      { thoughtNumber: 1, totalThoughts: 3 }
    )
    
    console.log('✅ Sequential thinking result:', {
      hasThought: !!thought,
      thoughtNumber: thought?.thoughtNumber,
      nextThoughtNeeded: thought?.nextThoughtNeeded
    })

    // Test 7: Documentation retrieval
    console.log('\n7. Testing documentation retrieval...')
    const reactDocs = await engine.getDocumentation('React', 'hooks')
    
    console.log('✅ Documentation retrieved:', {
      hasDocumentation: !!reactDocs,
      libraryId: reactDocs?.libraryId,
      topic: reactDocs?.topic
    })

    // Test 8: Context validation
    console.log('\n8. Testing context validation...')
    const validation = engine.validateContext()
    
    console.log('✅ Context validation:', {
      isValid: validation.isValid,
      score: validation.score,
      issueCount: validation.issues.length,
      recommendationCount: validation.recommendations.length
    })

    // Test 9: Get relevant context
    console.log('\n9. Testing relevant context retrieval...')
    const relevantContext = engine.getRelevantContext('techstack')
    
    console.log('✅ Relevant context retrieved:', {
      hasCurrent: !!relevantContext.current,
      hasDependencies: !!relevantContext.dependencies,
      hasMetadata: !!relevantContext.metadata
    })

    // Test 10: Cleanup
    console.log('\n10. Testing cleanup...')
    await engine.cleanup()
    console.log('✅ Engine cleanup completed')

    console.log('\n🎉 All core tests completed successfully!')
    console.log('\n📊 Test Summary:')
    console.log('- ✅ Engine initialization')
    console.log('- ✅ Agent registration')
    console.log('- ✅ Context updates')
    console.log('- ✅ Agent context retrieval')
    console.log('- ✅ Enhanced context with RAG')
    console.log('- ✅ Sequential thinking')
    console.log('- ✅ Documentation retrieval')
    console.log('- ✅ Context validation')
    console.log('- ✅ Relevant context retrieval')
    console.log('- ✅ Cleanup')

    return true

  } catch (error) {
    console.error('❌ Test failed:', error.message)
    console.error(error.stack)
    return false
  }
}

// Test configuration validation
function testConfigValidation() {
  console.log('\n🔧 Testing configuration validation...')
  
  try {
    const { validateConfig, getConfig } = require('./lib/unified-context-config')
    
    // Test valid config
    const config = getConfig()
    const validation = validateConfig(config)
    
    console.log('✅ Configuration validation:', {
      isValid: validation.isValid,
      errorCount: validation.errors.length
    })
    
    // Test invalid config
    const invalidConfig = {
      neo4j: { uri: '', user: '', password: '' },
      redis: { host: '', port: 0 },
      processing: { maxConcurrent: 0, batchSize: 0, enableRealTimeUpdates: true },
      mcp: { enableContext7: true, enableSequentialThinking: true, timeout: 0 }
    }
    
    const invalidValidation = validateConfig(invalidConfig)
    console.log('✅ Invalid configuration detected:', {
      isValid: invalidValidation.isValid,
      errorCount: invalidValidation.errors.length
    })
    
    return true
  } catch (error) {
    console.error('❌ Configuration test failed:', error.message)
    return false
  }
}

// Run tests
async function runAllTests() {
  console.log('🚀 Starting Unified Context Engine Tests\n')
  
  const configTest = testConfigValidation()
  const coreTest = await testContextEngineCore()
  
  if (configTest && coreTest) {
    console.log('\n🏁 All tests passed successfully!')
    console.log('\n✨ The Unified Context Engine is ready for integration!')
  } else {
    console.log('\n❌ Some tests failed. Please check the errors above.')
    process.exit(1)
  }
}

// Execute if run directly
if (require.main === module) {
  runAllTests().catch(console.error)
}

export { testContextEngineCore, testConfigValidation, runAllTests }
