/**
 * Context Integrity Tracker
 * Validates context consistency and tracks integrity across the system
 */

import type { ProjectContext } from "@/types/planning"
import type {
  ContextNode,
  ContextIntegrityReport,
  ContextIntegrityIssue
} from "@/types/context-engine"
import crypto from 'crypto'

export class ContextIntegrityTracker {
  private integrityHashes: Map<string, string> = new Map()
  private validationRules: Map<string, ValidationRule[]> = new Map()
  private issueHistory: ContextIntegrityIssue[] = []

  constructor() {
    this.initializeValidationRules()
  }

  /**
   * Initialize validation rules for different context types
   */
  private initializeValidationRules(): void {
    // Project context validation rules
    this.validationRules.set('project', [
      {
        field: 'originalPrompt',
        required: true,
        validator: (value: any) => typeof value === 'string' && value.length > 0,
        message: 'Original prompt is required and must be non-empty'
      },
      {
        field: 'projectType',
        required: true,
        validator: (value: any) => typeof value === 'string' && value.length > 0,
        message: 'Project type is required'
      },
      {
        field: 'features',
        required: true,
        validator: (value: any) => Array.isArray(value),
        message: 'Features must be an array'
      }
    ])

    // Tech stack validation rules
    this.validationRules.set('techstack', [
      {
        field: 'frontend',
        required: false,
        validator: (value: any) => !value || typeof value === 'object',
        message: 'Frontend tech stack must be an object'
      },
      {
        field: 'backend',
        required: false,
        validator: (value: any) => !value || typeof value === 'object',
        message: 'Backend tech stack must be an object'
      }
    ])

    // Task validation rules
    this.validationRules.set('tasks', [
      {
        field: 'tasks',
        required: true,
        validator: (value: any) => Array.isArray(value),
        message: 'Tasks must be an array'
      }
    ])
  }

  /**
   * Validate entire context
   */
  validateContext(context: ProjectContext, decisionGraph: Map<string, ContextNode>): ContextIntegrityReport {
    const issues: ContextIntegrityIssue[] = []
    let score = 100

    // Validate project context
    const projectIssues = this.validateProjectContext(context)
    issues.push(...projectIssues)

    // Validate decision graph consistency
    const graphIssues = this.validateDecisionGraph(decisionGraph)
    issues.push(...graphIssues)

    // Validate cross-references
    const crossRefIssues = this.validateCrossReferences(context, decisionGraph)
    issues.push(...crossRefIssues)

    // Calculate score based on issues
    score -= issues.length * 10
    score = Math.max(0, score)

    // Store issues in history
    this.issueHistory.push(...issues)

    return {
      isValid: issues.length === 0,
      issues,
      score,
      recommendations: this.generateRecommendations(issues)
    }
  }

  /**
   * Validate project context against rules
   */
  private validateProjectContext(context: ProjectContext): ContextIntegrityIssue[] {
    const issues: ContextIntegrityIssue[] = []
    const rules = this.validationRules.get('project') || []

    for (const rule of rules) {
      const value = (context as any)[rule.field]
      
      if (rule.required && (value === undefined || value === null)) {
        issues.push({
          type: 'missing_dependency',
          step: 'project',
          field: rule.field,
          severity: 'high',
          message: `Required field ${rule.field} is missing`,
          suggestedFix: `Provide a value for ${rule.field}`
        })
      } else if (value !== undefined && !rule.validator(value)) {
        issues.push({
          type: 'validation_failure',
          step: 'project',
          field: rule.field,
          severity: 'medium',
          message: rule.message,
          suggestedFix: `Fix the format of ${rule.field}`
        })
      }
    }

    return issues
  }

  /**
   * Validate decision graph consistency
   */
  private validateDecisionGraph(decisionGraph: Map<string, ContextNode>): ContextIntegrityIssue[] {
    const issues: ContextIntegrityIssue[] = []

    // Check for circular dependencies
    for (const [nodeId, node] of decisionGraph) {
      if (this.hasCircularDependency(nodeId, node, decisionGraph)) {
        issues.push({
          type: 'circular_reference',
          step: nodeId,
          severity: 'critical',
          message: `Circular dependency detected in step ${nodeId}`,
          suggestedFix: 'Remove circular dependency by reordering steps'
        })
      }
    }

    // Check for missing dependencies
    for (const [nodeId, node] of decisionGraph) {
      for (const dep of node.dependencies) {
        if (!decisionGraph.has(dep)) {
          issues.push({
            type: 'missing_dependency',
            step: nodeId,
            severity: 'high',
            message: `Missing dependency ${dep} for step ${nodeId}`,
            suggestedFix: `Add step ${dep} to the decision graph`
          })
        }
      }
    }

    return issues
  }

  /**
   * Validate cross-references between context elements
   */
  private validateCrossReferences(context: ProjectContext, decisionGraph: Map<string, ContextNode>): ContextIntegrityIssue[] {
    const issues: ContextIntegrityIssue[] = []

    // Check tech stack consistency
    if (context.techStack && context.features) {
      const techStackIssues = this.validateTechStackConsistency(context.techStack, context.features)
      issues.push(...techStackIssues)
    }

    // Check task dependencies
    if (context.tasks && Array.isArray(context.tasks)) {
      const taskIssues = this.validateTaskDependencies(context.tasks)
      issues.push(...taskIssues)
    }

    return issues
  }

  /**
   * Check for circular dependencies in decision graph
   */
  private hasCircularDependency(
    nodeId: string, 
    node: ContextNode, 
    graph: Map<string, ContextNode>, 
    visited: Set<string> = new Set()
  ): boolean {
    if (visited.has(nodeId)) {
      return true
    }

    visited.add(nodeId)

    for (const dep of node.dependencies) {
      const depNode = graph.get(dep)
      if (depNode && this.hasCircularDependency(dep, depNode, graph, new Set(visited))) {
        return true
      }
    }

    return false
  }

  /**
   * Validate tech stack consistency with features
   */
  private validateTechStackConsistency(techStack: any, features: string[]): ContextIntegrityIssue[] {
    const issues: ContextIntegrityIssue[] = []

    // Check if frontend tech is specified when UI features are present
    const hasUIFeatures = features.some(f => 
      f.toLowerCase().includes('dashboard') || 
      f.toLowerCase().includes('ui') || 
      f.toLowerCase().includes('interface')
    )

    if (hasUIFeatures && !techStack.frontend) {
      issues.push({
        type: 'data_mismatch',
        step: 'techstack',
        field: 'frontend',
        severity: 'medium',
        message: 'UI features detected but no frontend technology specified',
        suggestedFix: 'Add frontend technology to tech stack'
      })
    }

    return issues
  }

  /**
   * Validate task dependencies
   */
  private validateTaskDependencies(tasks: any[]): ContextIntegrityIssue[] {
    const issues: ContextIntegrityIssue[] = []

    for (const task of tasks) {
      if (task.dependencies) {
        for (const dep of task.dependencies) {
          const depExists = tasks.some(t => t.id === dep)
          if (!depExists) {
            issues.push({
              type: 'missing_dependency',
              step: 'tasks',
              field: 'dependencies',
              severity: 'medium',
              message: `Task ${task.id} depends on non-existent task ${dep}`,
              suggestedFix: `Add task ${dep} or remove dependency`
            })
          }
        }
      }
    }

    return issues
  }

  /**
   * Generate recommendations based on issues
   */
  private generateRecommendations(issues: ContextIntegrityIssue[]): string[] {
    const recommendations: string[] = []

    const criticalIssues = issues.filter(i => i.severity === 'critical')
    const highIssues = issues.filter(i => i.severity === 'high')

    if (criticalIssues.length > 0) {
      recommendations.push('Address critical issues immediately to prevent system failures')
    }

    if (highIssues.length > 0) {
      recommendations.push('Resolve high-priority issues to maintain system integrity')
    }

    if (issues.length > 10) {
      recommendations.push('Consider reviewing the entire context structure for systematic issues')
    }

    return recommendations
  }

  /**
   * Generate hash for context data
   */
  generateHash(data: any): string {
    const serialized = JSON.stringify(data, Object.keys(data).sort())
    return crypto.createHash('sha256').update(serialized).digest('hex')
  }

  /**
   * Get issue history
   */
  getIssueHistory(): ContextIntegrityIssue[] {
    return [...this.issueHistory]
  }

  /**
   * Clear issue history
   */
  clearIssueHistory(): void {
    this.issueHistory = []
  }
}

interface ValidationRule {
  field: string
  required: boolean
  validator: (value: any) => boolean
  message: string
}
