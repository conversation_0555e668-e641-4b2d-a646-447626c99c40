/**
 * Unit tests for Symbol Table Builder
 */

import { jest } from '@jest/globals';

// Mock the logger
jest.unstable_mockModule('../../../../src/utils/logger.js', () => ({
  createLogger: jest.fn(() => ({
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  }))
}));

describe('SymbolTableBuilder', () => {
  let SymbolTableBuilder;
  let builder;

  beforeAll(async () => {
    const module = await import('../../../../src/core/processors/SymbolTableBuilder.js');
    SymbolTableBuilder = module.SymbolTableBuilder;
  });

  beforeEach(() => {
    builder = new SymbolTableBuilder({
      supportedLanguages: ['javascript', 'typescript', 'python']
    });
  });

  describe('initialization', () => {
    test('should initialize successfully', async () => {
      await expect(builder.initialize()).resolves.not.toThrow();
      expect(builder.isInitialized).toBe(true);
    });
  });

  describe('symbol table building', () => {
    test('should build symbol table from AST with functions', async () => {
      await builder.initialize();
      
      const ast = {
        language: 'javascript',
        nodes: [
          {
            nodeType: 'function',
            name: 'testFunction',
            signature: 'testFunction(a, b)',
            parameters: [
              { name: 'a', type: 'unknown' },
              { name: 'b', type: 'unknown' }
            ],
            startLine: 1,
            endLine: 5,
            startColumn: 0,
            endColumn: 10
          }
        ],
        imports: [],
        exports: []
      };

      const file = global.testUtils.createMockFile();
      const context = { file, ast };

      const result = await builder.process(context);
      
      expect(result.symbols).toBeDefined();
      expect(result.symbols.symbols).toHaveLength(3); // function + 2 parameters
      
      const functionSymbol = result.symbols.symbols.find(s => s.type === 'function');
      expect(functionSymbol).toBeDefined();
      expect(functionSymbol.name).toBe('testFunction');
      expect(functionSymbol.signature).toBe('testFunction(a, b)');
      
      const parameterSymbols = result.symbols.symbols.filter(s => s.type === 'parameter');
      expect(parameterSymbols).toHaveLength(2);
      expect(parameterSymbols.map(p => p.name)).toContain('a');
      expect(parameterSymbols.map(p => p.name)).toContain('b');
    });

    test('should build symbol table from AST with classes', async () => {
      await builder.initialize();
      
      const ast = {
        language: 'javascript',
        nodes: [
          {
            nodeType: 'class',
            name: 'TestClass',
            superclass: 'BaseClass',
            methods: [
              { name: 'constructor', signature: 'constructor()', startLine: 2, endLine: 4 },
              { name: 'testMethod', signature: 'testMethod()', startLine: 5, endLine: 7 }
            ],
            properties: [],
            startLine: 1,
            endLine: 8,
            startColumn: 0,
            endColumn: 10
          }
        ],
        imports: [],
        exports: []
      };

      const file = global.testUtils.createMockFile();
      const context = { file, ast };

      const result = await builder.process(context);
      
      expect(result.symbols).toBeDefined();
      expect(result.symbols.symbols).toHaveLength(3); // class + 2 methods
      
      const classSymbol = result.symbols.symbols.find(s => s.type === 'class');
      expect(classSymbol).toBeDefined();
      expect(classSymbol.name).toBe('TestClass');
      expect(classSymbol.superclass).toBe('BaseClass');
      
      const methodSymbols = result.symbols.symbols.filter(s => s.type === 'method');
      expect(methodSymbols).toHaveLength(2);
      expect(methodSymbols.map(m => m.name)).toContain('constructor');
      expect(methodSymbols.map(m => m.name)).toContain('testMethod');
    });

    test('should build symbol table from AST with variables', async () => {
      await builder.initialize();
      
      const ast = {
        language: 'javascript',
        nodes: [
          {
            nodeType: 'variable',
            name: 'testVar',
            type: 'string',
            hasInitializer: true,
            startLine: 1,
            endLine: 1,
            startColumn: 0,
            endColumn: 20
          }
        ],
        imports: [],
        exports: []
      };

      const file = global.testUtils.createMockFile();
      const context = { file, ast };

      const result = await builder.process(context);
      
      expect(result.symbols).toBeDefined();
      expect(result.symbols.symbols).toHaveLength(1);
      
      const variableSymbol = result.symbols.symbols[0];
      expect(variableSymbol.type).toBe('variable');
      expect(variableSymbol.name).toBe('testVar');
      expect(variableSymbol.dataType).toBe('string');
      expect(variableSymbol.metadata.hasInitializer).toBe(true);
    });

    test('should process imports and exports', async () => {
      await builder.initialize();
      
      const ast = {
        language: 'javascript',
        nodes: [],
        imports: [
          {
            source: 'react',
            specifiers: ['React'],
            isDefault: true
          },
          {
            source: 'lodash',
            specifiers: ['map', 'filter'],
            isDefault: false
          }
        ],
        exports: [
          {
            name: 'TestComponent',
            isDefault: false,
            specifiers: []
          }
        ]
      };

      const file = global.testUtils.createMockFile();
      const context = { file, ast };

      const result = await builder.process(context);
      
      expect(result.symbols).toBeDefined();
      expect(result.symbols.imports).toHaveLength(2);
      expect(result.symbols.exports).toHaveLength(1);
      
      const reactImport = result.symbols.imports.find(i => i.source === 'react');
      expect(reactImport).toBeDefined();
      expect(reactImport.isDefault).toBe(true);
      
      const lodashImport = result.symbols.imports.find(i => i.source === 'lodash');
      expect(lodashImport).toBeDefined();
      expect(lodashImport.specifiers).toContain('map');
      expect(lodashImport.specifiers).toContain('filter');
      
      const testExport = result.symbols.exports[0];
      expect(testExport.name).toBe('TestComponent');
      expect(testExport.isDefault).toBe(false);
    });
  });

  describe('scope management', () => {
    test('should create scopes for functions', async () => {
      await builder.initialize();
      
      const ast = {
        language: 'javascript',
        nodes: [
          {
            nodeType: 'function',
            name: 'outerFunction',
            parameters: [],
            startLine: 1,
            endLine: 10,
            startColumn: 0,
            endColumn: 10
          }
        ],
        imports: [],
        exports: []
      };

      const file = global.testUtils.createMockFile();
      const context = { file, ast };

      const result = await builder.process(context);
      
      expect(result.symbols.scopes).toHaveLength(1);
      
      const functionScope = result.symbols.scopes[0];
      expect(functionScope.type).toBe('function');
      expect(functionScope.name).toBe('outerFunction');
      expect(functionScope.startLine).toBe(1);
      expect(functionScope.endLine).toBe(10);
    });

    test('should create nested scopes', async () => {
      await builder.initialize();
      
      const ast = {
        language: 'javascript',
        nodes: [
          {
            nodeType: 'function',
            name: 'outerFunction',
            parameters: [],
            startLine: 1,
            endLine: 10,
            startColumn: 0,
            endColumn: 10
          },
          {
            nodeType: 'function',
            name: 'innerFunction',
            parameters: [],
            startLine: 3,
            endLine: 7,
            startColumn: 2,
            endColumn: 8
          }
        ],
        imports: [],
        exports: []
      };

      const file = global.testUtils.createMockFile();
      const context = { file, ast };

      const result = await builder.process(context);
      
      expect(result.symbols.scopes).toHaveLength(2);
      
      // After hierarchy building, inner scope should have outer as parent
      const outerScope = result.symbols.scopes.find(s => s.name === 'outerFunction');
      const innerScope = result.symbols.scopes.find(s => s.name === 'innerFunction');
      
      expect(outerScope).toBeDefined();
      expect(innerScope).toBeDefined();
      expect(innerScope.parent).toBe(outerScope.id);
      expect(outerScope.children).toContain(innerScope.id);
    });
  });

  describe('error handling', () => {
    test('should handle missing AST gracefully', async () => {
      await builder.initialize();
      
      const file = global.testUtils.createMockFile();
      const context = { file, ast: null };

      const result = await builder.process(context);
      
      expect(result.symbols).toBeNull();
    });

    test('should handle empty AST', async () => {
      await builder.initialize();
      
      const ast = {
        language: 'javascript',
        nodes: [],
        imports: [],
        exports: []
      };

      const file = global.testUtils.createMockFile();
      const context = { file, ast };

      const result = await builder.process(context);
      
      expect(result.symbols).toBeDefined();
      expect(result.symbols.symbols).toHaveLength(0);
      expect(result.symbols.scopes).toHaveLength(0);
    });
  });

  describe('utility functions', () => {
    test('should generate unique IDs', async () => {
      await builder.initialize();
      
      const id1 = builder.generateId('function', 'test', 1);
      const id2 = builder.generateId('function', 'test', 1);
      
      expect(id1).not.toBe(id2);
      expect(id1).toContain('function_test_1');
      expect(id2).toContain('function_test_1');
    });

    test('should infer visibility correctly', async () => {
      await builder.initialize();
      
      const publicNode = { name: 'publicMethod' };
      const protectedNode = { name: '_protectedMethod' };
      const privateNode = { name: '__privateMethod' };
      
      expect(builder.inferVisibility(publicNode)).toBe('public');
      expect(builder.inferVisibility(protectedNode)).toBe('protected');
      expect(builder.inferVisibility(privateNode)).toBe('private');
    });

    test('should calculate complexity correctly', async () => {
      await builder.initialize();
      
      const simpleNode = { startLine: 1, endLine: 5, text: 'short code' };
      const mediumNode = { startLine: 1, endLine: 25, text: 'medium code' };
      const complexNode = { startLine: 1, endLine: 100, text: 'long code' };
      
      expect(builder.calculateComplexity(simpleNode)).toBe('low');
      expect(builder.calculateComplexity(mediumNode)).toBe('medium');
      expect(builder.calculateComplexity(complexNode)).toBe('high');
    });
  });
});
