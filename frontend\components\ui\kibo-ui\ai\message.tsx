import type { ComponentProps, HTMLAttributes } from 'react';
import { cn } from '@/lib/utils';

export type AIMessageProps = HTMLAttributes<HTMLDivElement> & {
  from: 'user' | 'assistant';
};

export const AIMessage = ({ className, from, ...props }: AIMessageProps) => (
  <div
    className={cn(
      'group flex gap-3 p-4',
      from === 'user' && 'flex-row-reverse',
      '[&>div]:max-w-[80%]',
      className
    )}
    {...props}
  />
);

export type AIMessageContentProps = HTMLAttributes<HTMLDivElement>;

export const AIMessageContent = ({
  children,
  className,
  ...props
}: AIMessageContentProps) => (
  <div
    className={cn(
      'flex-1 space-y-3 overflow-hidden rounded-2xl bg-muted p-4 shadow-sm',
      className
    )}
    {...props}
  >
    {children}
  </div>
);

export type AIMessageAvatarProps = {
  src: string;
  name?: string;
  className?: string;
};

export const AIMessageAvatar = ({
  src,
  name,
  className,
  ...props
}: AIMessageAvatarProps) => (
  <div
    className={cn(
      'flex h-10 w-10 shrink-0 select-none items-center justify-center rounded-xl border bg-background text-xs font-semibold shadow-lg',
      className
    )}
    {...props}
  >
    {src ? (
      <img
        src={src}
        alt={name || 'Avatar'}
        className="h-full w-full rounded-xl object-cover"
      />
    ) : (
      <span className="text-white">{name?.slice(0, 2) || 'ME'}</span>
    )}
  </div>
);
