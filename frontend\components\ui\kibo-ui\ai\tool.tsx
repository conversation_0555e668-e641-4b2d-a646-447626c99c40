'use client';

import {
  CheckCircleIcon,
  ChevronDownIcon,
  CircleIcon,
  ClockIcon,
  WrenchIcon,
  XCircleIcon,
} from 'lucide-react';
import type { ComponentProps, ReactNode } from 'react';
import { Badge } from '@/components/ui/badge';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { cn } from '@/lib/utils';

export type AIToolStatus = 'pending' | 'running' | 'completed' | 'error';

export type AIToolProps = ComponentProps<typeof Collapsible> & {
  status?: AIToolStatus;
};

export const AITool = ({
  className,
  status = 'pending',
  ...props
}: AIToolProps) => (
  <Collapsible
    className={cn('space-y-2', className)}
    data-status={status}
    {...props}
  />
);

export type AIToolHeaderProps = ComponentProps<typeof CollapsibleTrigger> & {
  status?: AIToolStatus;
  name: string;
  description?: string;
};

const getStatusBadge = (status: AIToolStatus) => {
  const labels = {
    pending: 'Pending',
    running: 'Running',
    completed: 'Completed',
    error: 'Error',
  } as const;

  const icons = {
    pending: <CircleIcon className="h-3 w-3" />,
    running: <ClockIcon className="h-3 w-3 animate-spin" />,
    completed: <CheckCircleIcon className="h-3 w-3" />,
    error: <XCircleIcon className="h-3 w-3" />,
  } as const;

  return (
    <Badge
      variant={status === 'error' ? 'destructive' : 'secondary'}
      className="gap-1"
    >
      {icons[status]}
      {labels[status]}
    </Badge>
  );
};

export const AIToolHeader = ({
  className,
  status = 'pending',
  name,
  description,
  ...props
}: AIToolHeaderProps) => (
  <CollapsibleTrigger
    className={cn(
      'flex w-full items-center justify-between rounded-lg border p-3 text-left transition-colors hover:bg-muted/50',
      className
    )}
    {...props}
  >
    <div className="flex items-center gap-3">
      <WrenchIcon className="h-4 w-4 text-muted-foreground" />
      <div className="flex flex-col gap-1">
        <span className="font-medium">{name}</span>
        {description && (
          <span className="text-sm text-muted-foreground">{description}</span>
        )}
      </div>
    </div>
    <div className="flex items-center gap-2">
      {getStatusBadge(status)}
      <ChevronDownIcon className="h-4 w-4 transition-transform data-[state=open]:rotate-180" />
    </div>
  </CollapsibleTrigger>
);

export type AIToolContentProps = ComponentProps<typeof CollapsibleContent>;

export const AIToolContent = ({ className, ...props }: AIToolContentProps) => (
  <CollapsibleContent
    className={cn('overflow-hidden text-sm', className)}
    {...props}
  />
);

export type AIToolParametersProps = ComponentProps<'div'> & {
  parameters: Record<string, any>;
};

export const AIToolParameters = ({
  className,
  parameters,
  ...props
}: AIToolParametersProps) => (
  <div className={cn('space-y-2', className)} {...props}>
    <div className="rounded-lg border bg-muted/50 p-3">
      <h4 className="mb-2 font-medium">Parameters</h4>
      <pre className="whitespace-pre-wrap font-mono text-xs">
        {JSON.stringify(parameters, null, 2)}
      </pre>
    </div>
  </div>
);

export type AIToolResultProps = ComponentProps<'div'> & {
  result?: ReactNode;
  error?: string;
};

export const AIToolResult = ({
  className,
  result,
  error,
  ...props
}: AIToolResultProps) => {
  if (!(result || error)) {
    return null;
  }

  return (
    <div className={cn('space-y-2', className)} {...props}>
      <div className="rounded-lg border bg-muted/50 p-3">
        <h4 className="mb-2 font-medium">
          {error ? 'Error' : 'Result'}
        </h4>
        {error ? <div className="text-destructive">{error}</div> : <div>{result}</div>}
      </div>
    </div>
  );
};
