/**
 * Planning Workflow Functions
 * Temporal-style workflow orchestration with Inngest
 */

import { inngest, sendPlanningEvent, PLANNING_STEPS, getNextSteps, canRunInParallel, type WorkflowState } from "../client"
import { PlanningService } from "@/src/modules/planning/planning.service"
import { config } from "@/src/env"

/**
 * Main Planning Workflow Orchestrator
 * Handles the entire planning session lifecycle
 */
export const planningWorkflow = inngest.createFunction(
  {
    id: "planning-workflow",
    name: "Planning Workflow Orchestrator",
    retries: 0, // We handle retries at the step level
  },
  { event: "planning/session.started" },
  async ({ event, step, logger }) => {
    const { sessionId, prompt, userId, isInteractive } = event.data
    
    logger.info("Starting planning workflow", { sessionId, prompt })

    // Initialize workflow state
    const workflowState: WorkflowState = {
      sessionId,
      status: "running",
      completedSteps: [],
      results: {},
      errors: [],
      startedAt: new Date().toISOString(),
      metadata: {
        prompt,
        isInteractive,
        userId,
        mcpEnabled: config.ai.enableMCP,
        totalSteps: Object.keys(PLANNING_STEPS).length
      }
    }

    try {
      // Execute planning steps in dependency order
      let remainingSteps = Object.keys(PLANNING_STEPS)
      
      while (remainingSteps.length > 0) {
        const nextSteps = getNextSteps(workflowState.completedSteps)
        
        if (nextSteps.length === 0) {
          throw new Error("No more steps can be executed - possible circular dependency")
        }

        // Group steps that can run in parallel
        const parallelGroups = canRunInParallel(nextSteps)
        
        for (const group of parallelGroups) {
          if (group.length === 1) {
            // Execute single step
            const stepName = group[0]
            const stepResult = await step.run(`execute-${stepName}`, async () => {
              return await executeStep(sessionId, stepName, workflowState.results, logger)
            })
            
            if (stepResult.success) {
              workflowState.completedSteps.push(stepName)
              workflowState.results = { ...workflowState.results, ...stepResult.results }
              remainingSteps = remainingSteps.filter(s => s !== stepName)
            } else {
              workflowState.errors.push({
                step: stepName,
                error: stepResult.error || "Unknown error",
                timestamp: new Date().toISOString()
              })
              throw new Error(`Step ${stepName} failed: ${stepResult.error}`)
            }
          } else {
            // Execute parallel steps
            const parallelResults = await step.run(`execute-parallel-${group.join("-")}`, async () => {
              const promises = group.map(stepName => 
                executeStep(sessionId, stepName, workflowState.results, logger)
              )
              return await Promise.allSettled(promises)
            })
            
            // Process parallel results
            for (let i = 0; i < group.length; i++) {
              const stepName = group[i]
              const result = parallelResults[i]
              
              if (result.status === "fulfilled" && result.value.success) {
                workflowState.completedSteps.push(stepName)
                workflowState.results = { ...workflowState.results, ...result.value.results }
                remainingSteps = remainingSteps.filter(s => s !== stepName)
              } else {
                const error = result.status === "rejected" 
                  ? result.reason 
                  : result.value.error || "Unknown error"
                
                workflowState.errors.push({
                  step: stepName,
                  error: error.toString(),
                  timestamp: new Date().toISOString()
                })
                throw new Error(`Parallel step ${stepName} failed: ${error}`)
              }
            }
          }
        }
      }

      // Workflow completed successfully
      workflowState.status = "completed"
      workflowState.completedAt = new Date().toISOString()
      
      await sendPlanningEvent("planning/session.completed", {
        sessionId,
        results: workflowState.results,
        totalDuration: Date.now() - new Date(workflowState.startedAt).getTime(),
        stepsCompleted: workflowState.completedSteps.length,
        timestamp: new Date().toISOString()
      })

      logger.info("Planning workflow completed successfully", { 
        sessionId, 
        stepsCompleted: workflowState.completedSteps.length 
      })

      return workflowState

    } catch (error) {
      // Workflow failed
      workflowState.status = "failed"
      workflowState.completedAt = new Date().toISOString()
      
      const errorMessage = error instanceof Error ? error.message : "Unknown error"
      
      await sendPlanningEvent("planning/session.failed", {
        sessionId,
        error: errorMessage,
        stepsCompleted: workflowState.completedSteps.length,
        timestamp: new Date().toISOString()
      })

      logger.error("Planning workflow failed", { sessionId, error: errorMessage })
      
      throw error
    }
  }
)

/**
 * Individual Step Executor
 * Handles single step execution with retries and error handling
 */
export const stepExecutor = inngest.createFunction(
  {
    id: "step-executor",
    name: "Planning Step Executor",
  },
  { event: "planning/step.requested" },
  async ({ event, step, logger }) => {
    const { sessionId, step: stepName, context, answer } = event.data
    const stepConfig = PLANNING_STEPS[stepName]
    
    if (!stepConfig) {
      throw new Error(`Unknown step: ${stepName}`)
    }

    logger.info("Executing planning step", { sessionId, stepName })

    const result = await step.run(
      `execute-step-${stepName}`,
      async () => {
        return await executeStep(sessionId, stepName, context, logger, answer)
      },
      {
        timeout: stepConfig.timeout,
        retries: stepConfig.maxRetries,
      }
    )

    if (result.success) {
      await sendPlanningEvent("planning/step.completed", {
        sessionId,
        step: stepName,
        results: result.results || {},
        duration: result.duration || 0,
        mcpEnhanced: result.mcpEnhanced || false,
        timestamp: new Date().toISOString()
      })
    } else {
      await sendPlanningEvent("planning/step.failed", {
        sessionId,
        step: stepName,
        error: result.error || "Unknown error",
        attempt: 1, // TODO: Track actual attempt number
        timestamp: new Date().toISOString()
      })
    }

    return result
  }
)

/**
 * Helper function to execute a single step
 */
async function executeStep(
  sessionId: string,
  stepName: string,
  context: Record<string, any>,
  logger: any,
  answer?: string
) {
  const startTime = Date.now()
  
  try {
    const planningService = new PlanningService({
      maxRetries: 1, // Inngest handles retries
      timeoutMs: PLANNING_STEPS[stepName].timeout,
      enableMCP: config.ai.enableMCP,
      enableSequentialThinking: config.ai.enableSequentialThinking,
      enableContext7: config.ai.enableContext7,
      model: config.ai.defaultModel,
      temperature: config.ai.temperature
    })

    const result = await planningService.executeStep({
      step: stepName as any,
      context,
      answer
    })

    const duration = Date.now() - startTime
    
    logger.info("Step executed successfully", { 
      sessionId, 
      stepName, 
      duration,
      mcpEnhanced: result.results?._mcpEnhanced 
    })

    return {
      success: result.success,
      results: result.results,
      duration,
      mcpEnhanced: result.results?._mcpEnhanced || false
    }

  } catch (error) {
    const duration = Date.now() - startTime
    const errorMessage = error instanceof Error ? error.message : "Unknown error"
    
    logger.error("Step execution failed", { 
      sessionId, 
      stepName, 
      error: errorMessage, 
      duration 
    })

    return {
      success: false,
      error: errorMessage,
      duration
    }
  }
}
