/**
 * Auto-Resizing Textarea with Custom Scrollbar
 * Dynamically adjusts height and includes custom styled scrollbar
 */

import * as React from "react"
import { cn } from "@/lib/utils"

interface AutoResizeTextareaProps extends Omit<React.ComponentProps<"textarea">, "rows"> {
  minRows?: number
  maxRows?: number
  onHeightChange?: (height: number) => void
}

const AutoResizeTextarea = React.forwardRef<HTMLTextAreaElement, AutoResizeTextareaProps>(
  ({ className, minRows = 1, maxRows = 15, onHeightChange, ...props }, ref) => {
    const textareaRef = React.useRef<HTMLTextAreaElement>(null)
    const [height, setHeight] = React.useState<number>(0)

    // Combine refs
    React.useImperativeHandle(ref, () => textareaRef.current!, [])

    const calculateHeight = React.useCallback(() => {
      const textarea = textareaRef.current
      if (!textarea) return

      // Reset height to auto to get the correct scrollHeight
      textarea.style.height = 'auto'
      
      // Calculate line height
      const computedStyle = window.getComputedStyle(textarea)
      const lineHeight = parseInt(computedStyle.lineHeight) || 24
      const paddingTop = parseInt(computedStyle.paddingTop) || 0
      const paddingBottom = parseInt(computedStyle.paddingBottom) || 0
      
      // Calculate min and max heights
      const minHeight = lineHeight * minRows + paddingTop + paddingBottom
      const maxHeight = lineHeight * maxRows + paddingTop + paddingBottom
      
      // Get the actual content height
      const scrollHeight = textarea.scrollHeight
      
      // Determine the final height
      const newHeight = Math.max(minHeight, Math.min(scrollHeight, maxHeight))
      
      // Set the height
      textarea.style.height = `${newHeight}px`
      setHeight(newHeight)
      onHeightChange?.(newHeight)
    }, [minRows, maxRows, onHeightChange])

    // Auto-resize on content change
    React.useEffect(() => {
      calculateHeight()
    }, [props.value, calculateHeight])

    // Auto-resize on mount
    React.useEffect(() => {
      calculateHeight()
    }, [calculateHeight])

    // Handle input changes
    const handleInput = (e: React.FormEvent<HTMLTextAreaElement>) => {
      calculateHeight()
      props.onInput?.(e)
    }

    const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      calculateHeight()
      props.onChange?.(e)
    }

    return (
      <div className="relative">
        <textarea
          ref={textareaRef}
          className={cn(
            // Base styles
            "w-full resize-none border-0 bg-transparent text-base text-black placeholder-gray-500",
            "focus:outline-none focus:ring-0",
            "transition-all duration-200 ease-in-out",
            // Custom scrollbar
            "custom-scrollbar",
            className
          )}
          style={{
            minHeight: `${24 * minRows + 16}px`, // 24px line height + 16px padding
            maxHeight: `${24 * maxRows + 16}px`,
            lineHeight: '24px',
            padding: '8px 12px',
            ...props.style
          }}
          onInput={handleInput}
          onChange={handleChange}
          {...props}
        />

      </div>
    )
  }
)

AutoResizeTextarea.displayName = "AutoResizeTextarea"

export { AutoResizeTextarea }
