import { createLogger, logRetrieval } from '../../utils/logger.js';

const logger = createLogger('HybridRetriever');

/**
 * Hybrid retriever combining graph and vector search
 */
export class HybridRetriever {
  constructor({ neo4jClient, config }) {
    this.neo4jClient = neo4jClient;
    this.config = config;
    this.isInitialized = false;
  }

  /**
   * Initialize the hybrid retriever
   */
  async initialize() {
    try {
      logger.info('Initializing hybrid retriever');
      this.isInitialized = true;
      logger.info('Hybrid retriever initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize hybrid retriever', { error: error.message });
      throw error;
    }
  }

  /**
   * Retrieve context using hybrid search
   */
  async retrieve(query, options = {}) {
    if (!this.isInitialized) {
      throw new Error('Hybrid retriever not initialized');
    }

    const startTime = Date.now();

    try {
      logger.debug('Starting context retrieval', {
        query: query.substring(0, 100),
        options
      });

      // Parse and analyze the query
      const parsedQuery = await this.parseQuery(query, options);

      // Get results from different sources
      const [graphResults, semanticResults] = await Promise.all([
        this.getGraphResults(parsedQuery),
        this.getSemanticResults(parsedQuery)
      ]);

      // Combine and rank results
      const combinedResults = await this.combineResults(graphResults, semanticResults, parsedQuery);
      const rankedResults = await this.rankResults(combinedResults, parsedQuery);

      // Format final response
      const response = this.formatResponse(rankedResults, options, startTime);

      const duration = Date.now() - startTime;
      logRetrieval(query, response.results.length, response.sources, duration);

      return response;

    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error('Context retrieval failed', {
        query: query.substring(0, 100),
        duration,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Parse and analyze the query
   */
  async parseQuery(query, options) {
    return {
      original: query,
      text: query.toLowerCase().trim(),
      intent: this.detectIntent(query),
      entities: this.extractEntities(query),
      filters: this.extractFilters(query, options),
      language: options.language || null,
      filePattern: options.filePattern || null,
      limit: options.limit || 10
    };
  }

  /**
   * Detect query intent
   */
  detectIntent(query) {
    const lowerQuery = query.toLowerCase();

    // Function-related queries
    if (lowerQuery.includes('function') || lowerQuery.includes('method') || 
        lowerQuery.includes('def ') || lowerQuery.includes('async')) {
      return 'function_search';
    }

    // Class-related queries
    if (lowerQuery.includes('class') || lowerQuery.includes('interface') || 
        lowerQuery.includes('type') || lowerQuery.includes('struct')) {
      return 'class_search';
    }

    // Variable-related queries
    if (lowerQuery.includes('variable') || lowerQuery.includes('const') || 
        lowerQuery.includes('let ') || lowerQuery.includes('var ')) {
      return 'variable_search';
    }

    // Import/dependency queries
    if (lowerQuery.includes('import') || lowerQuery.includes('require') || 
        lowerQuery.includes('dependency') || lowerQuery.includes('module')) {
      return 'dependency_search';
    }

    // File-related queries
    if (lowerQuery.includes('file') || lowerQuery.includes('path') || 
        lowerQuery.includes('.js') || lowerQuery.includes('.py')) {
      return 'file_search';
    }

    return 'general_search';
  }

  /**
   * Extract entities from query
   */
  extractEntities(query) {
    const entities = [];
    
    // Extract identifiers (CamelCase, snake_case, etc.)
    const identifierRegex = /\b[A-Za-z_][A-Za-z0-9_]*\b/g;
    const matches = query.match(identifierRegex) || [];
    
    matches.forEach(match => {
      if (match.length > 2 && !this.isCommonWord(match)) {
        entities.push({
          type: 'identifier',
          value: match,
          confidence: this.calculateEntityConfidence(match)
        });
      }
    });

    // Extract file extensions
    const extensionRegex = /\.[a-z]{1,4}\b/g;
    const extensions = query.match(extensionRegex) || [];
    extensions.forEach(ext => {
      entities.push({
        type: 'file_extension',
        value: ext,
        confidence: 0.9
      });
    });

    return entities;
  }

  /**
   * Extract filters from query and options
   */
  extractFilters(query, options) {
    const filters = {};

    // Language filters
    const languages = ['javascript', 'typescript', 'python', 'java', 'go', 'rust'];
    for (const lang of languages) {
      if (query.toLowerCase().includes(lang)) {
        filters.language = lang;
        break;
      }
    }

    // Apply option filters
    if (options.language) filters.language = options.language;
    if (options.filePattern) filters.filePattern = options.filePattern;
    if (options.minLines) filters.minLines = options.minLines;
    if (options.maxLines) filters.maxLines = options.maxLines;

    return filters;
  }

  /**
   * Get results from graph database
   */
  async getGraphResults(parsedQuery) {
    try {
      const cypherQuery = this.buildCypherQuery(parsedQuery);
      const result = await this.neo4jClient.run(cypherQuery.query, cypherQuery.parameters);
      
      return result.records.map(record => {
        const node = record.get('n');
        const file = record.get('f');
        
        return {
          id: node.identity.toNumber(),
          type: 'graph',
          source: 'neo4j',
          node: {
            id: node.identity.toNumber(),
            labels: node.labels,
            properties: node.properties
          },
          file: file ? {
            path: file.properties.path,
            language: file.properties.language
          } : null,
          score: this.calculateGraphScore(node, parsedQuery),
          metadata: {
            nodeType: node.labels[0],
            queryIntent: parsedQuery.intent
          }
        };
      });

    } catch (error) {
      logger.error('Graph search failed', { error: error.message });
      return [];
    }
  }

  /**
   * Get semantic search results (placeholder for vector search)
   */
  async getSemanticResults(parsedQuery) {
    // This is a placeholder for vector/semantic search
    // In a full implementation, this would query a vector database
    try {
      logger.debug('Semantic search not yet implemented, returning empty results');
      return [];
    } catch (error) {
      logger.error('Semantic search failed', { error: error.message });
      return [];
    }
  }

  /**
   * Build Cypher query based on parsed query
   */
  buildCypherQuery(parsedQuery) {
    let query = '';
    let parameters = {};

    switch (parsedQuery.intent) {
      case 'function_search':
        query = `
          MATCH (f:File)-[:CONTAINS]->(n:Function)
          WHERE n.name CONTAINS $searchTerm
          ${this.buildLanguageFilter('f')}
          RETURN n, f
          ORDER BY n.name
          LIMIT $limit
        `;
        break;

      case 'class_search':
        query = `
          MATCH (f:File)-[:CONTAINS]->(n:Class)
          WHERE n.name CONTAINS $searchTerm
          ${this.buildLanguageFilter('f')}
          RETURN n, f
          ORDER BY n.name
          LIMIT $limit
        `;
        break;

      case 'variable_search':
        query = `
          MATCH (f:File)-[:DEFINES]->(n:Variable)
          WHERE n.name CONTAINS $searchTerm
          ${this.buildLanguageFilter('f')}
          RETURN n, f
          ORDER BY n.name
          LIMIT $limit
        `;
        break;

      case 'file_search':
        query = `
          MATCH (n:File)
          WHERE n.path CONTAINS $searchTerm
          ${this.buildLanguageFilter('n')}
          RETURN n, n as f
          ORDER BY n.path
          LIMIT $limit
        `;
        break;

      default:
        query = `
          MATCH (f:File)-[:CONTAINS|DEFINES]->(n)
          WHERE n.name CONTAINS $searchTerm
          ${this.buildLanguageFilter('f')}
          RETURN n, f
          ORDER BY n.name
          LIMIT $limit
        `;
    }

    // Extract search term from entities or use original query
    const searchTerm = parsedQuery.entities.length > 0 
      ? parsedQuery.entities[0].value 
      : parsedQuery.text;

    parameters = {
      searchTerm: searchTerm,
      limit: parsedQuery.limit
    };

    // Add language filter parameter if specified
    if (parsedQuery.filters.language) {
      parameters.language = parsedQuery.filters.language;
    }

    return { query, parameters };
  }

  /**
   * Build language filter for Cypher query
   */
  buildLanguageFilter(nodeAlias) {
    return `AND (NOT EXISTS($language) OR ${nodeAlias}.language = $language)`;
  }

  /**
   * Combine results from different sources
   */
  async combineResults(graphResults, semanticResults, parsedQuery) {
    const combined = [];
    const seenIds = new Set();

    // Add graph results
    for (const result of graphResults) {
      const id = `graph_${result.id}`;
      if (!seenIds.has(id)) {
        seenIds.add(id);
        combined.push(result);
      }
    }

    // Add semantic results (when implemented)
    for (const result of semanticResults) {
      const id = `semantic_${result.id}`;
      if (!seenIds.has(id)) {
        seenIds.add(id);
        combined.push(result);
      }
    }

    return combined;
  }

  /**
   * Rank combined results
   */
  async rankResults(results, parsedQuery) {
    // Apply hybrid ranking algorithm
    const weights = this.config.hybridWeights || { graph: 0.6, vector: 0.4 };

    return results
      .map(result => ({
        ...result,
        finalScore: this.calculateFinalScore(result, parsedQuery, weights)
      }))
      .sort((a, b) => b.finalScore - a.finalScore);
  }

  /**
   * Calculate graph-based score
   */
  calculateGraphScore(node, parsedQuery) {
    let score = 0.5; // Base score

    // Exact name match bonus
    if (node.properties.name === parsedQuery.entities[0]?.value) {
      score += 0.4;
    }

    // Partial name match bonus
    if (node.properties.name && node.properties.name.includes(parsedQuery.entities[0]?.value)) {
      score += 0.2;
    }

    // Intent match bonus
    const nodeType = node.labels[0]?.toLowerCase();
    if (parsedQuery.intent.includes(nodeType)) {
      score += 0.3;
    }

    return Math.min(score, 1.0);
  }

  /**
   * Calculate final hybrid score
   */
  calculateFinalScore(result, parsedQuery, weights) {
    const baseScore = result.score || 0.5;
    const typeWeight = result.type === 'graph' ? weights.graph : weights.vector;
    
    return baseScore * typeWeight;
  }

  /**
   * Format final response
   */
  formatResponse(results, options, startTime) {
    const limit = options.limit || 10;
    const limitedResults = results.slice(0, limit);

    return {
      results: limitedResults.map(result => ({
        id: result.id,
        type: result.metadata?.nodeType || 'unknown',
        name: result.node?.properties?.name || 'unknown',
        file: result.file,
        score: result.finalScore,
        location: {
          startLine: result.node?.properties?.startLine,
          endLine: result.node?.properties?.endLine
        },
        metadata: result.metadata
      })),
      total: results.length,
      limited: results.length > limit,
      sources: [...new Set(results.map(r => r.source))],
      query: {
        original: options.originalQuery,
        processed: true
      },
      timestamp: new Date().toISOString(),
      processingTime: Date.now() - startTime
    };
  }

  /**
   * Check if word is common and should be ignored
   */
  isCommonWord(word) {
    const commonWords = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'];
    return commonWords.includes(word.toLowerCase());
  }

  /**
   * Calculate entity confidence
   */
  calculateEntityConfidence(entity) {
    // Simple heuristic based on entity characteristics
    if (entity.match(/^[A-Z][a-z]+([A-Z][a-z]+)*$/)) return 0.9; // CamelCase
    if (entity.match(/^[a-z]+(_[a-z]+)*$/)) return 0.8; // snake_case
    if (entity.length > 10) return 0.6; // Long identifiers
    return 0.7; // Default
  }

  /**
   * Get retriever health status
   */
  async getHealth() {
    return {
      status: this.isInitialized ? 'healthy' : 'not_initialized',
      isInitialized: this.isInitialized,
      graphConnection: this.neo4jClient ? 'connected' : 'disconnected'
    };
  }
}

export default HybridRetriever;
