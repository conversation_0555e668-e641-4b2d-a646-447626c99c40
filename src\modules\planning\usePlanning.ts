/**
 * Planning Hook - Clean React state management
 * Replaces useState hell with proper state machine
 */

import { useState, useCallback, useRef, useEffect } from 'react'
import {
  PlanningState,
  PlanningStep,
  StartPlanningRequest,
  PlanningStepRequest,
  PlanningResponse,
  PlanningEvent
} from './planning.types'
import usePartySocket from 'partysocket/react'

type PlanningStatus = 'idle' | 'planning' | 'step-executing' | 'completed' | 'error'

interface UsePlanningState {
  status: PlanningStatus
  currentStep?: PlanningStep
  results: Record<string, any>
  question?: {
    id: string
    question: string
    type: 'text' | 'textarea'
    placeholder?: string
    optional?: boolean
  }
  error?: string
  progress: number
  events: PlanningEvent[]
}

interface UsePlanningActions {
  startPlanning: (request: StartPlanningRequest) => Promise<void>
  executeStep: (request: PlanningStepRequest) => Promise<void>
  answerQuestion: (answer: string) => Promise<void>
  reset: () => void
  retry: () => void
}

interface UsePlanningReturn extends UsePlanningState, UsePlanningActions {
  isLoading: boolean
  canRetry: boolean
  isConnected: boolean
  participants: number
}

const TOTAL_STEPS = 13 // Total number of planning steps

export function usePlanning(sessionId?: string): UsePlanningReturn {
  const [state, setState] = useState<UsePlanningState>({
    status: 'idle',
    results: {},
    progress: 0,
    events: []
  })

  const [participants, setParticipants] = useState(0)
  const lastRequestRef = useRef<StartPlanningRequest | null>(null)
  const abortControllerRef = useRef<AbortController | null>(null)

  // Real-time connection to PartyKit
  const socket = usePartySocket({
    host: process.env.NEXT_PUBLIC_PARTYKIT_HOST || "localhost:1999",
    room: sessionId || "default",
    party: "planning",
    query: {
      userId: `user_${Date.now()}`
    },
    onMessage: (event) => {
      try {
        const message = JSON.parse(event.data)

        switch (message.type) {
          case "state_sync":
            setState(prev => ({
              ...prev,
              status: message.data.status,
              progress: message.data.progress,
              results: message.data.results,
              currentStep: message.data.currentStep
            }))
            setParticipants(message.data.participants?.length || 0)
            break

          case "step_completed":
            setState(prev => ({
              ...prev,
              results: { ...prev.results, [message.data.step]: message.data.results },
              progress: message.data.progress,
              events: [...prev.events, {
                type: 'step_completed',
                payload: {
                  step: message.data.step,
                  results: message.data.results,
                  timestamp: message.timestamp
                }
              }]
            }))
            break

          case "step_failed":
            setState(prev => ({
              ...prev,
              status: 'error',
              error: message.data.error,
              events: [...prev.events, {
                type: 'planning_failed',
                payload: {
                  error: message.data.error,
                  step: message.data.step,
                  timestamp: message.timestamp
                }
              }]
            }))
            break

          case "progress_update":
            setState(prev => ({
              ...prev,
              status: message.data.status,
              progress: message.data.progress
            }))
            break

          case "user_joined":
          case "user_left":
            setParticipants(message.data.participantCount || 0)
            break
        }
      } catch (error) {
        console.error("Error parsing real-time message:", error)
      }
    }
  })

  /**
   * Start a new planning session
   */
  const startPlanning = useCallback(async (request: StartPlanningRequest) => {
    // Cancel any ongoing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    abortControllerRef.current = new AbortController()
    lastRequestRef.current = request

    setState(prev => ({
      ...prev,
      status: 'planning',
      error: undefined,
      progress: 0,
      events: []
    }))

    try {
      const response = await fetch('/api/planning', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(request),
        signal: abortControllerRef.current.signal
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const result: PlanningResponse = await response.json()

      if (result.success) {
        setState(prev => ({
          ...prev,
          status: result.completed ? 'completed' : 'idle',
          results: result.results || {},
          question: result.question,
          progress: result.completed ? 100 : 0,
          events: [...prev.events, {
            type: 'planning_completed',
            payload: {
              results: result.results || {},
              timestamp: new Date().toISOString()
            }
          }]
        }))
      } else {
        throw new Error(result.error || 'Planning failed')
      }

    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        return // Request was cancelled
      }

      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      setState(prev => ({
        ...prev,
        status: 'error',
        error: errorMessage,
        events: [...prev.events, {
          type: 'planning_failed',
          payload: {
            error: errorMessage,
            timestamp: new Date().toISOString()
          }
        }]
      }))
    }
  }, [])

  /**
   * Execute a single planning step
   */
  const executeStep = useCallback(async (request: PlanningStepRequest) => {
    setState(prev => ({
      ...prev,
      status: 'step-executing',
      currentStep: request.step,
      error: undefined
    }))

    try {
      const response = await fetch('/api/planning/step', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(request)
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const result: PlanningResponse = await response.json()

      if (result.success) {
        setState(prev => {
          const newResults = { ...prev.results, ...result.results }
          const completedSteps = Object.keys(newResults).length
          const progress = Math.round((completedSteps / TOTAL_STEPS) * 100)

          return {
            ...prev,
            status: result.needsInput ? 'idle' : (result.completed ? 'completed' : 'idle'),
            results: newResults,
            question: result.question,
            progress,
            events: [...prev.events, {
              type: 'step_completed',
              payload: {
                step: request.step,
                results: result.results || {},
                timestamp: new Date().toISOString()
              }
            }]
          }
        })
      } else {
        throw new Error(result.error || 'Step execution failed')
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      setState(prev => ({
        ...prev,
        status: 'error',
        error: errorMessage,
        events: [...prev.events, {
          type: 'planning_failed',
          payload: {
            error: errorMessage,
            step: request.step,
            timestamp: new Date().toISOString()
          }
        }]
      }))
    }
  }, [])

  /**
   * Answer a question and continue planning
   */
  const answerQuestion = useCallback(async (answer: string) => {
    if (!state.question || !state.currentStep) {
      throw new Error('No question to answer')
    }

    const request: PlanningStepRequest = {
      step: state.currentStep,
      context: {
        ...state.results,
        question: state.question,
        userAnswers: state.results.userAnswers || {}
      },
      answer
    }

    await executeStep(request)
  }, [state.question, state.currentStep, state.results, executeStep])

  /**
   * Reset planning state
   */
  const reset = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    setState({
      status: 'idle',
      results: {},
      progress: 0,
      events: []
    })

    lastRequestRef.current = null
  }, [])

  /**
   * Retry last planning request
   */
  const retry = useCallback(async () => {
    if (lastRequestRef.current) {
      await startPlanning(lastRequestRef.current)
    }
  }, [startPlanning])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [])

  return {
    ...state,
    startPlanning,
    executeStep,
    answerQuestion,
    reset,
    retry,
    isLoading: state.status === 'planning' || state.status === 'step-executing',
    canRetry: state.status === 'error' && lastRequestRef.current !== null,
    isConnected: socket?.readyState === WebSocket.OPEN,
    participants
  }
}
