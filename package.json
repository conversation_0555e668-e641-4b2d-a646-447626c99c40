{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start", "test": "vitest", "test:e2e": "playwright test", "test:coverage": "vitest --coverage", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "type-check": "tsc --noEmit", "dev:full": "concurrently \"pnpm dev\" \"pnpm storybook\""}, "dependencies": {"@emotion/is-prop-valid": "latest", "@hookform/resolvers": "^3.9.1", "@langchain/core": "latest", "@langchain/langgraph": "latest", "@langchain/openai": "latest", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@sentry/nextjs": "^8.42.0", "@t3-oss/env-nextjs": "^0.11.1", "@tanstack/react-query": "^5.59.0", "@trpc/client": "^10.45.2", "@trpc/react-query": "^10.45.2", "@trpc/server": "^10.45.2", "@types/three": "^0.178.1", "@uploadthing/react": "^7.2.0", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "4.1.0", "embla-carousel-react": "8.5.1", "framer-motion": "^11.11.17", "inngest": "^3.25.1", "input-otp": "1.4.1", "ioredis": "^5.6.1", "lucide-react": "^0.454.0", "nanoid": "^5.0.9", "neo4j-driver": "^5.28.1", "next": "15.2.4", "next-themes": "^0.4.4", "partykit": "^0.0.111", "partysocket": "^1.0.2", "posthog-js": "^1.176.0", "react": "^19", "react-day-picker": "8.10.1", "react-dom": "^19", "react-hook-form": "^7.53.2", "react-markdown": "^10.1.0", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "remark-gfm": "^4.0.1", "sonner": "^1.7.1", "superjson": "^2.2.1", "tailwind-merge": "^2.5.5", "tailwind-scrollbar": "3.1.0", "tailwindcss-animate": "^1.0.7", "three": "^0.178.0", "uploadthing": "^7.2.0", "vaul": "^0.9.6", "zod": "^3.24.1", "zod-to-json-schema": "latest"}, "devDependencies": {"@playwright/test": "^1.49.1", "@storybook/addon-essentials": "^8.4.7", "@storybook/addon-interactions": "^8.4.7", "@storybook/addon-links": "^8.4.7", "@storybook/blocks": "^8.4.7", "@storybook/nextjs": "^8.4.7", "@storybook/react": "^8.4.7", "@storybook/test": "^8.4.7", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "@vitest/coverage-v8": "^2.1.8", "concurrently": "^9.1.0", "msw": "^2.6.8", "postcss": "^8.5", "storybook": "^8.4.7", "tailwindcss": "^3.4.17", "typescript": "^5", "vitest": "^2.1.8"}}