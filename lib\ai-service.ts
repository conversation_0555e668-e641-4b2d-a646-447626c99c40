/**
 * AI Service - Centralized AI integration for all planning operations
 * Replaces all mock data with real AI-powered responses
 */

interface AIResponse {
  success: boolean;
  data?: any;
  error?: string;
  retryAfter?: number;
}

interface AIRequestConfig {
  model?: string;
  temperature?: number;
  maxTokens?: number;
  retries?: number;
  timeout?: number;
}

export class AIService {
  private apiKey: string | null = null;
  private baseUrl: string;
  private defaultConfig: AIRequestConfig;

  constructor() {
    this.baseUrl = 'https://openrouter.ai/api/v1';
    this.defaultConfig = {
      model: 'anthropic/claude-sonnet-4',
      temperature: 0.7,
      maxTokens: 4000,
      retries: 3,
      timeout: 30000
    };
  }

  private ensureApiKey(): void {
    if (!this.apiKey) {
      this.apiKey = process.env.OPENROUTER_API_KEY || '';
      if (!this.apiKey) {
        throw new Error('OPENROUTER_API_KEY environment variable is required');
      }
    }
  }

  /**
   * Make AI request with retry logic and proper error handling
   */
  private async makeAIRequest(
    prompt: string,
    systemPrompt: string,
    config: AIRequestConfig = {}
  ): Promise<AIResponse> {
    this.ensureApiKey(); // Ensure API key is available before making request
    const finalConfig = { ...this.defaultConfig, ...config };
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= finalConfig.retries!; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), finalConfig.timeout);

        const response = await fetch(`${this.baseUrl}/chat/completions`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
            'HTTP-Referer': process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
            'X-Title': 'Linear Tasks Planning Agent'
          },
          body: JSON.stringify({
            model: finalConfig.model,
            messages: [
              { role: 'system', content: systemPrompt },
              { role: 'user', content: prompt }
            ],
            temperature: finalConfig.temperature,
            max_tokens: finalConfig.maxTokens
          }),
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(`AI API Error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
        }

        const data = await response.json();
        const content = data.choices?.[0]?.message?.content;

        if (!content) {
          throw new Error('No content received from AI API');
        }

        return {
          success: true,
          data: content
        };

      } catch (error) {
        lastError = error as Error;
        console.error(`AI request attempt ${attempt} failed:`, error);

        // Don't retry on certain errors
        if (error instanceof Error) {
          if (error.message.includes('401') || error.message.includes('403')) {
            break; // Authentication errors shouldn't be retried
          }
          if (error.message.includes('429')) {
            // Rate limit - exponential backoff
            await new Promise(resolve => setTimeout(resolve, Math.min(2000 * Math.pow(2, attempt), 30000)));
          }
          if (error.message.includes('timeout')) {
            // Timeout - shorter wait before retry
            await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
          }
        }

        if (attempt < finalConfig.retries!) {
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        }
      }
    }

    return {
      success: false,
      error: lastError?.message || 'AI request failed after all retries'
    };
  }

  /**
   * Parse JSON response with error handling
   */
  private parseJSONResponse(content: string): any {
    try {
      // Clean up common AI response formatting issues
      let cleaned = content
        .replace(/```json\s*/g, '')
        .replace(/```\s*/g, '')
        .replace(/^\s*[\r\n]+/gm, '')
        .trim();

      // Try to parse as-is first (most AI responses are already valid JSON)
      return JSON.parse(cleaned);
    } catch (error) {
      console.error('Failed to parse AI JSON response:', error);
      console.error('Raw content:', content.substring(0, 500) + '...');

      // Try to extract JSON from the content (AI often adds explanatory text before JSON)
      try {
        // Look for JSON object starting with { and ending with }
        const jsonMatch = content.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          let extractedJson = jsonMatch[0];

          // Basic cleanup for common issues
          extractedJson = extractedJson
            .replace(/[\x00-\x1F]/g, '') // Remove control characters
            .replace(/,(\s*[}\]])/g, '$1') // Remove trailing commas
            .replace(/([{,]\s*)(\w+):/g, '$1"$2":') // Quote unquoted keys
            .replace(/:\s*'([^']*)'/g, ': "$1"') // Convert single quotes to double quotes
            .trim();

          console.log('Attempting to parse extracted JSON...');
          return JSON.parse(extractedJson);
        }
      } catch (secondError) {
        console.error('Second JSON parse attempt failed:', secondError);
      }

      // Return a basic fallback structure
      console.log('All JSON parsing failed, returning fallback structure');
      return {
        structure: {
          folders: [
            {
              name: "src",
              purpose: "Main source code directory",
              subfolders: ["components", "pages", "utils", "styles"],
              keyFiles: ["App.tsx", "main.tsx"]
            }
          ]
        },
        keyFiles: [
          {
            path: "src/App.tsx",
            purpose: "Main application component",
            dependencies: [],
            priority: "high"
          }
        ],
        conventions: {
          naming: "camelCase for files, PascalCase for components",
          organization: "Feature-based organization",
          imports: "ES6 imports with absolute paths"
        },
        buildSystem: {
          configFiles: ["package.json", "tsconfig.json"],
          scripts: {
            dev: "npm run dev",
            build: "npm run build",
            test: "npm test"
          }
        }
      };
    }
  }

  /**
   * Answer clarification questions autonomously
   */
  async answerQuestionsAutonomously(prompt: string, analysis: any, questions: any[]): Promise<any> {
    const systemPrompt = `You are an expert business analyst who can autonomously answer clarification questions based on project context and industry best practices.

Given the project prompt and analysis, provide intelligent answers to clarification questions. Make reasonable assumptions based on:
- Industry standards and best practices
- Common project requirements
- Technical feasibility
- User experience principles
- Business logic

Return a JSON object with this structure:

{
  "overview": "Brief explanation of the autonomous decision-making approach",
  "answers": [
    {
      "questionId": "question_id",
      "question": "Original question text",
      "answer": "Intelligent answer based on context and best practices",
      "reasoning": "Why this answer was chosen",
      "confidence": "high|medium|low",
      "assumptions": ["assumption1", "assumption2"],
      "alternatives": ["alternative1", "alternative2"]
    }
  ],
  "recommendations": [
    {
      "category": "architecture|design|features|technology",
      "recommendation": "Specific recommendation",
      "rationale": "Why this is recommended",
      "impact": "Expected impact on the project"
    }
  ],
  "riskAssessment": [
    {
      "decision": "Decision made autonomously",
      "risk": "Potential risk of this decision",
      "mitigation": "How to mitigate this risk",
      "reviewPoint": "When to review this decision"
    }
  ]
}

AUTONOMOUS DECISION PRINCIPLES:
- Choose the most common, industry-standard approach
- Prioritize user experience and accessibility
- Select scalable and maintainable solutions
- Consider security and performance implications
- Make decisions that can be easily changed later
- Err on the side of simplicity and best practices

IMPORTANT:
- Provide specific, actionable answers
- Explain the reasoning behind each decision
- Consider the project context and requirements
- Make decisions that align with modern development practices`;

    const contextPrompt = `Project: ${prompt}

Analysis: ${JSON.stringify(analysis)}

Questions to Answer: ${JSON.stringify(questions)}

Provide autonomous answers based on best practices and project context.`;

    const response = await this.makeAIRequest(contextPrompt, systemPrompt);

    if (!response.success) {
      throw new Error(response.error || 'Failed to answer questions autonomously');
    }

    return this.parseJSONResponse(response.data);
  }

  /**
   * Analyze project prompt
   */
  async analyzePrompt(prompt: string, options: { designStyleGuide?: string, hasImages?: boolean } = {}, userConfig: { model?: string, apiKey?: string } = {}): Promise<any> {
    const { designStyleGuide, hasImages } = options || {};

    let systemPrompt = `You are a senior project analyst with expertise in software development, product management, and technical architecture.

Analyze the given project prompt and extract comprehensive information. Return a JSON object with the following structure:

{
  "projectType": "web_app" | "mobile_app" | "desktop_app" | "game" | "api" | "library" | "other",
  "complexity": "low" | "medium" | "high" | "enterprise",
  "domain": "e-commerce" | "social" | "productivity" | "gaming" | "fintech" | "healthcare" | "education" | "other",
  "features": ["feature1", "feature2", ...],
  "technicalHints": ["hint1", "hint2", ...],
  "estimatedTimeframe": "1-2 weeks" | "1-2 months" | "3-6 months" | "6+ months",
  "teamSize": "solo" | "small (2-4)" | "medium (5-10)" | "large (10+)",
  "riskFactors": ["risk1", "risk2", ...],
  "successCriteria": ["criteria1", "criteria2", ...]
}

Be thorough and specific in your analysis. Extract as much meaningful information as possible.

IMPORTANT: Return only valid JSON. Do not include newlines, tabs, or control characters in string values.`;

    if (hasImages && designStyleGuide) {
      systemPrompt += `

ADDITIONAL CONTEXT: The user has provided design reference images that have been analyzed to create a comprehensive style guide. This style guide should be considered when analyzing the project requirements, as it indicates the user has specific design preferences and visual direction for the project.`;
    }

    let analysisPrompt = prompt;
    if (hasImages && designStyleGuide) {
      analysisPrompt += `

DESIGN CONTEXT: The user has provided reference images with the following extracted style guide:

${designStyleGuide}

Consider this design context when analyzing the project requirements.`;
    }

    // Use user configuration if provided
    const requestConfig: AIRequestConfig = {};
    if (userConfig.model) {
      requestConfig.model = userConfig.model;
    }

    // Temporarily store user API key if provided
    const originalApiKey = this.apiKey;
    if (userConfig.apiKey) {
      this.apiKey = userConfig.apiKey;
    }

    try {
      const response = await this.makeAIRequest(analysisPrompt, systemPrompt, requestConfig);

      if (!response.success) {
        throw new Error(response.error || 'Failed to analyze prompt');
      }

      return this.parseJSONResponse(response.data);
    } finally {
      // Restore original API key
      this.apiKey = originalApiKey;
    }


  }

  /**
   * Generate clarification questions
   */
  async generateClarificationQuestions(prompt: string, analysis: any): Promise<any> {
    const systemPrompt = `You are a product manager expert at gathering requirements. Based on the project analysis, generate intelligent clarification questions.

Return a JSON object with this structure:

{
  "questions": [
    {
      "id": "unique_id",
      "question": "Clear, specific question",
      "type": "text" | "select" | "multiselect",
      "options": ["option1", "option2"] // only for select/multiselect
    }
  ],
  "priority": "high" | "medium" | "low",
  "reasoning": "Why these questions are important"
}

IMPORTANT: Return only valid JSON. Do not include newlines, tabs, or control characters in string values. Keep reasoning concise and on a single line.

Focus on the most critical unknowns that would significantly impact the project scope, timeline, or technical approach.`;

    const contextPrompt = `Project: ${prompt}

Analysis: ${JSON.stringify(analysis)}

Generate 3-5 high-impact clarification questions.`;

    const response = await this.makeAIRequest(contextPrompt, systemPrompt);
    
    if (!response.success) {
      throw new Error(response.error || 'Failed to generate clarification questions');
    }

    return this.parseJSONResponse(response.data);
  }

  /**
   * Generate project summary
   */
  async generateSummary(prompt: string, analysis: any, clarifications: any = {}): Promise<any> {
    const systemPrompt = `You are a technical writer specializing in project documentation. Create a comprehensive project summary.

Return a JSON object with this structure:

{
  "overview": "Clear, concise project overview",
  "objectives": ["objective1", "objective2", ...],
  "scope": "Detailed scope description",
  "keyFeatures": ["feature1", "feature2", ...],
  "targetAudience": "Description of target users",
  "successMetrics": ["metric1", "metric2", ...],
  "constraints": ["constraint1", "constraint2", ...],
  "assumptions": ["assumption1", "assumption2", ...]
}

Be specific and actionable in your descriptions.`;

    const contextPrompt = `Project: ${prompt}

Analysis: ${JSON.stringify(analysis)}

Clarifications: ${JSON.stringify(clarifications)}

Create a comprehensive project summary.`;

    const response = await this.makeAIRequest(contextPrompt, systemPrompt);
    
    if (!response.success) {
      throw new Error(response.error || 'Failed to generate summary');
    }

    return this.parseJSONResponse(response.data);
  }

  /**
   * Select technology stack
   */
  async selectTechStack(prompt: string, analysis: any, clarifications: any = {}): Promise<any> {
    const systemPrompt = `You are a senior technical architect with expertise in modern technology stacks. Recommend the optimal technology stack.

Return a JSON object with this structure:

{
  "frontend": {
    "framework": "React" | "Vue" | "Angular" | "Svelte" | "Next.js" | "other",
    "language": "TypeScript" | "JavaScript",
    "styling": "Tailwind CSS" | "Styled Components" | "CSS Modules" | "other",
    "reasoning": "Why this choice"
  },
  "backend": {
    "framework": "Node.js" | "Python" | "Go" | "Rust" | "Java" | "C#" | "other",
    "database": "PostgreSQL" | "MongoDB" | "MySQL" | "SQLite" | "other",
    "authentication": "Auth0" | "Firebase Auth" | "NextAuth" | "custom" | "other",
    "reasoning": "Why this choice"
  },
  "infrastructure": {
    "hosting": "Vercel" | "Netlify" | "AWS" | "Google Cloud" | "Azure" | "other",
    "cicd": "GitHub Actions" | "GitLab CI" | "Jenkins" | "other",
    "monitoring": "Sentry" | "LogRocket" | "DataDog" | "other",
    "reasoning": "Why this choice"
  },
  "additionalTools": ["tool1", "tool2", ...],
  "alternatives": {
    "considered": ["alternative1", "alternative2", ...],
    "reasoning": "Why alternatives were not chosen"
  }
}

Consider project complexity, team size, timeline, and specific requirements.`;

    const contextPrompt = `Project: ${prompt}

Analysis: ${JSON.stringify(analysis)}

Clarifications: ${JSON.stringify(clarifications)}

Recommend the optimal technology stack.`;

    const response = await this.makeAIRequest(contextPrompt, systemPrompt);
    
    if (!response.success) {
      throw new Error(response.error || 'Failed to select tech stack');
    }

    return this.parseJSONResponse(response.data);
  }

  /**
   * Create Product Requirements Document
   */
  async createPRD(prompt: string, analysis: any, techStack: any, clarifications: any = {}): Promise<any> {
    const systemPrompt = `You are a senior product manager expert at creating comprehensive PRDs. Create a detailed Product Requirements Document that is both technical and accessible to non-technical stakeholders.

Return a JSON object with this structure:

{
  "overview": "Clear, concise project overview explaining what this product does and why it matters",
  "businessGoals": ["Primary business goal 1", "Primary business goal 2"],
  "targetAudience": {
    "primary": "Primary user type and their characteristics",
    "secondary": "Secondary user type and their characteristics"
  },
  "objectives": ["Specific, measurable objective 1", "Specific, measurable objective 2"],
  "features": [
    {
      "name": "Feature name",
      "description": "Detailed feature description with user benefit",
      "priority": "high|medium|low",
      "complexity": "simple|moderate|complex",
      "businessValue": "Why this feature matters to the business",
      "userValue": "Why this feature matters to users"
    }
  ],
  "userStories": [
    {
      "id": "US001",
      "epic": "Epic name this story belongs to",
      "title": "Clear, action-oriented user story title",
      "description": "As a [specific user type], I want [specific goal] so that [clear benefit]",
      "acceptanceCriteria": [
        "Given [context], when [action], then [expected result]",
        "The system should [specific behavior]",
        "Users must be able to [specific capability]"
      ],
      "priority": "high|medium|low",
      "estimatedEffort": "1-3 days",
      "dependencies": ["US002", "US003"],
      "businessValue": "High|Medium|Low",
      "userValue": "High|Medium|Low"
    }
  ],
  "functionalRequirements": [
    {
      "id": "FR001",
      "title": "Requirement title",
      "description": "Detailed functional requirement",
      "priority": "Must Have|Should Have|Could Have|Won't Have",
      "acceptanceCriteria": ["criteria1", "criteria2", ...]
    }
  ],
  "nonFunctionalRequirements": [
    {
      "id": "NFR001",
      "category": "Performance|Security|Usability|Reliability|Scalability",
      "title": "Requirement title",
      "description": "Detailed non-functional requirement with measurable criteria",
      "acceptanceCriteria": "Specific, measurable criteria"
    }
  ],
  "constraints": [
    {
      "type": "Technical|Business|Legal|Time|Budget",
      "description": "Specific constraint and its impact"
    }
  ],
  "assumptions": [
    {
      "description": "Specific assumption",
      "risk": "What happens if this assumption is wrong",
      "validation": "How to validate this assumption"
    }
  ],
  "timeline": {
    "totalDuration": "X weeks/months",
    "phases": [
      {
        "name": "Phase name",
        "duration": "X weeks",
        "description": "What happens in this phase",
        "deliverables": ["Specific deliverable 1", "Specific deliverable 2"],
        "milestones": ["Key milestone 1", "Key milestone 2"]
      }
    ]
  },
  "successMetrics": [
    {
      "metric": "Metric name",
      "target": "Specific target value",
      "measurement": "How this will be measured"
    }
  ],
  "risks": [
    {
      "risk": "Specific risk description",
      "impact": "High|Medium|Low",
      "probability": "High|Medium|Low",
      "mitigation": "How to mitigate this risk"
    }
  ]
}

Generate at least 8-12 comprehensive user stories covering all major user journeys. Be specific and detailed.`;

    const contextPrompt = `Project: ${prompt}

Analysis: ${JSON.stringify(analysis)}

Tech Stack: ${JSON.stringify(techStack)}

Clarifications: ${JSON.stringify(clarifications)}

Create a comprehensive PRD.`;

    const response = await this.makeAIRequest(contextPrompt, systemPrompt);

    if (!response.success) {
      throw new Error(response.error || 'Failed to create PRD');
    }

    return this.parseJSONResponse(response.data);
  }

  /**
   * Design wireframes
   */
  async designWireframes(prompt: string, analysis: any, prd: any): Promise<any> {
    const systemPrompt = `You are a UX/UI designer expert at creating comprehensive wireframes. Design both ASCII wireframes and Mermaid diagrams for the project.

Return a JSON object with this structure:

{
  "overview": "Brief explanation of the wireframe design approach and key user experience decisions",
  "pages": [
    {
      "name": "Page name",
      "type": "landing|dashboard|form|list|detail|auth|admin|testing|other",
      "purpose": "What this page does and why it's important",
      "priority": "high|medium|low",
      "asciiWireframe": "ASCII art wireframe using box drawing characters",
      "mermaidDiagram": "Mermaid flowchart showing page flow and interactions",
      "components": [
        {
          "name": "Component name",
          "type": "header|navigation|form|card|modal|button|input|other",
          "description": "What this component does",
          "interactions": ["click", "hover", "submit", "etc"]
        }
      ],
      "userActions": ["Primary action 1", "Secondary action 2"],
      "responsiveNotes": "How this page adapts to different screen sizes"
    }
  ],
  "userFlows": [
    {
      "name": "Flow name (e.g., User Registration, Data Analysis)",
      "description": "What this flow accomplishes",
      "steps": [
        {
          "step": 1,
          "action": "Specific user action",
          "page": "Page name",
          "result": "What happens next",
          "validation": "Any validation or error handling"
        }
      ],
      "mermaidFlowchart": "Mermaid flowchart diagram for this user flow"
    }
  ],
  "testingPages": [
    {
      "name": "Testing page name",
      "purpose": "What backend functionality this page tests",
      "features": ["Feature 1 being tested", "Feature 2 being tested"],
      "asciiWireframe": "Simple ASCII wireframe for testing interface"
    }
  ],
  "designSystem": {
    "colorScheme": "Light|Dark|Auto",
    "typography": "Font choices and hierarchy",
    "spacing": "Spacing system (e.g., 8px grid)",
    "components": "Reusable component patterns"
  }
}

ASCII WIREFRAME RULES:
- Use box drawing characters: ┌ ┐ └ ┘ ├ ┤ ┬ ┴ ┼ ─ │
- Create clear visual layouts with proper spacing
- Use [Button], [Input], [Logo], [Menu], [Icon] for UI elements
- Make wireframes 60-80 characters wide
- Use \\n for line breaks within the JSON string
- Example format:
"asciiWireframe": "┌─────────────────────────────────────────────────────────────┐\\n│                        Header                               │\\n│  [Logo]                                    [User] [Menu]    │\\n├─────────────────────────────────────────────────────────────┤\\n│                                                             │\\n│  ┌─────────────────────────────────────────────────────┐    │\\n│  │                 Main Content                        │    │\\n│  │                                                     │    │\\n│  │  Primary Feature Area                               │    │\\n│  │                                                     │    │\\n│  │  [Action Button]                                    │    │\\n│  └─────────────────────────────────────────────────────┘    │\\n│                                                             │\\n└─────────────────────────────────────────────────────────────┘"

MERMAID DIAGRAM RULES:
- Use flowchart TD (top-down) format
- Include decision points with diamond shapes
- Show user interactions and system responses
- Example format:
"mermaidDiagram": "flowchart TD\\n    A[User lands on page] --> B{Authenticated?}\\n    B -->|Yes| C[Show dashboard]\\n    B -->|No| D[Show login form]\\n    D --> E[User enters credentials]\\n    E --> F{Valid credentials?}\\n    F -->|Yes| C\\n    F -->|No| G[Show error message]"

IMPORTANT:
- Always include testing pages for backend-only projects
- Create wireframes for ALL major user journeys
- Return only valid JSON with proper escaping
- Make wireframes specific to the project requirements
- Include both visual (ASCII) and logical (Mermaid) representations`;

    const contextPrompt = `Project: ${prompt}

Analysis: ${JSON.stringify(analysis)}

PRD: ${JSON.stringify(prd)}

Design comprehensive wireframes.`;

    const response = await this.makeAIRequest(contextPrompt, systemPrompt, { maxTokens: 6000 });

    if (!response.success) {
      throw new Error(response.error || 'Failed to design wireframes');
    }

    return this.parseJSONResponse(response.data);
  }

  /**
   * Plan file system structure
   */
  async planFileSystem(prompt: string, techStack: any, analysis: any, designContext?: string): Promise<any> {
    let systemPrompt = `You are a senior software architect expert at project structure. Plan a comprehensive file system structure with visual representations.

Return a JSON object with this structure:

{
  "overview": "Brief explanation of the file structure approach and architectural decisions",
  "projectType": "frontend|backend|fullstack|mobile|desktop|library",
  "structure": {
    "root": {
      "name": "project-root",
      "type": "folder",
      "description": "Root project directory",
      "children": [
        {
          "name": "folder-name",
          "type": "folder",
          "description": "What this folder contains and why",
          "importance": "critical|important|optional",
          "children": [
            {
              "name": "file.ext",
              "type": "file",
              "description": "What this file does",
              "language": "javascript|typescript|python|css|html|json|other",
              "purpose": "configuration|component|utility|test|documentation|other"
            }
          ]
        }
      ]
    }
  },
  "visualTree": "ASCII tree representation of the file structure",
  "mermaidDiagram": "Mermaid diagram showing folder relationships and dependencies",
  "keyFiles": [
    {
      "path": "relative/path/to/file.ext",
      "name": "file.ext",
      "purpose": "Detailed description of what this file does",
      "dependencies": ["dependency1", "dependency2"],
      "priority": "critical|high|medium|low",
      "complexity": "simple|moderate|complex",
      "estimatedLines": "Expected lines of code",
      "interfaces": ["interface1", "interface2"]
    }
  ],
  "conventions": {
    "naming": {
      "files": "File naming convention (e.g., kebab-case, camelCase)",
      "folders": "Folder naming convention",
      "components": "Component naming convention",
      "variables": "Variable naming convention"
    },
    "organization": {
      "principle": "How files are organized (feature-based, type-based, etc.)",
      "grouping": "How related files are grouped",
      "separation": "How concerns are separated"
    },
    "imports": {
      "style": "Import/export patterns and conventions",
      "paths": "Path resolution strategy (absolute, relative)",
      "barrels": "Use of index files for re-exports"
    }
  },
  "buildSystem": {
    "configFiles": [
      {
        "name": "config-file.json",
        "purpose": "What this config file controls",
        "critical": true
      }
    ],
    "scripts": {
      "dev": "Development command and what it does",
      "build": "Build command and output",
      "test": "Test command and coverage",
      "lint": "Linting command and rules",
      "deploy": "Deployment command and target"
    },
    "dependencies": {
      "runtime": ["prod dependency 1", "prod dependency 2"],
      "development": ["dev dependency 1", "dev dependency 2"],
      "optional": ["optional dependency 1"]
    }
  },
  "scalability": {
    "growth": "How the structure supports project growth",
    "modularity": "How modules can be added/removed",
    "maintenance": "How the structure supports maintenance"
  }
}

ASCII TREE RULES:
- Use tree characters: ├── └── │   ├─ └─
- Show folder hierarchy clearly
- Include key files in each folder
- Example format:
"visualTree": "project-root/\\n├── src/\\n│   ├── components/\\n│   │   ├── Header.tsx\\n│   │   └── Footer.tsx\\n│   ├── pages/\\n│   │   └── index.tsx\\n│   └── utils/\\n│       └── helpers.ts\\n├── public/\\n│   └── index.html\\n└── package.json"

MERMAID DIAGRAM RULES:
- Use graph TD (top-down) format
- Show folder dependencies and relationships
- Example format:
"mermaidDiagram": "graph TD\\n    A[src] --> B[components]\\n    A --> C[pages]\\n    A --> D[utils]\\n    B --> E[Header.tsx]\\n    B --> F[Footer.tsx]\\n    C --> G[index.tsx]\\n    D --> H[helpers.ts]"

IMPORTANT:
- Consider the specific technology stack
- Plan for scalability and maintainability
- Include testing and documentation structure
- Return only valid JSON with proper escaping`;

    // Add design context if available
    if (designContext) {
      systemPrompt += `

DESIGN CONTEXT: The user has provided reference images with the following extracted style guide:

${designContext}

Consider this design context when planning the file system structure. Include appropriate folders for assets, styles, components, and design-related files that align with the design requirements.`;
    }

    const contextPrompt = `Project: ${prompt}

Tech Stack: ${JSON.stringify(techStack)}

Analysis: ${JSON.stringify(analysis)}

Plan comprehensive file system structure.`;

    const response = await this.makeAIRequest(contextPrompt, systemPrompt);

    if (!response.success) {
      throw new Error(response.error || 'Failed to plan file system');
    }

    return this.parseJSONResponse(response.data);
  }

  /**
   * Define workflow logic
   */
  async defineWorkflow(prompt: string, analysis: any, prd: any, wireframes: any, designContext?: string): Promise<any> {
    let systemPrompt = `You are a systems architect expert at defining application workflows. Create comprehensive workflow definitions.

CRITICAL: Return ONLY the JSON object. Do not include any explanatory text, comments, or markdown formatting. Start your response directly with the opening brace {.

Return a JSON object with this structure:

{
  "userWorkflows": [
    {
      "name": "Workflow name",
      "description": "What this workflow accomplishes",
      "steps": [
        {
          "step": 1,
          "action": "User action",
          "system": "System response",
          "validation": "Validation rules",
          "errorHandling": "Error scenarios"
        }
      ],
      "triggers": ["trigger1", "trigger2", ...],
      "outcomes": ["outcome1", "outcome2", ...]
    }
  ],
  "systemWorkflows": [
    {
      "name": "System process name",
      "type": "background" | "scheduled" | "event-driven",
      "description": "What this process does",
      "steps": ["step1", "step2", ...],
      "dependencies": ["dependency1", "dependency2", ...]
    }
  ],
  "dataFlow": [
    {
      "source": "Data source",
      "destination": "Data destination",
      "transformation": "How data is transformed",
      "validation": "Validation rules"
    }
  ],
  "integrations": [
    {
      "service": "External service",
      "purpose": "Why we integrate",
      "dataExchange": "What data is exchanged",
      "errorHandling": "How errors are handled"
    }
  ]
}

Be specific about business logic and technical implementation.

CRITICAL: Return ONLY the JSON object. Do not include any explanatory text, comments, or markdown formatting. Start your response directly with the opening brace {.`;

    // Add design context if available
    if (designContext) {
      systemPrompt += `

DESIGN CONTEXT: The user has provided reference images with the following extracted style guide:

${designContext}

Consider this design context when defining workflows. Ensure user workflows align with the design patterns and UI components specified in the style guide.`;
    }

    const contextPrompt = `Project: ${prompt}

Analysis: ${JSON.stringify(analysis)}

PRD: ${JSON.stringify(prd)}

Wireframes: ${JSON.stringify(wireframes)}

Define comprehensive workflows. Return only valid JSON.`;

    const response = await this.makeAIRequest(contextPrompt, systemPrompt, { maxTokens: 6000 });

    if (!response.success) {
      throw new Error(response.error || 'Failed to define workflow');
    }

    return this.parseJSONResponse(response.data);
  }

  /**
   * Design database schema
   */
  async designDatabaseSchema(prompt: string, analysis: any, prd: any, techStack: any): Promise<any> {
    const systemPrompt = `You are a senior database architect expert at designing scalable database schemas. Create a comprehensive database schema design with visual ERD diagrams.

Return a JSON object with this structure:

{
  "overview": "Brief explanation of the database design approach and key architectural decisions",
  "databaseType": "relational|document|graph|hybrid",
  "databaseEngine": "PostgreSQL|MySQL|MongoDB|Neo4j|SQLite|other",
  "tables": [
    {
      "name": "table_name",
      "purpose": "What this table stores and why it's important",
      "category": "core|lookup|audit|cache|other",
      "estimatedRows": "Expected number of rows (e.g., 1K, 100K, 1M+)",
      "columns": [
        {
          "name": "column_name",
          "type": "VARCHAR(255)|INTEGER|BIGINT|BOOLEAN|TIMESTAMP|TEXT|JSON|UUID|DECIMAL|other",
          "constraints": ["PRIMARY KEY", "NOT NULL", "UNIQUE", "FOREIGN KEY", "CHECK", "DEFAULT"],
          "description": "What this column represents and business rules",
          "nullable": true,
          "defaultValue": "default value if any",
          "businessRules": ["rule1", "rule2"]
        }
      ],
      "indexes": [
        {
          "name": "index_name",
          "columns": ["column1", "column2"],
          "type": "btree|hash|gin|gist|unique|partial",
          "purpose": "Why this index is needed for performance",
          "estimatedUsage": "How often this index will be used"
        }
      ],
      "relationships": [
        {
          "type": "one-to-many|many-to-many|one-to-one",
          "relatedTable": "related_table_name",
          "foreignKey": "foreign_key_column",
          "referencedColumn": "referenced_column",
          "onDelete": "CASCADE|SET NULL|RESTRICT|NO ACTION",
          "onUpdate": "CASCADE|SET NULL|RESTRICT|NO ACTION",
          "description": "Business relationship description"
        }
      ],
      "triggers": [
        {
          "name": "trigger_name",
          "event": "INSERT|UPDATE|DELETE",
          "purpose": "What this trigger does",
          "logic": "Brief description of trigger logic"
        }
      ]
    }
  ],
  "views": [
    {
      "name": "view_name",
      "purpose": "What business need this view serves",
      "type": "materialized|standard",
      "query": "SQL query or detailed description",
      "tables": ["table1", "table2"],
      "refreshStrategy": "real-time|scheduled|manual",
      "performance": "Expected performance characteristics"
    }
  ],
  "erdDiagram": "Mermaid ERD diagram showing all tables and relationships",
  "migrations": [
    {
      "version": "001",
      "description": "Initial schema creation",
      "operations": ["CREATE TABLE users", "CREATE INDEX idx_users_email"],
      "rollback": ["DROP INDEX idx_users_email", "DROP TABLE users"],
      "estimatedTime": "Expected migration time"
    }
  ],
  "seedData": [
    {
      "table": "table_name",
      "description": "What seed data is needed and why",
      "priority": "critical|important|optional",
      "examples": [
        {
          "description": "Example record description",
          "data": {"column1": "value1", "column2": "value2"}
        }
      ]
    }
  ],
  "performance": {
    "considerations": ["Performance consideration with explanation"],
    "optimizations": ["Specific optimization strategy"],
    "scalingStrategy": "Detailed scaling approach (sharding, read replicas, etc.)",
    "bottlenecks": ["Potential bottleneck and mitigation"],
    "monitoring": ["Key metrics to monitor"]
  },
  "security": {
    "authentication": "How database users are authenticated",
    "authorization": "How permissions and roles are managed",
    "dataProtection": ["Encryption at rest", "Encryption in transit", "PII handling"],
    "compliance": ["GDPR", "HIPAA", "SOC2"],
    "auditTrail": "How data changes are tracked",
    "backupStrategy": "Backup and recovery approach"
  },
  "dataGovernance": {
    "dataRetention": "How long data is kept",
    "archivalStrategy": "How old data is archived",
    "dataQuality": ["Data validation rules", "Consistency checks"],
    "masterData": "How master data is managed"
  }
}

ERD DIAGRAM RULES:
- Use Mermaid erDiagram syntax
- Include all tables and relationships
- Show cardinality (||--o{, ||--||, }o--||, etc.)
- Example format:
"erdDiagram": "erDiagram\\n    CUSTOMER ||--o{ ORDER : places\\n    ORDER ||--|{ LINE-ITEM : contains\\n    CUSTOMER {\\n        string name\\n        string email\\n        string phone\\n    }\\n    ORDER {\\n        int order_id\\n        date order_date\\n        decimal total\\n    }"

IMPORTANT:
- Design for the specific technology stack provided
- Consider scalability from day one
- Include comprehensive indexing strategy
- Plan for data growth and performance
- Return only valid JSON with proper escaping`;

    const contextPrompt = `Project: ${prompt}

Analysis: ${JSON.stringify(analysis)}

PRD: ${JSON.stringify(prd)}

Tech Stack: ${JSON.stringify(techStack)}

Design a comprehensive database schema. Return only valid JSON.`;

    const response = await this.makeAIRequest(contextPrompt, systemPrompt, { maxTokens: 6000 });

    if (!response.success) {
      throw new Error(response.error || 'Failed to design database schema');
    }

    return this.parseJSONResponse(response.data);
  }

  /**
   * Generate project scaffold
   */
  async generateProjectScaffold(prompt: string, analysis: any, techStack: any, filesystem: any, database: any): Promise<any> {
    const systemPrompt = `You are a senior software engineer expert at project scaffolding and code generation. Generate a comprehensive project scaffold with actual, production-ready code files.

Return a JSON object with this structure:

{
  "overview": "Brief explanation of the scaffold approach and architectural decisions",
  "projectMetadata": {
    "name": "project-name",
    "version": "1.0.0",
    "description": "Project description",
    "author": "Generated by AI Planning Agent",
    "license": "MIT"
  },
  "codeFiles": [
    {
      "path": "relative/path/to/file.ext",
      "name": "filename.ext",
      "type": "configuration|component|utility|test|documentation|entry|api|model|other",
      "language": "javascript|typescript|python|css|html|json|yaml|sql|other",
      "content": "Complete, working file content with proper syntax",
      "description": "What this file does and why it's important",
      "dependencies": ["dependency1", "dependency2"],
      "exports": ["export1", "export2"],
      "imports": ["import1", "import2"],
      "complexity": "simple|moderate|complex",
      "estimatedLines": 50,
      "keyFeatures": ["feature1", "feature2"]
    }
  ],
  "folderStructure": {
    "name": "project-root",
    "type": "folder",
    "children": [
      {
        "name": "src",
        "type": "folder",
        "description": "Source code directory",
        "children": [
          {
            "name": "components",
            "type": "folder",
            "description": "Reusable UI components"
          }
        ]
      }
    ]
  },
  "setupInstructions": [
    {
      "step": 1,
      "category": "installation",
      "title": "Install Dependencies",
      "command": "npm install",
      "description": "Install all required packages and dependencies",
      "estimatedTime": "2-3 minutes",
      "troubleshooting": "If installation fails, try clearing npm cache"
    }
  ],
  "environmentSetup": {
    "envVariables": [
      {
        "name": "DATABASE_URL",
        "description": "Database connection string",
        "example": "postgresql://user:pass@localhost:5432/dbname",
        "required": true,
        "category": "database",
        "validation": "Must be a valid database URL"
      }
    ],
    "configFiles": [
      {
        "name": ".env.example",
        "content": "# Database Configuration\\nDATABASE_URL=your_database_url_here\\n\\n# API Configuration\\nAPI_PORT=3000\\nAPI_HOST=localhost",
        "description": "Environment variables template with examples"
      }
    ],
    "systemRequirements": {
      "node": ">=18.0.0",
      "npm": ">=8.0.0",
      "database": "PostgreSQL 14+",
      "os": "Windows, macOS, Linux"
    }
  },
  "scripts": {
    "development": [
      {
        "name": "dev",
        "command": "npm run dev",
        "description": "Start development server with hot reload",
        "port": 3000,
        "features": ["hot reload", "source maps", "error overlay"]
      }
    ],
    "production": [
      {
        "name": "build",
        "command": "npm run build",
        "description": "Build optimized production bundle",
        "output": "dist/ or build/ directory",
        "optimizations": ["minification", "tree shaking", "code splitting"]
      }
    ],
    "testing": [
      {
        "name": "test",
        "command": "npm test",
        "description": "Run unit and integration tests",
        "coverage": true,
        "framework": "Jest, Vitest, or similar"
      }
    ],
    "maintenance": [
      {
        "name": "lint",
        "command": "npm run lint",
        "description": "Check code quality and style",
        "autofix": true
      }
    ]
  },
  "documentation": {
    "readme": "# Project Name\\n\\nComplete README.md content with:\\n- Project description\\n- Installation instructions\\n- Usage examples\\n- API documentation\\n- Contributing guidelines",
    "apiDocs": "Comprehensive API documentation with endpoints, parameters, and examples",
    "deploymentGuide": "Step-by-step deployment instructions for various platforms",
    "developmentGuide": "Guidelines for local development and contribution"
  },
  "testing": {
    "strategy": "Testing approach and philosophy",
    "frameworks": ["Jest", "React Testing Library", "Cypress"],
    "coverage": "Expected test coverage percentage",
    "testFiles": [
      {
        "path": "src/__tests__/example.test.js",
        "content": "Complete test file content with examples",
        "description": "What this test file covers"
      }
    ]
  },
  "deployment": {
    "platforms": ["Vercel", "Netlify", "AWS", "Docker"],
    "buildCommand": "npm run build",
    "outputDirectory": "dist",
    "environmentVariables": ["DATABASE_URL", "API_KEY"],
    "healthCheck": "/api/health endpoint or similar"
  },
  "nextSteps": [
    {
      "step": "Configure environment variables",
      "description": "Set up .env file with required variables",
      "priority": "critical",
      "estimatedTime": "5 minutes"
    }
  ],
  "bestPractices": {
    "codeQuality": ["ESLint configuration", "Prettier formatting", "TypeScript strict mode"],
    "security": ["Environment variable validation", "Input sanitization", "CORS configuration"],
    "performance": ["Code splitting", "Lazy loading", "Caching strategies"],
    "maintenance": ["Automated testing", "CI/CD pipeline", "Dependency updates"]
  }
}

CODE GENERATION RULES:
- Generate complete, syntactically correct code
- Include proper error handling and validation
- Use modern best practices and patterns
- Include comprehensive comments and documentation
- Make code immediately runnable after setup
- Follow the specific technology stack conventions
- Include realistic example data and use cases

IMPORTANT:
- Generate at least 8-12 key code files
- Include configuration, components, utilities, and tests
- Use the chosen technology stack consistently
- Make the scaffold production-ready, not just a demo
- Include proper TypeScript types if applicable
- Return only valid JSON with proper escaping`;

    const contextPrompt = `Project: ${prompt}

Analysis: ${JSON.stringify(analysis)}

Tech Stack: ${JSON.stringify(techStack)}

File System: ${JSON.stringify(filesystem)}

Database: ${JSON.stringify(database)}

Generate a complete, working project scaffold with actual code files. Return only valid JSON.`;

    const response = await this.makeAIRequest(contextPrompt, systemPrompt, { maxTokens: 8000 });

    if (!response.success) {
      throw new Error(response.error || 'Failed to generate project scaffold');
    }

    return this.parseJSONResponse(response.data);
  }

  /**
   * Break down into implementation tasks
   */
  async breakdownTasks(prompt: string, allPreviousResults: any, designContext?: string): Promise<any> {
    let systemPrompt = `You are a senior project manager expert at breaking down projects into actionable tasks. Create a comprehensive task breakdown.

CRITICAL: Return ONLY the JSON object. Do not include any explanatory text, comments, or markdown formatting. Start your response directly with the opening brace {.

Return a JSON object with this structure:

{
  "phases": [
    {
      "name": "Phase name",
      "description": "What this phase accomplishes",
      "duration": "X weeks",
      "tasks": ["task_id1", "task_id2", ...]
    }
  ],
  "tasks": [
    {
      "id": "unique_task_id",
      "title": "Clear, actionable task title",
      "description": "Detailed task description",
      "category": "setup" | "frontend" | "backend" | "testing" | "deployment" | "documentation",
      "priority": "high" | "medium" | "low",
      "estimatedHours": 4,
      "dependencies": ["task_id1", "task_id2", ...],
      "skills": ["skill1", "skill2", ...],
      "deliverables": ["deliverable1", "deliverable2", ...],
      "acceptanceCriteria": ["criteria1", "criteria2", ...]
    }
  ],
  "milestones": [
    {
      "name": "Milestone name",
      "description": "What this milestone represents",
      "tasks": ["task_id1", "task_id2", ...],
      "deadline": "Week X"
    }
  ],
  "resources": {
    "teamSize": "Recommended team size",
    "roles": ["role1", "role2", ...],
    "tools": ["tool1", "tool2", ...]
  },
  "estimates": {
    "totalHours": 200,
    "totalWeeks": 8,
    "confidence": "high" | "medium" | "low"
  }
}

Create 20-40 specific, actionable tasks with realistic estimates.`;

    // Add design context if available
    if (designContext) {
      systemPrompt += `

DESIGN CONTEXT: The user has provided reference images with the following extracted style guide:

${designContext}

Consider this design context when breaking down tasks. Include specific tasks for implementing the design system, creating styled components, and ensuring the final implementation matches the provided design references.`;
    }

    const contextPrompt = `Project: ${prompt}

All Previous Results: ${JSON.stringify(allPreviousResults)}

Break down into comprehensive implementation tasks.`;

    const response = await this.makeAIRequest(contextPrompt, systemPrompt, { maxTokens: 8000 });

    if (!response.success) {
      throw new Error(response.error || 'Failed to breakdown tasks');
    }

    return this.parseJSONResponse(response.data);
  }

  /**
   * Generate context profile for AI agents
   */
  async generateContextProfile(prompt: string, analysis: any, summary: any, prd: any): Promise<any> {
    const systemPrompt = `You are an AI systems architect expert at creating comprehensive context profiles for AI agents. Create a detailed context profile template.

CRITICAL: Return ONLY the JSON object. Do not include any explanatory text, comments, or markdown formatting. Start your response directly with the opening brace {.

Return a JSON object with this exact structure:

{
  "profile_id": "agent-[domain]-[role]-v1.0.0",
  "identity": {
    "name": "[Agent Name based on project purpose]",
    "role": "[Specific Role based on project type]",
    "organization": "[Organization/Domain from project context]",
    "timezone": "UTC",
    "language": "en-US"
  },
  "goals": {
    "short_term": ["specific short-term goal 1", "specific short-term goal 2"],
    "long_term": ["specific long-term goal 1", "specific long-term goal 2"]
  },
  "preferences": {
    "communication_style": "[professional/casual/technical/friendly]",
    "response_format": "[structured_text/json/dashboard/report]",
    "tone": "[professional/helpful/authoritative/conversational]",
    "visuals": true,
    "default_output_type": "[text_response/structured_data/visual_report]"
  },
  "capabilities": {
    "tools_enabled": ["tool1", "tool2", "tool3"],
    "environment": {
      "platform": "[Cloud-based/On-premise/Hybrid]",
      "extensions": ["extension1", "extension2"]
    }
  },
  "memory": {
    "scope": "[conversation/session/persistent/company-specific]",
    "persistence": "[session/temporary/persistent]",
    "structure": "[contextual/relational_db/vector_db]",
    "data_points": ["data_point1", "data_point2", "data_point3"]
  },
  "constraints": {
    "rate_limit": "[number] requests/hour",
    "budget": {
      "monthly": 250,
      "used": 0
    },
    "operational_constraints": ["constraint1", "constraint2"]
  },
  "behavioral_flags": {
    "debug_mode": false,
    "auto_summarize": true,
    "use_context_window": true,
    "custom_flag_1": false,
    "custom_flag_2": true
  },
  "metadata": {
    "created_at": "[current ISO timestamp]",
    "last_updated": "[current ISO timestamp]",
    "version": "1.0.0"
  }
}

Base the profile on the project analysis and create specific, relevant values for each field.`;

    const contextPrompt = `Project: ${prompt}

Analysis: ${JSON.stringify(analysis)}

Summary: ${JSON.stringify(summary)}

PRD: ${JSON.stringify(prd)}

Generate a comprehensive context profile template for this AI agent project.`;

    const response = await this.makeAIRequest(contextPrompt, systemPrompt);

    if (!response.success) {
      throw new Error(response.error || 'Failed to generate context profile');
    }

    return this.parseJSONResponse(response.data);
  }
}

// Export singleton instance
export const aiService = new AIService();
