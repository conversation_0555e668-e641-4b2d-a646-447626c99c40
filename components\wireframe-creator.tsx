"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Layout, Smartphone, Monitor } from "lucide-react"
import type { ModuleProps } from "@/types/planning"

interface Wireframe {
  name: string
  type: "desktop" | "mobile"
  ascii: string
}

export function WireframeCreator({ context, onComplete }: ModuleProps) {
  const [wireframes, setWireframes] = useState<Wireframe[]>([])
  const [isGenerating, setIsGenerating] = useState(true)

  useEffect(() => {
    generateWireframes()
  }, [])

  const generateWireframes = async () => {
    setIsGenerating(true)

    try {
      // This component now receives AI-generated wireframe data from the planning agent
      const wireframeData = context?.results?.wireframes || context?.wireframes || {}

      if (Object.keys(wireframeData).length === 0) {
        // If no wireframe data available, show error
        setWireframes([{
          id: "error",
          title: "Error",
          description: "No wireframe data available. Please ensure the AI planning process completed successfully.",
          mockup: "AI service unavailable"
        }])
      } else {
        // Convert AI wireframe data to component format
        const wireframeList = wireframeData.pages?.map((page: any, index: number) => ({
          id: `page-${index}`,
          title: page.name || `Page ${index + 1}`,
          description: page.purpose || page.description || "AI-generated wireframe",
          mockup: page.wireframe || page.mockup || "Wireframe content generated by AI"
        })) || [{
          id: "default",
          title: "Main Interface",
          description: "AI-generated wireframe design",
          mockup: wireframeData.mockups || "Wireframe generated by AI planning agent"
        }]

        setWireframes(wireframeList)
      }
    } catch (error) {
      console.error("Wireframe display failed:", error)
      setWireframes([{
        id: "error",
        title: "Error",
        description: "Unable to display wireframes. Please check AI service configuration.",
        mockup: "AI service error"
      }])
    }

    setIsGenerating(false)
  }

  const createWireframes = (): Wireframe[] => {
    const { clarifications, features } = context || {}
    const needsAuth = clarifications?.authentication?.includes("Yes")
    const isMobile = clarifications?.platform?.includes("mobile")

    const wireframes: Wireframe[] = []

    // Main page wireframe
    wireframes.push({
      name: "Main Page",
      type: "desktop",
      ascii: `
┌─────────────────────────────────────────────────────────────┐
│                        Header                               │
│  [Logo]                                    [User] [Menu]    │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────────────────────────────────────────┐    │
│  │                 Main Content                        │    │
│  │                                                     │    │
│  │  ${features?.[0] || "Primary Feature Area"}                                │    │
│  │                                                     │    │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  │    │
│  │  │   Action    │  │   Action    │  │   Action    │  │    │
│  │  │   Button    │  │   Button    │  │   Button    │  │    │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  │    │
│  │                                                     │    │
│  │  ┌─────────────────────────────────────────────┐    │    │
│  │  │            Data Display Area                │    │    │
│  │  │                                             │    │    │
│  │  │  • Item 1                                   │    │    │
│  │  │  • Item 2                                   │    │    │
│  │  │  • Item 3                                   │    │    │
│  │  └─────────────────────────────────────────────┘    │    │
│  └─────────────────────────────────────────────────────┘    │
│                                                             │
├─────────────────────────────────────────────────────────────┤
│                        Footer                               │
│              [Links] [Contact] [About]                      │
└─────────────────────────────────────────────────────────────┘`,
    })

    if (needsAuth) {
      wireframes.push({
        name: "Login Page",
        type: "desktop",
        ascii: `
┌─────────────────────────────────────────────────────────────┐
│                        Header                               │
│  [Logo]                                              [Home] │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│                                                             │
│              ┌─────────────────────────────┐                │
│              │         Login Form          │                │
│              │                             │                │
│              │  Email:    [____________]   │                │
│              │                             │                │
│              │  Password: [____________]   │                │
│              │                             │                │
│              │  [ ] Remember me            │                │
│              │                             │                │
│              │     [Login Button]          │                │
│              │                             │                │
│              │  [Forgot Password?]         │                │
│              │  [Create Account]           │                │
│              └─────────────────────────────┘                │
│                                                             │
│                                                             │
├─────────────────────────────────────────────────────────────┤
│                        Footer                               │
└─────────────────────────────────────────────────────────────┘`,
      })
    }

    if (isMobile) {
      wireframes.push({
        name: "Mobile Main",
        type: "mobile",
        ascii: `
┌─────────────────┐
│ [☰]  App  [👤] │
├─────────────────┤
│                 │
│  ${features?.[0]?.substring(0, 15) || "Main Feature"}   │
│                 │
│ ┌─────────────┐ │
│ │   Action    │ │
│ │   Button    │ │
│ └─────────────┘ │
│                 │
│ ┌─────────────┐ │
│ │ Data Item 1 │ │
│ └─────────────┘ │
│ ┌─────────────┐ │
│ │ Data Item 2 │ │
│ └─────────────┘ │
│ ┌─────────────┐ │
│ │ Data Item 3 │ │
│ └─────────────┘ │
│                 │
│     [+ Add]     │
│                 │
├─────────────────┤
│ [Home] [Search] │
│ [Profile] [More]│
└─────────────────┘`,
      })
    }

    return wireframes
  }

  const handleContinue = () => {
    onComplete(wireframes)
  }

  return (
    <Card className="border-slate-700" style={{backgroundColor: '#818181'}}>
      <CardHeader>
        <div className="flex items-center gap-2">
          <Layout className="w-5 h-5 text-purple-400" />
          <CardTitle className="text-white">UI Wireframes</CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        {isGenerating ? (
          <div className="flex items-center justify-center py-12">
            <div className="flex items-center gap-3">
              <Layout className="w-5 h-5 text-purple-400 animate-pulse" />
              <span className="text-gray-300">Creating wireframes for your application...</span>
            </div>
          </div>
        ) : (
          <>
            <Tabs defaultValue="0" className="w-full">
              <TabsList className="grid w-full grid-cols-1 md:grid-cols-3 bg-slate-700">
                {wireframes.map((wireframe, index) => (
                  <TabsTrigger key={index} value={index.toString()} className="text-xs">
                    {wireframe.type === "mobile" ? (
                      <Smartphone className="w-4 h-4 mr-1" />
                    ) : (
                      <Monitor className="w-4 h-4 mr-1" />
                    )}
                    {wireframe.name}
                  </TabsTrigger>
                ))}
              </TabsList>

              {wireframes.map((wireframe, index) => (
                <TabsContent key={index} value={index.toString()} className="mt-4">
                  <div className="bg-slate-900 p-4 rounded-lg overflow-x-auto">
                    <pre className="text-green-400 font-mono text-xs leading-tight">{wireframe.ascii}</pre>
                  </div>
                </TabsContent>
              ))}
            </Tabs>

            <Button onClick={handleContinue} className="w-full mt-6 bg-purple-600 hover:bg-purple-700">
              Approve Wireframes & Continue
            </Button>
          </>
        )}
      </CardContent>
    </Card>
  )
}
