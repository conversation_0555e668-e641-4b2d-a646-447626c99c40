/**
 * Planning Progress API
 * Real-time progress tracking for planning sessions
 */

import { type NextRequest, NextResponse } from "next/server"
import { getProgressStatus, subscribeToProgress } from "@/lib/progress-tracker"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const sessionId = searchParams.get('sessionId')

    if (!sessionId) {
      return NextResponse.json({ error: "Session ID is required" }, { status: 400 })
    }

    const status = getProgressStatus(sessionId)
    
    if (!status) {
      return NextResponse.json({ error: "Session not found" }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      ...status
    })

  } catch (error) {
    console.error("Progress API error:", error)
    return NextResponse.json(
      { error: "Failed to get progress status" },
      { status: 500 }
    )
  }
}

// Server-Sent Events for real-time updates
export async function POST(request: NextRequest) {
  try {
    const { sessionId } = await request.json()

    if (!sessionId) {
      return NextResponse.json({ error: "Session ID is required" }, { status: 400 })
    }

    // For now, return the current status
    // In a full implementation, this would set up SSE
    const status = getProgressStatus(sessionId)
    
    return NextResponse.json({
      success: true,
      message: "Progress tracking enabled",
      status
    })

  } catch (error) {
    console.error("Progress subscription error:", error)
    return NextResponse.json(
      { error: "Failed to subscribe to progress" },
      { status: 500 }
    )
  }
}
