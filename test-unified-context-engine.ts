/**
 * Test script for Unified Context Engine
 * Verifies that the unified context engine works correctly
 */

import { createUnifiedEngine } from './lib/context-engine-migration'
import { testConfig } from './lib/unified-context-config'
import { UnifiedContextEngine } from './lib/unified-context-engine'

async function testUnifiedContextEngine() {
  console.log('🧪 Testing Unified Context Engine...\n')

  try {
    // Test 1: Create unified engine
    console.log('1. Creating unified context engine...')
    const engine = new UnifiedContextEngine({
      originalPrompt: "Build a modern React dashboard with real-time data visualization",
      projectType: "Web Application",
      features: ["Dashboard", "Real-time Updates", "Data Visualization", "User Authentication"],
      techStack: { 
        frontend: { framework: "React", language: "TypeScript" },
        backend: { framework: "Node.js", language: "TypeScript" },
        database: "PostgreSQL"
      }
    }, testConfig)

    // Wait for initialization
    await new Promise((resolve, reject) => {
      engine.once('initialized', resolve)
      engine.once('error', reject)
      setTimeout(() => reject(new Error('Initialization timeout')), 10000)
    })
    
    console.log('✅ Unified context engine created successfully')

    // Test 2: Register agents
    console.log('\n2. Registering agents...')
    const plannerAgentId = engine.registerAgent('project-planner', 'test-planning', {
      requiredCapabilities: ['requirements_analysis', 'tech_stack_selection'],
      contextFilters: ['all']
    })
    
    const taskAgentId = engine.registerAgent('task-breakdown', 'test-tasks', {
      requiredCapabilities: ['task_breakdown', 'dependency_analysis'],
      contextFilters: ['requirements', 'architecture']
    })
    
    console.log(`✅ Registered planner agent: ${plannerAgentId}`)
    console.log(`✅ Registered task agent: ${taskAgentId}`)

    // Test 3: Update context
    console.log('\n3. Updating context...')
    engine.updateContext('analyze', {
      projectType: 'Web Application',
      complexity: 'Medium',
      domain: 'Data Visualization',
      requirements: ['Real-time updates', 'Interactive charts', 'User management']
    })
    
    engine.updateContext('techstack', {
      frontend: { framework: 'React', version: '18.x', language: 'TypeScript' },
      backend: { framework: 'Express', version: '4.x', language: 'TypeScript' },
      database: { type: 'PostgreSQL', version: '15.x' },
      realtime: { technology: 'WebSocket', library: 'Socket.io' }
    })
    
    console.log('✅ Context updated successfully')

    // Test 4: Get agent context
    console.log('\n4. Getting agent context...')
    const plannerContext = await engine.getAgentContext(plannerAgentId)
    const taskContext = await engine.getAgentContext(taskAgentId)
    
    console.log('✅ Planner context retrieved:', {
      hasCurrentContext: !!plannerContext.current,
      hasDependencies: !!plannerContext.dependencies,
      hasMetadata: !!plannerContext.metadata,
      agentType: plannerContext.agentMetadata?.agentType
    })
    
    console.log('✅ Task context retrieved:', {
      hasCurrentContext: !!taskContext.current,
      hasDependencies: !!taskContext.dependencies,
      hasMetadata: !!taskContext.metadata,
      agentType: taskContext.agentMetadata?.agentType
    })

    // Test 5: Enhanced context with RAG
    console.log('\n5. Testing enhanced context with RAG...')
    const enhancedContext = await engine.enhanceWithRAG('techstack', 'React dashboard with TypeScript')
    
    console.log('✅ Enhanced context retrieved:', {
      hasBaseContext: !!enhancedContext.context,
      hasEnrichments: !!enhancedContext.enrichments,
      hasExternalKnowledge: !!enhancedContext.externalKnowledge,
      hasBestPractices: !!enhancedContext.bestPractices,
      hasTemplates: !!enhancedContext.templates
    })

    // Test 6: Sequential thinking
    console.log('\n6. Testing sequential thinking...')
    const thought = await engine.performSequentialThinking(
      'How should we structure the React dashboard for optimal performance?',
      { thoughtNumber: 1, totalThoughts: 3 }
    )
    
    console.log('✅ Sequential thinking result:', {
      hasThought: !!thought,
      thoughtNumber: thought?.thoughtNumber,
      nextThoughtNeeded: thought?.nextThoughtNeeded
    })

    // Test 7: Documentation retrieval
    console.log('\n7. Testing documentation retrieval...')
    const reactDocs = await engine.getDocumentation('React', 'hooks')
    
    console.log('✅ Documentation retrieved:', {
      hasDocumentation: !!reactDocs,
      libraryId: reactDocs?.libraryId,
      topic: reactDocs?.topic
    })

    // Test 8: Context validation
    console.log('\n8. Testing context validation...')
    const validation = engine.validateContext()
    
    console.log('✅ Context validation:', {
      isValid: validation.isValid,
      score: validation.score,
      issueCount: validation.issues.length,
      recommendationCount: validation.recommendations.length
    })

    // Test 9: Simulate codebase processing (without actual files)
    console.log('\n9. Testing codebase processing simulation...')
    try {
      // This will fail gracefully since we don't have a real project path
      await engine.processCodebase('./test-project')
    } catch (error) {
      console.log('⚠️  Codebase processing failed as expected (no test project):', error.message)
    }

    // Test 10: Query capabilities
    console.log('\n10. Testing query capabilities...')
    try {
      const queryResult = await engine.queryCodebase('find all functions')
      console.log('✅ Query executed:', { resultCount: queryResult?.count || 0 })
    } catch (error) {
      console.log('⚠️  Query failed as expected (no codebase loaded):', error.message)
    }

    // Test 11: Cleanup
    console.log('\n11. Testing cleanup...')
    await engine.cleanup()
    console.log('✅ Engine cleanup completed')

    console.log('\n🎉 All tests completed successfully!')
    console.log('\n📊 Test Summary:')
    console.log('- ✅ Engine initialization')
    console.log('- ✅ Agent registration')
    console.log('- ✅ Context updates')
    console.log('- ✅ Agent context retrieval')
    console.log('- ✅ Enhanced context with RAG')
    console.log('- ✅ Sequential thinking')
    console.log('- ✅ Documentation retrieval')
    console.log('- ✅ Context validation')
    console.log('- ⚠️  Codebase processing (no test data)')
    console.log('- ⚠️  Query capabilities (no codebase)')
    console.log('- ✅ Cleanup')

  } catch (error) {
    console.error('❌ Test failed:', error.message)
    console.error(error.stack)
    process.exit(1)
  }
}

// Test with migration
async function testMigration() {
  console.log('\n🔄 Testing migration from old context engine...')
  
  try {
    // Create using migration helper
    const engine = await createUnifiedEngine({
      originalPrompt: "Test migration functionality",
      projectType: "Test Project",
      features: ["Migration Test"]
    })
    
    console.log('✅ Migration-based engine creation successful')
    
    // Test basic functionality
    const agentId = engine.registerAgent('planner', 'migration-test')
    const context = await engine.getAgentContext(agentId)
    
    console.log('✅ Migration engine functionality verified')
    
    await engine.cleanup()
    console.log('✅ Migration test cleanup completed')
    
  } catch (error) {
    console.error('❌ Migration test failed:', error.message)
  }
}

// Run tests
async function runAllTests() {
  await testUnifiedContextEngine()
  await testMigration()
  
  console.log('\n🏁 All tests completed!')
}

// Execute if run directly
if (require.main === module) {
  runAllTests().catch(console.error)
}

export { testUnifiedContextEngine, testMigration, runAllTests }
