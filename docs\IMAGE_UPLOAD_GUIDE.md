# Image Upload Feature Guide

The Planning Agent now supports uploading design reference images to automatically generate comprehensive style guides using AI analysis.

## How to Use

### 1. Upload Images
- Click the **paperclip icon** (📎) in the input field
- Select one or more design reference images (max 5 images, 10MB each)
- Supported formats: JPG, PNG, GIF, WebP, SVG
- Images will appear as thumbnails with hover effects showing filenames

### 2. AI Analysis
- When you start planning, uploaded images are automatically sent to Google Gemini 2.5 Pro
- The AI analyzes your images and extracts:
  - **Color palettes** with exact hex codes and RGB values
  - **Typography** specifications (fonts, weights, sizes, spacing)
  - **Layout patterns** and spacing systems
  - **Component styling** details
  - **Visual effects** and design principles
  - **Implementation notes** for developers

### 3. Style Guide Generation
- A comprehensive style guide is generated and replaces the regular "Create design guidelines" step
- The style guide is formatted as a professional document with markdown formatting
- Results are clearly marked as "Generated from Reference Images"

## Features

### Image Management
- **Preview**: Hover over thumbnails to see filenames and remove buttons
- **Remove Individual**: Click the X button on any image to remove it
- **Clear All**: Use the "Clear all" button to remove all images at once
- **Validation**: Automatic file type and size validation with helpful error messages

### Visual Indicators
- **Processing State**: Loading spinner and dimmed images during AI analysis
- **Task Status**: Design task shows "Analyze uploaded design references" when images are present
- **Success Indicator**: Green checkmark when comprehensive style guide is generated

### Error Handling
- Graceful fallback if image analysis fails
- Detailed error messages for troubleshooting
- Continues with regular planning if design agent is unavailable

## Best Practices

### Image Selection
- **High Quality**: Use high-resolution images for better analysis
- **Multiple Views**: Include different pages/screens for comprehensive analysis
- **Clear Examples**: Choose images that clearly show the design elements you want to replicate
- **Consistent Style**: Upload images from the same design system for coherent results

### File Organization
- Name your files descriptively (e.g., "homepage-hero.png", "navigation-menu.jpg")
- Keep file sizes reasonable (under 5MB recommended for faster processing)
- Use common image formats for best compatibility

## Technical Details

### AI Models Used
- **Google Gemini 2.5 Pro**: For image analysis and style guide generation
- **Claude 3.5 Sonnet**: For overall project planning and coordination

### API Integration
- Images are processed through OpenRouter API
- Base64 encoding for secure image transmission
- Comprehensive error handling and retry logic

### Output Format
The generated style guide includes:

1. **Color Palette Section**
   - Primary, secondary, and accent colors
   - Exact hex codes and RGB values
   - Usage context for each color

2. **Typography Section**
   - Font families and weights
   - Size hierarchy and spacing
   - Line heights and letter spacing

3. **Layout & Spacing**
   - Grid systems and breakpoints
   - Margin and padding patterns
   - Container specifications

4. **Components**
   - Button styles and states
   - Card designs and shadows
   - Navigation elements
   - Form styling

5. **Visual Style**
   - Overall aesthetic description
   - Visual effects and animations
   - Border radius patterns

6. **Implementation Notes**
   - CSS custom properties
   - Framework recommendations
   - Responsive behavior

## Troubleshooting

### Common Issues

**Images not uploading:**
- Check file size (max 10MB per image)
- Verify file format is supported
- Ensure stable internet connection

**Analysis fails:**
- Check OpenRouter API key configuration
- Verify sufficient API credits
- Try with fewer or smaller images

**Style guide not generated:**
- Check browser console for error messages
- Ensure images contain clear design elements
- Try uploading different reference images

### Error Messages

- **"File too large"**: Reduce image size or use compression
- **"Invalid file type"**: Use supported image formats (JPG, PNG, etc.)
- **"Maximum 5 images allowed"**: Remove some images before adding more
- **"API key not configured"**: Check environment variables
- **"Rate limit exceeded"**: Wait and try again later

## Examples

### Good Reference Images
- Website homepages with clear branding
- Mobile app screens with consistent styling
- Design system documentation pages
- UI component libraries
- Brand style guides

### What Gets Analyzed
- Color schemes and palettes
- Button styles and hover states
- Typography hierarchy
- Spacing and layout patterns
- Card designs and shadows
- Navigation styling
- Form elements
- Icons and visual elements

The AI is particularly good at extracting precise measurements, color codes, and styling patterns that would take significant time to manually document.
