import { createLogger } from '../../utils/logger.js';
import { Neo4jClient } from '../../storage/neo4j/Neo4jClient.js';
import { IngestionPipeline } from '../../ingestion/pipeline/IngestionPipeline.js';
import { HybridRetriever } from '../../core/retrieval/HybridRetriever.js';

const logger = createLogger('ContextEngine');

/**
 * Main Context Engine class that orchestrates all components
 */
export class ContextEngine {
  constructor(config) {
    this.config = config;
    this.neo4jClient = null;
    this.ingestionPipeline = null;
    this.retriever = null;
    this.isInitialized = false;
    this.startTime = null;
  }

  /**
   * Initialize all engine components
   */
  async initialize() {
    try {
      this.startTime = Date.now();
      logger.info('Initializing Context Engine');

      // Initialize storage layer
      await this.initializeStorage();
      
      // Initialize ingestion pipeline
      await this.initializeIngestion();
      
      // Initialize retrieval system
      await this.initializeRetrieval();
      
      this.isInitialized = true;
      const initTime = Date.now() - this.startTime;
      
      logger.info('Context Engine initialized successfully', {
        initializationTime: initTime,
        components: ['storage', 'ingestion', 'retrieval']
      });

    } catch (error) {
      logger.error('Failed to initialize Context Engine', { error: error.message });
      throw error;
    }
  }

  /**
   * Initialize storage components (Neo4j, Vector DB)
   */
  async initializeStorage() {
    logger.info('Initializing storage layer');
    
    try {
      // Initialize Neo4j client
      this.neo4jClient = new Neo4jClient(this.config.neo4j);
      await this.neo4jClient.connect();
      
      logger.info('Storage layer initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize storage layer', { error: error.message });
      throw error;
    }
  }

  /**
   * Initialize ingestion pipeline
   */
  async initializeIngestion() {
    logger.info('Initializing ingestion pipeline');
    
    try {
      this.ingestionPipeline = new IngestionPipeline({
        ...this.config.processing,
        neo4jClient: this.neo4jClient
      });
      
      await this.ingestionPipeline.initialize();
      
      logger.info('Ingestion pipeline initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize ingestion pipeline', { error: error.message });
      throw error;
    }
  }

  /**
   * Initialize retrieval system
   */
  async initializeRetrieval() {
    logger.info('Initializing retrieval system');
    
    try {
      this.retriever = new HybridRetriever({
        neo4jClient: this.neo4jClient,
        config: this.config.retrieval
      });
      
      await this.retriever.initialize();
      
      logger.info('Retrieval system initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize retrieval system', { error: error.message });
      throw error;
    }
  }

  /**
   * Process a codebase from a repository path
   */
  async processCodebase(repositoryPath, options = {}) {
    if (!this.isInitialized) {
      throw new Error('Context Engine not initialized');
    }

    const startTime = Date.now();
    logger.info('Starting codebase processing', {
      repositoryPath,
      options
    });

    try {
      const result = await this.ingestionPipeline.processRepository(repositoryPath, options);
      const duration = Date.now() - startTime;
      
      logger.info('Codebase processing completed', {
        repositoryPath,
        duration,
        filesProcessed: result.filesProcessed,
        nodesCreated: result.nodesCreated,
        relationshipsCreated: result.relationshipsCreated
      });

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error('Codebase processing failed', {
        repositoryPath,
        duration,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Get context for a query
   */
  async getContext(query, options = {}) {
    if (!this.isInitialized) {
      throw new Error('Context Engine not initialized');
    }

    const startTime = Date.now();
    logger.info('Processing context query', {
      query: query.substring(0, 100),
      options
    });

    try {
      const result = await this.retriever.retrieve(query, options);
      const duration = Date.now() - startTime;
      
      logger.info('Context query completed', {
        query: query.substring(0, 100),
        duration,
        resultCount: result.results.length,
        sources: result.sources
      });

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error('Context query failed', {
        query: query.substring(0, 100),
        duration,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Get engine health status
   */
  async getHealth() {
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: this.startTime ? Date.now() - this.startTime : 0,
      components: {}
    };

    try {
      // Check Neo4j health
      if (this.neo4jClient) {
        health.components.neo4j = await this.neo4jClient.healthCheck();
      } else {
        health.components.neo4j = { status: 'not_initialized' };
      }

      // Check ingestion pipeline health
      if (this.ingestionPipeline) {
        health.components.ingestion = await this.ingestionPipeline.getHealth();
      } else {
        health.components.ingestion = { status: 'not_initialized' };
      }

      // Check retriever health
      if (this.retriever) {
        health.components.retrieval = await this.retriever.getHealth();
      } else {
        health.components.retrieval = { status: 'not_initialized' };
      }

      // Determine overall health
      const componentStatuses = Object.values(health.components).map(c => c.status);
      if (componentStatuses.some(status => status === 'unhealthy')) {
        health.status = 'unhealthy';
      } else if (componentStatuses.some(status => status === 'degraded')) {
        health.status = 'degraded';
      }

    } catch (error) {
      health.status = 'unhealthy';
      health.error = error.message;
      logger.error('Health check failed', { error: error.message });
    }

    return health;
  }

  /**
   * Get engine statistics
   */
  async getStatistics() {
    if (!this.isInitialized) {
      throw new Error('Context Engine not initialized');
    }

    try {
      const [neo4jStats, ingestionStats] = await Promise.all([
        this.neo4jClient.getStatistics(),
        this.ingestionPipeline.getStatistics()
      ]);

      return {
        timestamp: new Date().toISOString(),
        uptime: this.startTime ? Date.now() - this.startTime : 0,
        database: neo4jStats,
        ingestion: ingestionStats,
        retrieval: {
          // Add retrieval statistics when implemented
        }
      };
    } catch (error) {
      logger.error('Failed to get statistics', { error: error.message });
      throw error;
    }
  }

  /**
   * Shutdown the engine gracefully
   */
  async shutdown() {
    logger.info('Shutting down Context Engine');

    try {
      // Stop ingestion pipeline
      if (this.ingestionPipeline) {
        await this.ingestionPipeline.stop();
      }

      // Close database connections
      if (this.neo4jClient) {
        await this.neo4jClient.disconnect();
      }

      this.isInitialized = false;
      logger.info('Context Engine shutdown completed');
    } catch (error) {
      logger.error('Error during shutdown', { error: error.message });
      throw error;
    }
  }
}

export default ContextEngine;
