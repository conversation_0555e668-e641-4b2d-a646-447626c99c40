import dotenv from 'dotenv';
import <PERSON><PERSON> from 'joi';

// Load environment variables
dotenv.config();

/**
 * Configuration schema validation
 */
const configSchema = Joi.object({
  // Application settings
  NODE_ENV: Joi.string().valid('development', 'production', 'test').default('development'),
  PORT: Joi.number().port().default(3000),
  LOG_LEVEL: Joi.string().valid('error', 'warn', 'info', 'debug').default('info'),

  // Neo4j settings
  NEO4J_URI: Joi.string().uri().required(),
  NEO4J_USER: Joi.string().required(),
  NEO4J_PASSWORD: Joi.string().required(),
  NEO4J_DATABASE: Joi.string().default('neo4j'),

  // Vector database settings
  PINECONE_API_KEY: Joi.string().required(),
  PINECONE_ENVIRONMENT: Joi.string().required(),
  PINECONE_INDEX_NAME: Joi.string().default('code-context-embeddings'),

  // Redis settings
  REDIS_URL: Joi.string().uri().default('redis://localhost:6379'),
  REDIS_PASSWORD: Joi.string().allow('').default(''),

  // API settings
  API_KEY_SECRET: Joi.string().min(32).required(),
  RATE_LIMIT_WINDOW_MS: Joi.number().positive().default(900000), // 15 minutes
  RATE_LIMIT_MAX_REQUESTS: Joi.number().positive().default(100),

  // Processing settings
  MAX_CONCURRENT_PROCESSES: Joi.number().positive().default(5),
  BATCH_SIZE: Joi.number().positive().default(100),
  EMBEDDING_MODEL: Joi.string().default('text-embedding-ada-002'),

  // Monitoring settings
  METRICS_ENABLED: Joi.boolean().default(true),
  HEALTH_CHECK_INTERVAL: Joi.number().positive().default(30000),

  // Development settings
  DEBUG: Joi.string().allow('').default(''),
  ENABLE_CORS: Joi.boolean().default(true)
});

/**
 * Validate and create configuration object
 */
const { error, value: envVars } = configSchema.validate(process.env, {
  allowUnknown: true,
  stripUnknown: true
});

if (error) {
  throw new Error(`Config validation error: ${error.message}`);
}

/**
 * Application configuration object
 */
export const config = {
  env: envVars.NODE_ENV,
  port: envVars.PORT,
  logLevel: envVars.LOG_LEVEL,

  neo4j: {
    uri: envVars.NEO4J_URI,
    user: envVars.NEO4J_USER,
    password: envVars.NEO4J_PASSWORD,
    database: envVars.NEO4J_DATABASE,
    maxConnectionPoolSize: 50,
    connectionTimeout: 30000,
    maxTransactionRetryTime: 30000
  },

  vector: {
    provider: 'pinecone',
    apiKey: envVars.PINECONE_API_KEY,
    environment: envVars.PINECONE_ENVIRONMENT,
    indexName: envVars.PINECONE_INDEX_NAME,
    dimension: 1536, // OpenAI embedding dimension
    metric: 'cosine'
  },

  redis: {
    url: envVars.REDIS_URL,
    password: envVars.REDIS_PASSWORD,
    retryDelayOnFailover: 100,
    maxRetriesPerRequest: 3
  },

  api: {
    keySecret: envVars.API_KEY_SECRET,
    rateLimit: {
      windowMs: envVars.RATE_LIMIT_WINDOW_MS,
      max: envVars.RATE_LIMIT_MAX_REQUESTS
    },
    cors: {
      enabled: envVars.ENABLE_CORS,
      origin: envVars.NODE_ENV === 'production' ? false : true
    }
  },

  processing: {
    maxConcurrent: envVars.MAX_CONCURRENT_PROCESSES,
    batchSize: envVars.BATCH_SIZE,
    embeddingModel: envVars.EMBEDDING_MODEL,
    supportedLanguages: ['javascript', 'typescript', 'python', 'java', 'go', 'rust'],
    fileExtensions: {
      javascript: ['.js', '.jsx', '.mjs'],
      typescript: ['.ts', '.tsx'],
      python: ['.py', '.pyx'],
      java: ['.java'],
      go: ['.go'],
      rust: ['.rs']
    }
  },

  monitoring: {
    enabled: envVars.METRICS_ENABLED,
    healthCheckInterval: envVars.HEALTH_CHECK_INTERVAL,
    metricsPath: '/metrics',
    healthPath: '/health'
  },

  ingestion: {
    watchInterval: 1000, // ms
    debounceDelay: 500, // ms
    maxFileSize: 1024 * 1024, // 1MB
    excludePatterns: [
      'node_modules/**',
      '.git/**',
      'dist/**',
      'build/**',
      '*.log',
      '*.tmp'
    ]
  },

  retrieval: {
    vectorTopK: 50,
    graphMaxDepth: 3,
    hybridWeights: {
      vector: 0.6,
      graph: 0.4
    },
    cacheEnabled: true,
    cacheTTL: 300 // 5 minutes
  }
};

export default config;
