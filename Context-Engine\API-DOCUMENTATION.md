# Advanced Code Context Engine - API Documentation

## 📖 Overview

The Advanced Code Context Engine provides a comprehensive RESTful API for querying and managing code context. This API enables AI coding agents and other applications to access deep insights about codebases through graph-based analysis and hybrid retrieval.

## 🔗 Base URL

```
http://localhost:3000/api
```

## 🔐 Authentication

Currently, the API uses API key authentication (configurable for production):

```bash
# Include API key in headers (when enabled)
curl -H "X-API-Key: your-api-key" http://localhost:3000/api/context/query
```

## 📊 Response Format

All API responses follow this consistent structure:

```json
{
  "success": true,
  "data": {
    // Response data
  },
  "meta": {
    "processingTime": 150,
    "timestamp": "2024-01-01T12:00:00.000Z"
  }
}
```

Error responses:
```json
{
  "success": false,
  "error": {
    "message": "Error description",
    "type": "ERROR_TYPE",
    "details": []
  },
  "meta": {
    "processingTime": 50,
    "timestamp": "2024-01-01T12:00:00.000Z"
  }
}
```

## 🔍 Context Query Endpoints

### Query for Code Context

**POST** `/context/query`

Query the codebase using natural language to find relevant code elements.

#### Request Body
```json
{
  "query": "find all functions that handle user authentication",
  "options": {
    "language": "javascript",
    "filePattern": "src/**/*.js",
    "limit": 10,
    "minLines": 5,
    "maxLines": 100,
    "includeTests": false
  }
}
```

#### Parameters
- `query` (string, required): Natural language query
- `options` (object, optional):
  - `language` (string): Filter by programming language
  - `filePattern` (string): File path pattern to search
  - `limit` (number): Maximum results to return (1-100)
  - `minLines` (number): Minimum lines of code
  - `maxLines` (number): Maximum lines of code
  - `includeTests` (boolean): Include test files

#### Response
```json
{
  "success": true,
  "data": {
    "results": [
      {
        "id": 123,
        "type": "function",
        "name": "authenticateUser",
        "file": {
          "path": "src/auth/authentication.js",
          "language": "javascript"
        },
        "location": {
          "startLine": 15,
          "endLine": 45
        },
        "score": 0.95,
        "metadata": {
          "complexity": "medium",
          "parameters": ["username", "password"],
          "returnType": "Promise<User>"
        }
      }
    ],
    "total": 5,
    "limited": false,
    "sources": ["graph", "vector"],
    "query": {
      "original": "find all functions that handle user authentication",
      "processed": true
    },
    "processingTime": 245
  }
}
```

### Search Files

**GET** `/context/files/search`

Search for files by name, path, or content patterns.

#### Query Parameters
- `q` (string, required): Search query
- `language` (string): Filter by language
- `limit` (number): Maximum results (default: 20)

#### Example
```bash
curl "http://localhost:3000/api/context/files/search?q=authentication&language=javascript&limit=10"
```

#### Response
```json
{
  "success": true,
  "data": {
    "files": [
      {
        "id": 456,
        "type": "File",
        "name": "authentication.js",
        "file": {
          "path": "src/auth/authentication.js",
          "language": "javascript",
          "size": 2048,
          "lastModified": "2024-01-01T10:00:00.000Z"
        }
      }
    ],
    "total": 3,
    "query": "authentication"
  }
}
```

## 🏗️ Repository Processing Endpoints

### Process Repository

**POST** `/context/repositories/process`

Process a Git repository to extract and store code context.

#### Request Body
```json
{
  "repositoryPath": "/path/to/repository",
  "options": {
    "batchSize": 100,
    "maxConcurrent": 5,
    "includeTests": true,
    "includeNodeModules": false,
    "languages": ["javascript", "typescript", "python"],
    "excludePatterns": ["dist/**", "build/**"]
  }
}
```

#### Parameters
- `repositoryPath` (string, required): Absolute path to Git repository
- `options` (object, optional):
  - `batchSize` (number): Files per batch (1-1000)
  - `maxConcurrent` (number): Concurrent processes (1-20)
  - `includeTests` (boolean): Process test files
  - `includeNodeModules` (boolean): Process node_modules
  - `languages` (array): Languages to process
  - `excludePatterns` (array): Patterns to exclude

#### Response
```json
{
  "success": true,
  "data": {
    "repositoryPath": "/path/to/repository",
    "filesProcessed": 156,
    "nodesCreated": 1247,
    "relationshipsCreated": 892,
    "duration": 45000,
    "errors": 2,
    "summary": {
      "functions": 234,
      "classes": 45,
      "files": 156,
      "languages": ["javascript", "typescript"]
    }
  }
}
```

## 📈 Statistics and Monitoring

### Get Engine Statistics

**GET** `/context/statistics`

Retrieve comprehensive statistics about the context engine.

#### Response
```json
{
  "success": true,
  "data": {
    "timestamp": "2024-01-01T12:00:00.000Z",
    "uptime": 3600000,
    "database": {
      "nodeCount": 10000,
      "relationshipCount": 15000,
      "fileCount": 500,
      "functionCount": 2000,
      "classCount": 300
    },
    "ingestion": {
      "filesProcessed": 500,
      "processingRate": 2.5,
      "queueSize": 0,
      "errors": 5
    },
    "retrieval": {
      "totalQueries": 1000,
      "avgResponseTime": 150,
      "cacheHitRate": 0.75
    }
  }
}
```

## 🔍 Code Element Details

### Get Element Details

**GET** `/context/elements/{id}`

Get detailed information about a specific code element.

#### Parameters
- `id` (number, required): Element ID from query results

#### Response
```json
{
  "success": true,
  "data": {
    "id": 123,
    "labels": ["Function", "CodeElement"],
    "properties": {
      "name": "authenticateUser",
      "signature": "authenticateUser(username, password)",
      "startLine": 15,
      "endLine": 45,
      "complexity": "medium"
    },
    "file": {
      "path": "src/auth/authentication.js",
      "language": "javascript"
    }
  }
}
```

### Get Element Relationships

**GET** `/context/elements/{id}/relationships`

Get relationships for a specific code element.

#### Query Parameters
- `direction` (string): "incoming", "outgoing", or "both" (default: "both")
- `limit` (number): Maximum relationships to return (default: 50)

#### Response
```json
{
  "success": true,
  "data": {
    "relationships": [
      {
        "id": 789,
        "type": "CALLS",
        "properties": {
          "callCount": 5,
          "lastCalled": "2024-01-01T10:00:00.000Z"
        },
        "relatedNode": {
          "id": 456,
          "labels": ["Function"],
          "properties": {
            "name": "validateCredentials"
          }
        }
      }
    ],
    "total": 12,
    "direction": "both",
    "elementId": 123
  }
}
```

## 🏥 Health and Status Endpoints

### Basic Health Check

**GET** `/health`

Basic health status of the service.

#### Response
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "uptime": 3600000,
  "version": "1.0.0",
  "environment": "production",
  "components": {
    "neo4j": { "status": "healthy" },
    "ingestion": { "status": "healthy" },
    "retrieval": { "status": "healthy" }
  }
}
```

### Detailed Health Check

**GET** `/health/detailed`

Comprehensive health information including system metrics.

#### Response
```json
{
  "status": "healthy",
  "components": {
    "neo4j": {
      "status": "healthy",
      "connected": true,
      "recordCount": 1
    }
  },
  "statistics": {
    "database": { "nodeCount": 10000 },
    "ingestion": { "filesProcessed": 500 }
  },
  "system": {
    "nodeVersion": "v18.17.0",
    "platform": "linux",
    "memory": {
      "rss": 134217728,
      "heapTotal": 67108864,
      "heapUsed": 45088768
    },
    "uptime": 3600
  }
}
```

### Readiness Probe

**GET** `/health/ready`

Check if the service is ready to accept requests.

### Liveness Probe

**GET** `/health/live`

Check if the service is alive and responding.

### Component Health

**GET** `/health/components/{component}`

Check health of a specific component (neo4j, ingestion, retrieval).

### Performance Metrics

**GET** `/health/metrics`

Get performance metrics for monitoring systems.

## ❌ Error Codes

### HTTP Status Codes
- `200` - Success
- `400` - Bad Request (validation error)
- `401` - Unauthorized (invalid API key)
- `404` - Not Found
- `429` - Too Many Requests (rate limited)
- `500` - Internal Server Error
- `503` - Service Unavailable

### Error Types
- `VALIDATION_ERROR` - Request validation failed
- `CONTEXT_QUERY_ERROR` - Query processing failed
- `REPOSITORY_PROCESSING_ERROR` - Repository processing failed
- `NOT_FOUND` - Resource not found
- `INTERNAL_ERROR` - Internal server error

## 📝 Examples

### Complete Query Example

```bash
# Query for authentication functions
curl -X POST http://localhost:3000/api/context/query \
  -H "Content-Type: application/json" \
  -d '{
    "query": "find functions that validate user input",
    "options": {
      "language": "javascript",
      "limit": 5,
      "includeTests": false
    }
  }'
```

### Process Repository Example

```bash
# Process a local repository
curl -X POST http://localhost:3000/api/context/repositories/process \
  -H "Content-Type: application/json" \
  -d '{
    "repositoryPath": "/home/<USER>/my-project",
    "options": {
      "batchSize": 50,
      "languages": ["javascript", "typescript"]
    }
  }'
```

### Search Files Example

```bash
# Search for configuration files
curl "http://localhost:3000/api/context/files/search?q=config&limit=10"
```

## 🔧 Rate Limiting

The API implements rate limiting to ensure fair usage:

- **Default**: 100 requests per 15 minutes per IP
- **Headers**: Rate limit info in response headers
- **Override**: Configurable via environment variables

Rate limit headers:
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```

## 📚 SDK and Integration

### JavaScript/Node.js Example

```javascript
const ContextEngineClient = require('./context-engine-client');

const client = new ContextEngineClient({
  baseURL: 'http://localhost:3000/api',
  apiKey: 'your-api-key'
});

// Query for context
const results = await client.query('find user authentication functions', {
  language: 'javascript',
  limit: 10
});

// Process repository
const processing = await client.processRepository('/path/to/repo', {
  batchSize: 100,
  languages: ['javascript', 'typescript']
});
```

### Python Example

```python
import requests

class ContextEngineClient:
    def __init__(self, base_url, api_key=None):
        self.base_url = base_url
        self.headers = {'Content-Type': 'application/json'}
        if api_key:
            self.headers['X-API-Key'] = api_key
    
    def query(self, query_text, options=None):
        response = requests.post(
            f"{self.base_url}/context/query",
            json={"query": query_text, "options": options or {}},
            headers=self.headers
        )
        return response.json()

# Usage
client = ContextEngineClient('http://localhost:3000/api')
results = client.query('find database connection functions')
```

This API documentation provides comprehensive guidance for integrating with the Advanced Code Context Engine. The API is designed to be intuitive, well-documented, and suitable for both human developers and AI coding agents.
