# Advanced Code Context Engine

A sophisticated AI-driven code context engine that provides unparalleled understanding of codebases through advanced graph analysis, AST processing, and hybrid retrieval systems.

## 🚀 Features

- **Multi-Language Support**: JavaScript, TypeScript, Python, Java, Go, Rust
- **Graph-Based Analysis**: Neo4j-powered knowledge graph with bi-temporal data modeling
- **AST Processing**: Tree-sitter based Abstract Syntax Tree analysis
- **Hybrid Retrieval**: Combines graph traversal with semantic vector search
- **Real-Time Ingestion**: Continuous monitoring of Git repositories and local files
- **RESTful API**: Comprehensive API for integration with coding agents
- **Scalable Architecture**: Distributed processing with Docker support

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Ingestion     │    │   Processing    │    │    Storage      │
│     Layer       │───▶│    Pipeline     │───▶│     Layer       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Git Repositories│    │ AST Processing  │    │     Neo4j       │
│ Local Files     │    │ Symbol Tables   │    │ Vector Database │
│ Change Detection│    │ Code Embeddings │    │ Redis Cache     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                    ┌─────────────────┐
                    │   Retrieval     │
                    │     System      │
                    └─────────────────┘
                                │
                                ▼
                    ┌─────────────────┐
                    │   API Gateway   │
                    │ Context Queries │
                    └─────────────────┘
```

## 🛠️ Installation

### Prerequisites

- Node.js 18+
- Docker & Docker Compose
- Git

### Quick Start with Docker

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd advanced-code-context-engine
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start the services**
   ```bash
   docker-compose up -d
   ```

4. **Verify installation**
   ```bash
   curl http://localhost:3000/api/health
   ```

### Local Development Setup

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Start Neo4j and Redis**
   ```bash
   docker-compose up -d neo4j redis
   ```

3. **Configure environment**
   ```bash
   cp .env.example .env
   # Update database connection strings
   ```

4. **Start the application**
   ```bash
   npm run dev
   ```

## 📖 API Documentation

### Core Endpoints

#### Query Context
```http
POST /api/context/query
Content-Type: application/json

{
  "query": "find all functions that handle user authentication",
  "options": {
    "language": "javascript",
    "limit": 10
  }
}
```

#### Process Repository
```http
POST /api/context/repositories/process
Content-Type: application/json

{
  "repositoryPath": "/path/to/repository",
  "options": {
    "batchSize": 100,
    "includeTests": true
  }
}
```

#### Search Files
```http
GET /api/context/files/search?q=authentication&language=javascript&limit=20
```

#### Health Check
```http
GET /api/health
```

### Response Format

All API responses follow this structure:

```json
{
  "success": true,
  "data": {
    // Response data
  },
  "meta": {
    "processingTime": 150,
    "timestamp": "2024-01-01T12:00:00.000Z"
  }
}
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `NODE_ENV` | Environment (development/production) | `development` |
| `PORT` | Server port | `3000` |
| `NEO4J_URI` | Neo4j connection URI | `bolt://localhost:7687` |
| `NEO4J_USER` | Neo4j username | `neo4j` |
| `NEO4J_PASSWORD` | Neo4j password | `password` |
| `REDIS_URL` | Redis connection URL | `redis://localhost:6379` |
| `LOG_LEVEL` | Logging level | `info` |

### Processing Configuration

```javascript
{
  "processing": {
    "maxConcurrent": 5,
    "batchSize": 100,
    "supportedLanguages": ["javascript", "typescript", "python"],
    "excludePatterns": ["node_modules/**", ".git/**"]
  }
}
```

## 🚀 Usage Examples

### Processing a Repository

```javascript
import { ContextEngine } from './src/core/engine/ContextEngine.js';
import { config } from './src/utils/config.js';

const engine = new ContextEngine(config);
await engine.initialize();

const result = await engine.processCodebase('/path/to/repository');
console.log(`Processed ${result.filesProcessed} files`);
```

### Querying Context

```javascript
const context = await engine.getContext(
  'find all React components that use useState hook',
  {
    language: 'javascript',
    limit: 20
  }
);

console.log(`Found ${context.results.length} results`);
```

### Using the API

```bash
# Process a repository
curl -X POST http://localhost:3000/api/context/repositories/process \
  -H "Content-Type: application/json" \
  -d '{"repositoryPath": "/path/to/repo"}'

# Query for context
curl -X POST http://localhost:3000/api/context/query \
  -H "Content-Type: application/json" \
  -d '{"query": "authentication functions", "options": {"language": "javascript"}}'
```

## 🧪 Testing

### Run Tests
```bash
npm test
```

### Run with Coverage
```bash
npm run test:coverage
```

### Integration Tests
```bash
npm run test:integration
```

## 📊 Monitoring

### Health Checks

- **Basic**: `GET /api/health`
- **Detailed**: `GET /api/health/detailed`
- **Readiness**: `GET /api/health/ready`
- **Liveness**: `GET /api/health/live`

### Metrics

- **Application**: `GET /api/health/metrics`
- **Database**: `GET /api/health/database`
- **Components**: `GET /api/health/components/:component`

### Logging

Logs are structured JSON format with multiple levels:
- `error`: Error conditions
- `warn`: Warning conditions
- `info`: Informational messages
- `debug`: Debug-level messages

## 🔒 Security

- **Input Validation**: Joi-based request validation
- **Rate Limiting**: Configurable rate limits
- **CORS**: Cross-origin resource sharing controls
- **Helmet**: Security headers
- **Authentication**: API key-based authentication

## 🚀 Deployment

### Production Deployment

1. **Build the Docker image**
   ```bash
   docker build -t context-engine:latest .
   ```

2. **Deploy with Docker Compose**
   ```bash
   docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
   ```

3. **Configure monitoring**
   ```bash
   docker-compose --profile monitoring up -d
   ```

### Kubernetes Deployment

Kubernetes manifests are available in the `k8s/` directory:

```bash
kubectl apply -f k8s/
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [API Docs](http://localhost:3000/api/docs)
- **Issues**: [GitHub Issues](https://github.com/your-org/advanced-code-context-engine/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-org/advanced-code-context-engine/discussions)

## 🗺️ Roadmap

- [ ] Vector database integration (Pinecone/Weaviate)
- [ ] Advanced code embeddings
- [ ] Multi-repository support
- [ ] Real-time collaboration features
- [ ] Machine learning-based code suggestions
- [ ] IDE integrations
- [ ] GraphQL API
- [ ] Webhook support for CI/CD integration
