import { NextRequest, NextResponse } from "next/server"
import { aiService } from "@/lib/ai-service"

export async function POST(request: NextRequest) {
  try {
    const { prompt, analysis, question, context } = await request.json()

    if (!prompt || !question) {
      return NextResponse.json({ error: "Prompt and question are required" }, { status: 400 })
    }

    console.log("🤖 Autonomous question answering for:", question.question)

    // Use the AI service to answer the question autonomously
    const autonomousAnswer = await aiService.answerQuestionsAutonomously(
      prompt,
      analysis,
      [question]
    )

    console.log("✅ Autonomous answer generated successfully")

    // Extract the first answer from the response
    const firstAnswer = autonomousAnswer.answers?.[0]
    if (!firstAnswer) {
      throw new Error("No autonomous answer generated")
    }

    return NextResponse.json({
      success: true,
      answer: firstAnswer.answer,
      reasoning: firstAnswer.reasoning,
      confidence: firstAnswer.confidence,
      assumptions: firstAnswer.assumptions,
      alternatives: firstAnswer.alternatives,
      recommendations: autonomousAnswer.recommendations || [],
      riskAssessment: autonomousAnswer.riskAssessment || []
    })

  } catch (error) {
    console.error("❌ Autonomous answer generation failed:", error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Failed to generate autonomous answer",
      // Provide a fallback answer
      answer: "Standard implementation following industry best practices",
      reasoning: "Fallback to standard approach due to processing error",
      confidence: "medium",
      assumptions: ["Using industry standard practices"],
      alternatives: ["Custom implementation based on specific requirements"]
    }, { status: 500 })
  }
}
