/**
 * Unit tests for AST Processor
 */

import { jest } from '@jest/globals';

// Mock the logger
jest.unstable_mockModule('../../../../src/utils/logger.js', () => ({
  createLogger: jest.fn(() => ({
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  }))
}));

describe('ASTProcessor', () => {
  let ASTProcessor;
  let processor;

  beforeAll(async () => {
    const module = await import('../../../../src/core/processors/ASTProcessor.js');
    ASTProcessor = module.ASTProcessor;
  });

  beforeEach(() => {
    processor = new ASTProcessor({
      supportedLanguages: ['javascript', 'typescript', 'python']
    });
  });

  describe('initialization', () => {
    test('should initialize successfully', async () => {
      await expect(processor.initialize()).resolves.not.toThrow();
      expect(processor.isInitialized).toBe(true);
    });
  });

  describe('JavaScript processing', () => {
    test('should extract functions from JavaScript code', async () => {
      await processor.initialize();
      
      const jsCode = `
        function testFunction(param1, param2) {
          return param1 + param2;
        }
        
        const arrowFunction = (x) => x * 2;
        
        async function asyncFunction() {
          return await Promise.resolve(42);
        }
      `;

      const file = global.testUtils.createMockFile({
        content: jsCode,
        language: 'javascript'
      });

      const result = await processor.process({ file });
      
      expect(result.ast).toBeDefined();
      expect(result.ast.nodes).toHaveLength(3);
      
      const functions = result.ast.nodes.filter(n => n.nodeType === 'function');
      expect(functions).toHaveLength(3);
      
      const functionNames = functions.map(f => f.name);
      expect(functionNames).toContain('testFunction');
      expect(functionNames).toContain('arrowFunction');
      expect(functionNames).toContain('asyncFunction');
    });

    test('should extract classes from JavaScript code', async () => {
      await processor.initialize();
      
      const jsCode = `
        class TestClass {
          constructor(name) {
            this.name = name;
          }
          
          getName() {
            return this.name;
          }
        }
        
        class ExtendedClass extends TestClass {
          constructor(name, age) {
            super(name);
            this.age = age;
          }
        }
      `;

      const file = global.testUtils.createMockFile({
        content: jsCode,
        language: 'javascript'
      });

      const result = await processor.process({ file });
      
      expect(result.ast).toBeDefined();
      expect(result.ast.nodes).toHaveLength(2);
      
      const classes = result.ast.nodes.filter(n => n.nodeType === 'class');
      expect(classes).toHaveLength(2);
      
      const testClass = classes.find(c => c.name === 'TestClass');
      expect(testClass).toBeDefined();
      expect(testClass.superclass).toBeNull();
      
      const extendedClass = classes.find(c => c.name === 'ExtendedClass');
      expect(extendedClass).toBeDefined();
      expect(extendedClass.superclass).toBe('TestClass');
    });

    test('should extract imports from JavaScript code', async () => {
      await processor.initialize();
      
      const jsCode = `
        import React from 'react';
        import { useState, useEffect } from 'react';
        import * as utils from './utils';
        import './styles.css';
      `;

      const file = global.testUtils.createMockFile({
        content: jsCode,
        language: 'javascript'
      });

      const result = await processor.process({ file });
      
      expect(result.ast).toBeDefined();
      expect(result.ast.imports).toHaveLength(4);
      
      const reactImport = result.ast.imports.find(i => i.source === 'react' && i.isDefault);
      expect(reactImport).toBeDefined();
      expect(reactImport.specifiers).toContain('React');
      
      const namedImport = result.ast.imports.find(i => i.source === 'react' && !i.isDefault);
      expect(namedImport).toBeDefined();
      expect(namedImport.specifiers).toContain('useState');
      expect(namedImport.specifiers).toContain('useEffect');
    });
  });

  describe('Python processing', () => {
    test('should extract functions from Python code', async () => {
      await processor.initialize();
      
      const pyCode = `
        def regular_function(param1, param2):
            return param1 + param2
        
        async def async_function():
            return await some_async_call()
        
        def function_with_defaults(x=10, y=20):
            return x * y
      `;

      const file = global.testUtils.createMockFile({
        content: pyCode,
        language: 'python'
      });

      const result = await processor.process({ file });
      
      expect(result.ast).toBeDefined();
      expect(result.ast.nodes).toHaveLength(3);
      
      const functions = result.ast.nodes.filter(n => n.nodeType === 'function');
      expect(functions).toHaveLength(3);
      
      const functionNames = functions.map(f => f.name);
      expect(functionNames).toContain('regular_function');
      expect(functionNames).toContain('async_function');
      expect(functionNames).toContain('function_with_defaults');
    });

    test('should extract classes from Python code', async () => {
      await processor.initialize();
      
      const pyCode = `
        class BaseClass:
            def __init__(self):
                self.value = 0
        
        class DerivedClass(BaseClass):
            def __init__(self, value):
                super().__init__()
                self.value = value
      `;

      const file = global.testUtils.createMockFile({
        content: pyCode,
        language: 'python'
      });

      const result = await processor.process({ file });
      
      expect(result.ast).toBeDefined();
      expect(result.ast.nodes).toHaveLength(2);
      
      const classes = result.ast.nodes.filter(n => n.nodeType === 'class');
      expect(classes).toHaveLength(2);
      
      const baseClass = classes.find(c => c.name === 'BaseClass');
      expect(baseClass).toBeDefined();
      
      const derivedClass = classes.find(c => c.name === 'DerivedClass');
      expect(derivedClass).toBeDefined();
      expect(derivedClass.superclass).toBe('BaseClass');
    });

    test('should extract imports from Python code', async () => {
      await processor.initialize();
      
      const pyCode = `
        import os
        import sys
        from datetime import datetime, timedelta
        from typing import List, Dict
      `;

      const file = global.testUtils.createMockFile({
        content: pyCode,
        language: 'python'
      });

      const result = await processor.process({ file });
      
      expect(result.ast).toBeDefined();
      expect(result.ast.imports).toHaveLength(4);
      
      const osImport = result.ast.imports.find(i => i.source === 'os');
      expect(osImport).toBeDefined();
      expect(osImport.isDefault).toBe(true);
      
      const datetimeImport = result.ast.imports.find(i => i.source === 'datetime');
      expect(datetimeImport).toBeDefined();
      expect(datetimeImport.specifiers).toContain('datetime');
      expect(datetimeImport.specifiers).toContain('timedelta');
    });
  });

  describe('error handling', () => {
    test('should handle unsupported language gracefully', async () => {
      await processor.initialize();
      
      const file = global.testUtils.createMockFile({
        content: 'some code',
        language: 'unsupported'
      });

      const result = await processor.process({ file });
      
      expect(result.ast).toBeDefined();
      expect(result.ast.language).toBe('unsupported');
      expect(result.ast.nodes).toHaveLength(0);
    });

    test('should handle empty file content', async () => {
      await processor.initialize();
      
      const file = global.testUtils.createMockFile({
        content: '',
        language: 'javascript'
      });

      const result = await processor.process({ file });
      
      expect(result.ast).toBeDefined();
      expect(result.ast.nodes).toHaveLength(0);
    });

    test('should handle malformed code gracefully', async () => {
      await processor.initialize();
      
      const file = global.testUtils.createMockFile({
        content: 'function incomplete(',
        language: 'javascript'
      });

      const result = await processor.process({ file });
      
      expect(result.ast).toBeDefined();
      // Should not throw, even with malformed code
    });
  });
});
