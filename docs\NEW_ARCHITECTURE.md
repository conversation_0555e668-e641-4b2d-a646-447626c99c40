# AG3NT Refactored Architecture: CrewAI + LangGraph

## Overview

This document outlines the refactored architecture for AG3NT, transitioning from a custom agent framework to a robust system using CrewAI for multi-agent orchestration and LangGraph for explicit workflow control.

## Architecture Components

### 1. Python Backend (FastAPI)
- **Purpose**: Host CrewAI agents and LangGraph workflows
- **Technology**: FastAPI, Python 3.11+
- **Location**: `/backend/` directory

### 2. CrewAI Agent Teams

#### **Project Planning Crew**
```python
class ProjectPlanningCrew:
    agents:
        - Project Analyst: Analyzes requirements and scope
        - Requirements Engineer: Clarifies and validates requirements
        - Technical Architect: Defines technical approach
    
    tasks:
        - analyze_project_requirements
        - clarify_ambiguities
        - generate_project_summary
```

#### **Development Crew**
```python
class DevelopmentCrew:
    agents:
        - Tech Stack Advisor: Recommends optimal technologies
        - System Designer: Creates system architecture
        - Code Architect: Plans code structure
    
    tasks:
        - select_technology_stack
        - design_system_architecture
        - plan_file_structure
```

#### **Implementation Crew**
```python
class ImplementationCrew:
    agents:
        - Task Planner: Breaks down implementation tasks
        - Workflow Designer: Defines development workflows
        - Quality Assurance: Reviews and validates outputs
    
    tasks:
        - breakdown_implementation_tasks
        - define_development_workflow
        - validate_deliverables
```

### 3. LangGraph Workflows

#### **Project Initialization Workflow**
```python
class ProjectInitWorkflow(StateGraph):
    state: ProjectState
    nodes:
        - analyze_requirements
        - gather_clarifications
        - validate_scope
        - initialize_project
    
    flow:
        START → analyze_requirements → gather_clarifications
        gather_clarifications → validate_scope → initialize_project → END
```

#### **Iterative Planning Workflow**
```python
class IterativePlanningWorkflow(StateGraph):
    state: PlanningState
    nodes:
        - generate_plan
        - review_plan
        - refine_plan
        - approve_plan
    
    flow:
        START → generate_plan → review_plan
        review_plan → [refine_plan | approve_plan]
        refine_plan → generate_plan
        approve_plan → END
```

#### **Code Review Workflow**
```python
class CodeReviewWorkflow(StateGraph):
    state: CodeReviewState
    nodes:
        - analyze_code
        - identify_issues
        - suggest_improvements
        - validate_fixes
    
    flow:
        START → analyze_code → identify_issues
        identify_issues → suggest_improvements → validate_fixes → END
```

### 4. Context7 Integration

#### **Enhanced Context Engine**
```python
class Context7Engine:
    capabilities:
        - Advanced RAG with vector search
        - Real-time knowledge retrieval
        - Context persistence across sessions
        - Multi-modal context understanding
    
    integration_points:
        - CrewAI agents (as tools)
        - LangGraph workflows (as context providers)
        - Frontend components (via API)
```

### 5. API Layer

#### **FastAPI Endpoints**
```python
# Agent Management
POST /api/crews/{crew_name}/kickoff
GET /api/crews/{crew_name}/status
POST /api/crews/{crew_name}/interrupt

# Workflow Management  
POST /api/workflows/{workflow_name}/start
GET /api/workflows/{workflow_name}/state
POST /api/workflows/{workflow_name}/resume

# Context Management
POST /api/context/enhance
GET /api/context/retrieve
POST /api/context/update
```

### 6. Frontend Integration

#### **Updated Next.js Components**
- **PlanningAgent** → **CrewOrchestrator**
- **TaskBreakdown** → **WorkflowManager**
- **ContextEngine** → **Context7Interface**
- **ResultsView** → **MultiAgentResults**

## Data Flow

### 1. Project Initialization
```
User Input → Frontend → API → ProjectInitWorkflow → ProjectPlanningCrew → Context7 → Results
```

### 2. Iterative Planning
```
Requirements → IterativePlanningWorkflow → DevelopmentCrew → Feedback Loop → Refined Plan
```

### 3. Implementation
```
Plan → CodeReviewWorkflow → ImplementationCrew → Generated Code → Quality Check
```

## State Management

### **Shared State Schema**
```python
class ProjectState(TypedDict):
    project_id: str
    requirements: Dict[str, Any]
    current_phase: str
    crew_results: Dict[str, Any]
    workflow_state: Dict[str, Any]
    context_data: Dict[str, Any]
    user_feedback: List[Dict[str, Any]]
```

### **Persistence Strategy**
- **LangGraph**: Built-in checkpointing for workflow state
- **CrewAI**: Custom state persistence for crew results
- **Context7**: Vector database for context storage
- **Frontend**: Real-time state synchronization via WebSockets

## Benefits of New Architecture

### **Scalability**
- Independent agent scaling
- Workflow parallelization
- Modular component architecture

### **Reliability**
- Built-in error handling and recovery
- State persistence and resumption
- Comprehensive logging and monitoring

### **Flexibility**
- Dynamic agent composition
- Configurable workflow paths
- Extensible agent capabilities

### **Maintainability**
- Clear separation of concerns
- Standardized agent interfaces
- Comprehensive testing framework

## Migration Strategy

1. **Phase 1**: Set up Python backend with basic FastAPI structure
2. **Phase 2**: Implement core CrewAI agents with existing functionality
3. **Phase 3**: Create LangGraph workflows for complex processes
4. **Phase 4**: Integrate Context7 for enhanced context management
5. **Phase 5**: Update frontend to use new API endpoints
6. **Phase 6**: Implement real-time state synchronization
7. **Phase 7**: Add comprehensive testing and monitoring

## Technology Stack

### **Backend**
- Python 3.11+
- FastAPI
- CrewAI
- LangGraph
- Context7
- Pydantic (data validation)
- SQLAlchemy (database ORM)
- Redis (caching/sessions)

### **Frontend** (Existing)
- Next.js 15
- React 19
- TypeScript
- Tailwind CSS
- Radix UI

### **Infrastructure**
- Docker containers
- PostgreSQL database
- Redis cache
- Vector database (for Context7)
- WebSocket connections

## Next Steps

1. Create Python backend structure
2. Implement basic CrewAI agents
3. Set up LangGraph workflows
4. Integrate Context7 engine
5. Create API bridge layer
6. Update frontend components
7. Implement state management
8. Add comprehensive testing

This architecture provides a robust, scalable, and maintainable foundation for AG3NT while leveraging industry-standard frameworks for agent orchestration and workflow management.
