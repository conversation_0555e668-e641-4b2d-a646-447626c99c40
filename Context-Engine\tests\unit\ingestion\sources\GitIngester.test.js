/**
 * Unit tests for Git Ingester
 */

import { jest } from '@jest/globals';
import fs from 'fs/promises';
import path from 'path';

// Mock the logger
jest.unstable_mockModule('../../../../src/utils/logger.js', () => ({
  createLogger: jest.fn(() => ({
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  }))
}));

// Mock simple-git
const mockGit = {
  checkIsRepo: jest.fn(),
  raw: jest.fn(),
  log: jest.fn(),
  status: jest.fn(),
  getRemotes: jest.fn(),
  branch: jest.fn()
};

jest.unstable_mockModule('simple-git', () => ({
  default: jest.fn(() => mockGit)
}));

// Mock fs operations
jest.mock('fs/promises');

describe('GitIngester', () => {
  let GitIngester;
  let ingester;
  const testRepoPath = '/test/repo';

  beforeAll(async () => {
    const module = await import('../../../../src/ingestion/sources/GitIngester.js');
    GitIngester = module.GitIngester;
  });

  beforeEach(() => {
    jest.clearAllMocks();
    ingester = new GitIngester(testRepoPath, {
      supportedLanguages: ['javascript', 'python'],
      fileExtensions: {
        javascript: ['.js', '.jsx'],
        python: ['.py']
      },
      maxFileSize: 1024 * 1024,
      excludePatterns: ['node_modules/**', '.git/**']
    });
  });

  describe('initialization', () => {
    test('should initialize successfully with valid git repository', async () => {
      fs.stat.mockResolvedValue({ isDirectory: () => true });
      mockGit.checkIsRepo.mockResolvedValue(true);

      await expect(ingester.initialize()).resolves.not.toThrow();
      expect(ingester.isInitialized).toBe(true);
    });

    test('should throw error for non-directory path', async () => {
      fs.stat.mockResolvedValue({ isDirectory: () => false });

      await expect(ingester.initialize()).rejects.toThrow('Repository path is not a directory');
    });

    test('should throw error for non-git repository', async () => {
      fs.stat.mockResolvedValue({ isDirectory: () => true });
      mockGit.checkIsRepo.mockResolvedValue(false);

      await expect(ingester.initialize()).rejects.toThrow('Path is not a Git repository');
    });

    test('should throw error for non-existent path', async () => {
      fs.stat.mockRejectedValue(new Error('ENOENT: no such file or directory'));

      await expect(ingester.initialize()).rejects.toThrow('ENOENT: no such file or directory');
    });
  });

  describe('file discovery', () => {
    beforeEach(async () => {
      fs.stat.mockResolvedValue({ isDirectory: () => true });
      mockGit.checkIsRepo.mockResolvedValue(true);
      await ingester.initialize();
    });

    test('should get all tracked files from repository', async () => {
      const mockFiles = 'src/index.js\nsrc/utils.py\nREADME.md\npackage.json';
      mockGit.raw.mockResolvedValue(mockFiles);

      // Mock file stats and content for each file
      fs.stat.mockImplementation((filePath) => {
        return Promise.resolve({
          size: 1000,
          mtime: new Date('2024-01-01'),
          birthtime: new Date('2024-01-01')
        });
      });

      fs.readFile.mockImplementation((filePath) => {
        if (filePath.includes('index.js')) {
          return Promise.resolve('console.log("Hello World");');
        } else if (filePath.includes('utils.py')) {
          return Promise.resolve('def hello(): print("Hello")');
        }
        return Promise.resolve('# README');
      });

      mockGit.log.mockResolvedValue({
        latest: {
          hash: 'abc123',
          date: '2024-01-01',
          author_name: 'Test Author',
          message: 'Initial commit'
        }
      });

      const files = await ingester.getFiles();
      
      expect(files).toHaveLength(2); // Only JS and Python files should be processed
      expect(files.map(f => f.language)).toContain('javascript');
      expect(files.map(f => f.language)).toContain('python');
    });

    test('should apply file filter when provided', async () => {
      const mockFiles = 'src/index.js\nsrc/test.js\nsrc/utils.py';
      mockGit.raw.mockResolvedValue(mockFiles);

      fs.stat.mockResolvedValue({
        size: 1000,
        mtime: new Date('2024-01-01'),
        birthtime: new Date('2024-01-01')
      });

      fs.readFile.mockResolvedValue('test content');
      mockGit.log.mockResolvedValue({ latest: null });

      const filter = (file) => !file.path.includes('test');
      const files = await ingester.getFiles(filter);
      
      expect(files).toHaveLength(2); // index.js and utils.py, but not test.js
      expect(files.map(f => f.path)).not.toContain('src/test.js');
    });

    test('should handle empty repository', async () => {
      mockGit.raw.mockResolvedValue('');

      const files = await ingester.getFiles();
      
      expect(files).toHaveLength(0);
    });
  });

  describe('language detection', () => {
    test('should detect JavaScript files correctly', () => {
      expect(ingester.detectLanguage('test.js')).toBe('javascript');
      expect(ingester.detectLanguage('test.jsx')).toBe('javascript');
      expect(ingester.detectLanguage('test.mjs')).toBe('unknown'); // Not in config
    });

    test('should detect Python files correctly', () => {
      expect(ingester.detectLanguage('test.py')).toBe('python');
      expect(ingester.detectLanguage('test.pyx')).toBe('unknown'); // Not in config
    });

    test('should return unknown for unsupported extensions', () => {
      expect(ingester.detectLanguage('test.txt')).toBe('unknown');
      expect(ingester.detectLanguage('test.md')).toBe('unknown');
      expect(ingester.detectLanguage('test')).toBe('unknown');
    });
  });

  describe('file filtering', () => {
    test('should include supported language files', () => {
      const jsFile = { path: 'test.js', size: 1000, language: 'javascript' };
      const pyFile = { path: 'test.py', size: 1000, language: 'python' };
      
      expect(ingester.shouldProcessFile(jsFile)).toBe(true);
      expect(ingester.shouldProcessFile(pyFile)).toBe(true);
    });

    test('should exclude unsupported language files', () => {
      const txtFile = { path: 'test.txt', size: 1000, language: 'unknown' };
      
      expect(ingester.shouldProcessFile(txtFile)).toBe(false);
    });

    test('should exclude files that are too large', () => {
      const largeFile = { 
        path: 'large.js', 
        size: 2 * 1024 * 1024, // 2MB, larger than 1MB limit
        language: 'javascript' 
      };
      
      expect(ingester.shouldProcessFile(largeFile)).toBe(false);
    });

    test('should exclude files matching exclude patterns', () => {
      const nodeModulesFile = { 
        path: 'node_modules/package/index.js', 
        size: 1000, 
        language: 'javascript' 
      };
      const gitFile = { 
        path: '.git/config', 
        size: 1000, 
        language: 'unknown' 
      };
      
      expect(ingester.shouldProcessFile(nodeModulesFile)).toBe(false);
      expect(ingester.shouldProcessFile(gitFile)).toBe(false);
    });
  });

  describe('pattern matching', () => {
    test('should match simple patterns', () => {
      expect(ingester.matchesPattern('test.js', '*.js')).toBe(true);
      expect(ingester.matchesPattern('test.py', '*.js')).toBe(false);
    });

    test('should match directory patterns', () => {
      expect(ingester.matchesPattern('node_modules/test.js', 'node_modules/**')).toBe(true);
      expect(ingester.matchesPattern('src/node_modules/test.js', 'node_modules/**')).toBe(false);
    });

    test('should match substring patterns', () => {
      expect(ingester.matchesPattern('test.spec.js', '.spec.')).toBe(true);
      expect(ingester.matchesPattern('test.js', '.spec.')).toBe(false);
    });
  });

  describe('repository information', () => {
    beforeEach(async () => {
      fs.stat.mockResolvedValue({ isDirectory: () => true });
      mockGit.checkIsRepo.mockResolvedValue(true);
      await ingester.initialize();
    });

    test('should get repository information', async () => {
      mockGit.status.mockResolvedValue({
        ahead: 0,
        behind: 0,
        staged: [],
        modified: ['test.js'],
        created: ['new.js'],
        deleted: []
      });

      mockGit.getRemotes.mockResolvedValue([
        { name: 'origin', refs: { fetch: '**************:user/repo.git' } }
      ]);

      mockGit.branch.mockResolvedValue({
        current: 'main',
        all: ['main', 'develop']
      });

      mockGit.log.mockResolvedValue({
        all: [
          {
            hash: 'abc123',
            date: '2024-01-01',
            author_name: 'Test Author',
            message: 'Test commit'
          }
        ]
      });

      const info = await ingester.getRepositoryInfo();
      
      expect(info.path).toBe(testRepoPath);
      expect(info.currentBranch).toBe('main');
      expect(info.branches).toContain('main');
      expect(info.branches).toContain('develop');
      expect(info.status.modified).toContain('test.js');
      expect(info.status.created).toContain('new.js');
      expect(info.recentCommits).toHaveLength(1);
    });
  });

  describe('error handling', () => {
    test('should handle git command failures gracefully', async () => {
      fs.stat.mockResolvedValue({ isDirectory: () => true });
      mockGit.checkIsRepo.mockResolvedValue(true);
      await ingester.initialize();

      mockGit.raw.mockRejectedValue(new Error('Git command failed'));

      await expect(ingester.getFiles()).rejects.toThrow('Git command failed');
    });

    test('should handle file read failures gracefully', async () => {
      fs.stat.mockResolvedValue({ isDirectory: () => true });
      mockGit.checkIsRepo.mockResolvedValue(true);
      await ingester.initialize();

      const mockFiles = 'src/index.js';
      mockGit.raw.mockResolvedValue(mockFiles);
      fs.stat.mockRejectedValue(new Error('File not found'));

      const files = await ingester.getFiles();
      
      expect(files).toHaveLength(0); // Should skip files that can't be read
    });
  });
});
