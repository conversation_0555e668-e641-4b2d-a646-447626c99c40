export interface PlanningTask {
  id: string
  title: string
  completed: boolean
}

export interface Question {
  id: string
  question: string
  type: "text" | "textarea"
  placeholder?: string
  optional?: boolean
}

export interface ProjectContext {
  originalPrompt: string
  projectType: string
  features: string[]
  clarifications: Record<string, any>
  summary: string
  techStack: Record<string, any>
  prd: Record<string, any>
  wireframes: any[]
  filesystem: Record<string, any>
  workflow: Record<string, any>
  tasks: any[]
}

export interface ModuleProps {
  context?: ProjectContext
  prompt?: string
  onComplete: (data: any) => void
}
