import express from 'express';
import { createLogger } from '../../utils/logger.js';

const router = express.Router();
const logger = createLogger('HealthRoutes');

/**
 * Basic health check endpoint
 * GET /api/health
 */
router.get('/', async (req, res) => {
  try {
    const contextEngine = req.app.get('contextEngine');
    
    if (!contextEngine) {
      return res.status(503).json({
        status: 'unhealthy',
        message: 'Context engine not available',
        timestamp: new Date().toISOString(),
        version: process.env.VERSION || '1.0.0'
      });
    }

    const health = await contextEngine.getHealth();
    const statusCode = health.status === 'healthy' ? 200 : 
                      health.status === 'degraded' ? 200 : 503;

    res.status(statusCode).json({
      ...health,
      version: process.env.VERSION || '1.0.0',
      environment: process.env.NODE_ENV || 'development'
    });

  } catch (error) {
    logger.error('Health check failed', { error: error.message });
    
    res.status(503).json({
      status: 'unhealthy',
      message: 'Health check failed',
      error: error.message,
      timestamp: new Date().toISOString(),
      version: process.env.VERSION || '1.0.0'
    });
  }
});

/**
 * Detailed health check endpoint
 * GET /api/health/detailed
 */
router.get('/detailed', async (req, res) => {
  try {
    const contextEngine = req.app.get('contextEngine');
    
    if (!contextEngine) {
      return res.status(503).json({
        status: 'unhealthy',
        message: 'Context engine not available',
        components: {},
        timestamp: new Date().toISOString()
      });
    }

    const health = await contextEngine.getHealth();
    const statistics = await contextEngine.getStatistics();

    const detailedHealth = {
      ...health,
      statistics,
      system: {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        memory: process.memoryUsage(),
        uptime: process.uptime()
      },
      environment: {
        nodeEnv: process.env.NODE_ENV || 'development',
        version: process.env.VERSION || '1.0.0'
      }
    };

    const statusCode = health.status === 'healthy' ? 200 : 
                      health.status === 'degraded' ? 200 : 503;

    res.status(statusCode).json(detailedHealth);

  } catch (error) {
    logger.error('Detailed health check failed', { error: error.message });
    
    res.status(503).json({
      status: 'unhealthy',
      message: 'Detailed health check failed',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Readiness probe endpoint
 * GET /api/health/ready
 */
router.get('/ready', async (req, res) => {
  try {
    const contextEngine = req.app.get('contextEngine');
    
    if (!contextEngine || !contextEngine.isInitialized) {
      return res.status(503).json({
        ready: false,
        message: 'Context engine not ready',
        timestamp: new Date().toISOString()
      });
    }

    // Check if all critical components are ready
    const health = await contextEngine.getHealth();
    const isReady = health.status === 'healthy' && 
                   health.components.neo4j?.status === 'healthy';

    const statusCode = isReady ? 200 : 503;

    res.status(statusCode).json({
      ready: isReady,
      status: health.status,
      components: health.components,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Readiness check failed', { error: error.message });
    
    res.status(503).json({
      ready: false,
      message: 'Readiness check failed',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Liveness probe endpoint
 * GET /api/health/live
 */
router.get('/live', (req, res) => {
  // Simple liveness check - if we can respond, we're alive
  res.json({
    alive: true,
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

/**
 * Component-specific health checks
 * GET /api/health/components/:component
 */
router.get('/components/:component', async (req, res) => {
  try {
    const { component } = req.params;
    const contextEngine = req.app.get('contextEngine');
    
    if (!contextEngine) {
      return res.status(503).json({
        component,
        status: 'unavailable',
        message: 'Context engine not available',
        timestamp: new Date().toISOString()
      });
    }

    const health = await contextEngine.getHealth();
    const componentHealth = health.components[component];

    if (!componentHealth) {
      return res.status(404).json({
        component,
        status: 'not_found',
        message: `Component '${component}' not found`,
        availableComponents: Object.keys(health.components),
        timestamp: new Date().toISOString()
      });
    }

    const statusCode = componentHealth.status === 'healthy' ? 200 : 503;

    res.status(statusCode).json({
      component,
      ...componentHealth,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Component health check failed', {
      component: req.params.component,
      error: error.message
    });
    
    res.status(503).json({
      component: req.params.component,
      status: 'error',
      message: 'Component health check failed',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Database connectivity check
 * GET /api/health/database
 */
router.get('/database', async (req, res) => {
  try {
    const contextEngine = req.app.get('contextEngine');
    
    if (!contextEngine || !contextEngine.neo4jClient) {
      return res.status(503).json({
        database: 'neo4j',
        status: 'unavailable',
        message: 'Neo4j client not available',
        timestamp: new Date().toISOString()
      });
    }

    const dbHealth = await contextEngine.neo4jClient.healthCheck();
    const statusCode = dbHealth.status === 'healthy' ? 200 : 503;

    res.status(statusCode).json({
      database: 'neo4j',
      ...dbHealth,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Database health check failed', { error: error.message });
    
    res.status(503).json({
      database: 'neo4j',
      status: 'error',
      message: 'Database health check failed',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Performance metrics endpoint
 * GET /api/health/metrics
 */
router.get('/metrics', async (req, res) => {
  try {
    const contextEngine = req.app.get('contextEngine');
    
    if (!contextEngine) {
      return res.status(503).json({
        message: 'Context engine not available',
        timestamp: new Date().toISOString()
      });
    }

    const statistics = await contextEngine.getStatistics();
    const memoryUsage = process.memoryUsage();
    
    const metrics = {
      system: {
        uptime: process.uptime(),
        memory: {
          rss: memoryUsage.rss,
          heapTotal: memoryUsage.heapTotal,
          heapUsed: memoryUsage.heapUsed,
          external: memoryUsage.external,
          arrayBuffers: memoryUsage.arrayBuffers
        },
        cpu: process.cpuUsage()
      },
      application: {
        ...statistics,
        version: process.env.VERSION || '1.0.0',
        environment: process.env.NODE_ENV || 'development'
      },
      timestamp: new Date().toISOString()
    };

    res.json(metrics);

  } catch (error) {
    logger.error('Metrics collection failed', { error: error.message });
    
    res.status(500).json({
      message: 'Metrics collection failed',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

export default router;
