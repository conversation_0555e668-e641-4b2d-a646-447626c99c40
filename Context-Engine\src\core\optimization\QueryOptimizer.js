import { createLogger } from '../../utils/logger.js';

const logger = createLogger('QueryOptimizer');

/**
 * Query optimizer for improving performance of graph and vector queries
 */
export class QueryOptimizer {
  constructor(config) {
    this.config = config;
    this.queryCache = new Map();
    this.queryStats = new Map();
    this.isInitialized = false;
  }

  /**
   * Initialize the query optimizer
   */
  async initialize() {
    try {
      logger.info('Initializing query optimizer');
      
      // Initialize query cache with TTL
      this.setupQueryCache();
      
      // Initialize query statistics tracking
      this.setupQueryStats();
      
      this.isInitialized = true;
      logger.info('Query optimizer initialized successfully');
      
    } catch (error) {
      logger.error('Failed to initialize query optimizer', { error: error.message });
      throw error;
    }
  }

  /**
   * Setup query cache with TTL support
   */
  setupQueryCache() {
    const cacheConfig = this.config.cache || {
      maxSize: 1000,
      ttl: 300000 // 5 minutes
    };

    this.queryCache = new Map();
    this.cacheMetadata = new Map();
    this.maxCacheSize = cacheConfig.maxSize;
    this.cacheTTL = cacheConfig.ttl;

    // Setup cache cleanup interval
    this.cacheCleanupInterval = setInterval(() => {
      this.cleanupExpiredCache();
    }, 60000); // Clean every minute
  }

  /**
   * Setup query statistics tracking
   */
  setupQueryStats() {
    this.queryStats = new Map();
    this.performanceMetrics = {
      totalQueries: 0,
      cacheHits: 0,
      cacheMisses: 0,
      avgQueryTime: 0,
      slowQueries: []
    };
  }

  /**
   * Optimize a Cypher query for Neo4j
   */
  optimizeCypherQuery(query, parameters = {}) {
    logger.debug('Optimizing Cypher query', { 
      query: query.substring(0, 100),
      parameterCount: Object.keys(parameters).length 
    });

    let optimizedQuery = query;

    // Apply various optimization techniques
    optimizedQuery = this.addIndexHints(optimizedQuery);
    optimizedQuery = this.optimizeWhereClause(optimizedQuery);
    optimizedQuery = this.optimizeJoins(optimizedQuery);
    optimizedQuery = this.addLimitOptimization(optimizedQuery);

    return {
      query: optimizedQuery,
      parameters,
      optimizations: this.getAppliedOptimizations(query, optimizedQuery)
    };
  }

  /**
   * Add index hints to improve query performance
   */
  addIndexHints(query) {
    // Add USING INDEX hints for common patterns
    let optimized = query;

    // File path lookups
    if (query.includes('f:File') && query.includes('f.path')) {
      optimized = optimized.replace(
        /(MATCH\s+\([^)]*f:File[^)]*\))/i,
        '$1 USING INDEX f:File(path)'
      );
    }

    // Function name lookups
    if (query.includes('fn:Function') && query.includes('fn.name')) {
      optimized = optimized.replace(
        /(MATCH\s+\([^)]*fn:Function[^)]*\))/i,
        '$1 USING INDEX fn:Function(name)'
      );
    }

    // Class name lookups
    if (query.includes('c:Class') && query.includes('c.name')) {
      optimized = optimized.replace(
        /(MATCH\s+\([^)]*c:Class[^)]*\))/i,
        '$1 USING INDEX c:Class(name)'
      );
    }

    return optimized;
  }

  /**
   * Optimize WHERE clauses for better performance
   */
  optimizeWhereClause(query) {
    let optimized = query;

    // Move most selective conditions first
    if (query.includes('WHERE')) {
      // This is a simplified optimization - in practice, you'd analyze
      // the query plan and statistics to determine selectivity
      optimized = this.reorderWhereConditions(optimized);
    }

    // Convert CONTAINS to more efficient patterns when possible
    optimized = optimized.replace(
      /(\w+)\.(\w+)\s+CONTAINS\s+\$(\w+)/g,
      '$1.$2 =~ (".*" + $$$3 + ".*")'
    );

    return optimized;
  }

  /**
   * Optimize joins and relationship traversals
   */
  optimizeJoins(query) {
    let optimized = query;

    // Prefer specific relationship types over generic ones
    optimized = optimized.replace(
      /-\[r\]->/g,
      '-[r:CONTAINS|DEFINES|CALLS]->'
    );

    // Add relationship direction hints when beneficial
    if (query.includes('MATCH') && query.includes('-[') && !query.includes('->')) {
      // Add direction hints for common patterns
      optimized = optimized.replace(
        /-\[([^]]+):CONTAINS\]-/g,
        '-[$1:CONTAINS]->'
      );
    }

    return optimized;
  }

  /**
   * Add LIMIT optimization for large result sets
   */
  addLimitOptimization(query) {
    // If no LIMIT is specified and query could return many results, add one
    if (!query.includes('LIMIT') && !query.includes('COUNT')) {
      if (query.includes('MATCH') && query.includes('RETURN')) {
        return query + ' LIMIT 1000';
      }
    }

    return query;
  }

  /**
   * Reorder WHERE conditions for optimal performance
   */
  reorderWhereConditions(query) {
    // This is a simplified implementation
    // In practice, you'd use query statistics to determine optimal order
    
    const whereMatch = query.match(/WHERE\s+(.+?)(?=\s+RETURN|\s+ORDER|\s+LIMIT|$)/i);
    if (!whereMatch) return query;

    const conditions = whereMatch[1].split(/\s+AND\s+/i);
    
    // Sort conditions by estimated selectivity (most selective first)
    const sortedConditions = conditions.sort((a, b) => {
      const selectivityA = this.estimateSelectivity(a);
      const selectivityB = this.estimateSelectivity(b);
      return selectivityA - selectivityB;
    });

    return query.replace(whereMatch[0], `WHERE ${sortedConditions.join(' AND ')}`);
  }

  /**
   * Estimate selectivity of a WHERE condition
   */
  estimateSelectivity(condition) {
    // Simple heuristics for condition selectivity
    if (condition.includes('=')) return 0.1; // Equality is very selective
    if (condition.includes('CONTAINS')) return 0.3; // Contains is moderately selective
    if (condition.includes('>') || condition.includes('<')) return 0.5; // Range queries
    return 0.8; // Default for other conditions
  }

  /**
   * Get applied optimizations for logging
   */
  getAppliedOptimizations(original, optimized) {
    const optimizations = [];
    
    if (optimized.includes('USING INDEX') && !original.includes('USING INDEX')) {
      optimizations.push('index_hints');
    }
    
    if (optimized.includes('LIMIT') && !original.includes('LIMIT')) {
      optimizations.push('auto_limit');
    }
    
    if (optimized !== original) {
      optimizations.push('query_rewrite');
    }
    
    return optimizations;
  }

  /**
   * Cache query results
   */
  cacheQuery(queryKey, result) {
    if (!this.config.cacheEnabled) return;

    // Implement LRU eviction if cache is full
    if (this.queryCache.size >= this.maxCacheSize) {
      this.evictOldestCacheEntry();
    }

    this.queryCache.set(queryKey, result);
    this.cacheMetadata.set(queryKey, {
      timestamp: Date.now(),
      accessCount: 1,
      lastAccessed: Date.now()
    });

    this.performanceMetrics.cacheMisses++;
  }

  /**
   * Get cached query result
   */
  getCachedQuery(queryKey) {
    if (!this.config.cacheEnabled) return null;

    const result = this.queryCache.get(queryKey);
    if (result) {
      const metadata = this.cacheMetadata.get(queryKey);
      if (metadata && Date.now() - metadata.timestamp < this.cacheTTL) {
        // Update access statistics
        metadata.accessCount++;
        metadata.lastAccessed = Date.now();
        this.performanceMetrics.cacheHits++;
        return result;
      } else {
        // Expired entry
        this.queryCache.delete(queryKey);
        this.cacheMetadata.delete(queryKey);
      }
    }

    return null;
  }

  /**
   * Generate cache key for query
   */
  generateCacheKey(query, parameters) {
    const paramString = JSON.stringify(parameters, Object.keys(parameters).sort());
    return `${query}:${paramString}`;
  }

  /**
   * Clean up expired cache entries
   */
  cleanupExpiredCache() {
    const now = Date.now();
    const expiredKeys = [];

    for (const [key, metadata] of this.cacheMetadata.entries()) {
      if (now - metadata.timestamp > this.cacheTTL) {
        expiredKeys.push(key);
      }
    }

    for (const key of expiredKeys) {
      this.queryCache.delete(key);
      this.cacheMetadata.delete(key);
    }

    if (expiredKeys.length > 0) {
      logger.debug('Cleaned up expired cache entries', { count: expiredKeys.length });
    }
  }

  /**
   * Evict oldest cache entry (LRU)
   */
  evictOldestCacheEntry() {
    let oldestKey = null;
    let oldestTime = Date.now();

    for (const [key, metadata] of this.cacheMetadata.entries()) {
      if (metadata.lastAccessed < oldestTime) {
        oldestTime = metadata.lastAccessed;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.queryCache.delete(oldestKey);
      this.cacheMetadata.delete(oldestKey);
    }
  }

  /**
   * Track query performance
   */
  trackQueryPerformance(query, duration, resultCount) {
    this.performanceMetrics.totalQueries++;
    
    // Update average query time
    const totalTime = this.performanceMetrics.avgQueryTime * (this.performanceMetrics.totalQueries - 1);
    this.performanceMetrics.avgQueryTime = (totalTime + duration) / this.performanceMetrics.totalQueries;

    // Track slow queries
    if (duration > 1000) { // Queries taking more than 1 second
      this.performanceMetrics.slowQueries.push({
        query: query.substring(0, 100),
        duration,
        resultCount,
        timestamp: Date.now()
      });

      // Keep only last 100 slow queries
      if (this.performanceMetrics.slowQueries.length > 100) {
        this.performanceMetrics.slowQueries.shift();
      }
    }

    // Update query statistics
    const queryType = this.classifyQuery(query);
    if (!this.queryStats.has(queryType)) {
      this.queryStats.set(queryType, {
        count: 0,
        totalTime: 0,
        avgTime: 0,
        maxTime: 0,
        minTime: Infinity
      });
    }

    const stats = this.queryStats.get(queryType);
    stats.count++;
    stats.totalTime += duration;
    stats.avgTime = stats.totalTime / stats.count;
    stats.maxTime = Math.max(stats.maxTime, duration);
    stats.minTime = Math.min(stats.minTime, duration);
  }

  /**
   * Classify query type for statistics
   */
  classifyQuery(query) {
    const lowerQuery = query.toLowerCase();
    
    if (lowerQuery.includes('match') && lowerQuery.includes('return')) {
      if (lowerQuery.includes('function')) return 'function_search';
      if (lowerQuery.includes('class')) return 'class_search';
      if (lowerQuery.includes('file')) return 'file_search';
      if (lowerQuery.includes('variable')) return 'variable_search';
      return 'general_search';
    }
    
    if (lowerQuery.includes('create')) return 'create_operation';
    if (lowerQuery.includes('merge')) return 'merge_operation';
    if (lowerQuery.includes('delete')) return 'delete_operation';
    
    return 'other';
  }

  /**
   * Get performance statistics
   */
  getPerformanceStats() {
    return {
      ...this.performanceMetrics,
      cacheSize: this.queryCache.size,
      cacheHitRate: this.performanceMetrics.totalQueries > 0 
        ? this.performanceMetrics.cacheHits / this.performanceMetrics.totalQueries 
        : 0,
      queryTypeStats: Object.fromEntries(this.queryStats)
    };
  }

  /**
   * Clear all caches and reset statistics
   */
  reset() {
    this.queryCache.clear();
    this.cacheMetadata.clear();
    this.queryStats.clear();
    this.performanceMetrics = {
      totalQueries: 0,
      cacheHits: 0,
      cacheMisses: 0,
      avgQueryTime: 0,
      slowQueries: []
    };
  }

  /**
   * Shutdown the optimizer
   */
  shutdown() {
    if (this.cacheCleanupInterval) {
      clearInterval(this.cacheCleanupInterval);
    }
    this.reset();
  }
}

export default QueryOptimizer;
