import type { ProjectContext } from "@/types/planning"
import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Misalignment } from "./cross-reference-validator"

/**
 * Tech Stack PRD Alignment Checker
 * Ensures tech stack choices align with PRD requirements
 */
export class TechStackPRDAlignment<PERSON>hecker implements AlignmentChecker {
  id = 'techstack-prd-alignment'
  description = 'Checks alignment between tech stack and PRD requirements'

  async check(context: ProjectContext): Promise<AlignmentResult> {
    const misalignments: Misalignment[] = []
    const techStack = context.techStack as any
    const prd = context.prd as any

    if (!techStack || !prd) {
      return { isAligned: true, misalignments }
    }

    // Check performance requirements vs tech stack
    if (prd.performance && techStack.frontend) {
      const performanceReqs = JSON.stringify(prd.performance).toLowerCase()
      const frontend = techStack.frontend.toLowerCase()

      if (performanceReqs.includes('high performance') && frontend.includes('jquery')) {
        misalignments.push({
          fromStep: 'prd',
          toStep: 'techstack',
          field: 'performance',
          message: 'High performance requirements may not be met with jQuery',
          severity: 'medium',
          suggestedFix: 'Consider React, Vue, or Angular for better performance'
        })
      }
    }

    // Check scalability requirements vs tech stack
    if (prd.scalability && techStack.backend) {
      const scalabilityReqs = JSON.stringify(prd.scalability).toLowerCase()
      const backend = techStack.backend.toLowerCase()

      if (scalabilityReqs.includes('high scalability') && backend.includes('php') && !backend.includes('laravel')) {
        misalignments.push({
          fromStep: 'prd',
          toStep: 'techstack',
          field: 'scalability',
          message: 'High scalability requirements may need framework support',
          severity: 'medium',
          suggestedFix: 'Consider Laravel, Node.js, or microservices architecture'
        })
      }
    }

    // Check real-time requirements vs tech stack
    if (prd.features && techStack.backend) {
      const features = Array.isArray(prd.features) ? prd.features.join(' ') : JSON.stringify(prd.features)
      const hasRealTime = features.toLowerCase().includes('real-time') || features.toLowerCase().includes('live')
      
      if (hasRealTime && !JSON.stringify(techStack).toLowerCase().includes('websocket') && 
          !JSON.stringify(techStack).toLowerCase().includes('socket.io')) {
        misalignments.push({
          fromStep: 'prd',
          toStep: 'techstack',
          field: 'real-time',
          message: 'Real-time features require WebSocket or similar technology',
          severity: 'high',
          suggestedFix: 'Add WebSocket support or Socket.io to tech stack'
        })
      }
    }

    return { isAligned: misalignments.length === 0, misalignments }
  }
}

/**
 * Wireframe PRD Alignment Checker
 * Ensures wireframes align with PRD user stories and requirements
 */
export class WireframePRDAlignmentChecker implements AlignmentChecker {
  id = 'wireframe-prd-alignment'
  description = 'Checks alignment between wireframes and PRD requirements'

  async check(context: ProjectContext): Promise<AlignmentResult> {
    const misalignments: Misalignment[] = []
    const wireframes = context.wireframes
    const prd = context.prd as any

    if (!wireframes || !prd) {
      return { isAligned: true, misalignments }
    }

    // Check if wireframes cover all user stories
    if (prd.userStories && wireframes.length > 0) {
      const wireframeContent = JSON.stringify(wireframes).toLowerCase()
      const userStories = Array.isArray(prd.userStories) ? prd.userStories : []

      // Look for key UI elements mentioned in user stories
      const uiElements = ['login', 'dashboard', 'profile', 'settings', 'search', 'form', 'list', 'detail']
      
      for (const element of uiElements) {
        const mentionedInStories = userStories.some((story: string) => 
          story.toLowerCase().includes(element)
        )
        const coveredInWireframes = wireframeContent.includes(element)

        if (mentionedInStories && !coveredInWireframes) {
          misalignments.push({
            fromStep: 'prd',
            toStep: 'wireframes',
            field: element,
            message: `${element} mentioned in user stories but not in wireframes`,
            severity: 'medium',
            suggestedFix: `Add ${element} wireframe or component`
          })
        }
      }
    }

    // Check navigation flow consistency
    if (prd.userFlow && wireframes.length > 0) {
      const userFlow = JSON.stringify(prd.userFlow).toLowerCase()
      const wireframeFlow = JSON.stringify(wireframes).toLowerCase()

      if (userFlow.includes('multi-step') && !wireframeFlow.includes('step')) {
        misalignments.push({
          fromStep: 'prd',
          toStep: 'wireframes',
          field: 'navigation',
          message: 'Multi-step user flow not reflected in wireframes',
          severity: 'medium',
          suggestedFix: 'Add step indicators or multi-page wireframes'
        })
      }
    }

    return { isAligned: misalignments.length === 0, misalignments }
  }
}

/**
 * Database PRD Alignment Checker
 * Ensures database schema supports all PRD requirements
 */
export class DatabasePRDAlignmentChecker implements AlignmentChecker {
  id = 'database-prd-alignment'
  description = 'Checks alignment between database schema and PRD requirements'

  async check(context: ProjectContext): Promise<AlignmentResult> {
    const misalignments: Misalignment[] = []
    const database = context.database as any
    const prd = context.prd as any

    if (!database || !prd) {
      return { isAligned: true, misalignments }
    }

    // Check if database supports user management features
    if (prd.features && database.tables) {
      const features = Array.isArray(prd.features) ? prd.features.join(' ') : JSON.stringify(prd.features)
      const hasUserFeatures = features.toLowerCase().includes('user') || features.toLowerCase().includes('auth')
      const hasUserTable = JSON.stringify(database.tables).toLowerCase().includes('user')

      if (hasUserFeatures && !hasUserTable) {
        misalignments.push({
          fromStep: 'prd',
          toStep: 'database',
          field: 'user-management',
          message: 'User features required but no user table in database schema',
          severity: 'high',
          suggestedFix: 'Add user/account table to database schema'
        })
      }
    }

    // Check for audit trail requirements
    if (prd.compliance && database.tables) {
      const compliance = JSON.stringify(prd.compliance).toLowerCase()
      const hasAuditReq = compliance.includes('audit') || compliance.includes('log')
      const hasAuditTable = JSON.stringify(database.tables).toLowerCase().includes('audit') ||
                           JSON.stringify(database.tables).toLowerCase().includes('log')

      if (hasAuditReq && !hasAuditTable) {
        misalignments.push({
          fromStep: 'prd',
          toStep: 'database',
          field: 'audit-trail',
          message: 'Audit requirements specified but no audit tables in schema',
          severity: 'high',
          suggestedFix: 'Add audit/logging tables to database schema'
        })
      }
    }

    return { isAligned: misalignments.length === 0, misalignments }
  }
}

/**
 * File System Tech Stack Alignment Checker
 * Ensures file system structure aligns with tech stack conventions
 */
export class FileSystemTechStackAlignmentChecker implements AlignmentChecker {
  id = 'filesystem-techstack-alignment'
  description = 'Checks alignment between file system and tech stack conventions'

  async check(context: ProjectContext): Promise<AlignmentResult> {
    const misalignments: Misalignment[] = []
    const filesystem = context.filesystem as any
    const techStack = context.techStack as any

    if (!filesystem || !techStack) {
      return { isAligned: true, misalignments }
    }

    const filesystemStr = JSON.stringify(filesystem).toLowerCase()

    // Check React/Next.js conventions
    if (techStack.frontend && techStack.frontend.toLowerCase().includes('react')) {
      if (!filesystemStr.includes('components') && !filesystemStr.includes('src')) {
        misalignments.push({
          fromStep: 'techstack',
          toStep: 'filesystem',
          field: 'react-structure',
          message: 'React project should have components or src directory',
          severity: 'medium',
          suggestedFix: 'Add components/ and src/ directories'
        })
      }
    }

    // Check Node.js backend conventions
    if (techStack.backend && techStack.backend.toLowerCase().includes('node')) {
      if (!filesystemStr.includes('package.json')) {
        misalignments.push({
          fromStep: 'techstack',
          toStep: 'filesystem',
          field: 'nodejs-structure',
          message: 'Node.js project should have package.json',
          severity: 'high',
          suggestedFix: 'Add package.json to project root'
        })
      }
    }

    // Check for environment configuration
    if (techStack.hosting && !filesystemStr.includes('.env')) {
      misalignments.push({
        fromStep: 'techstack',
        toStep: 'filesystem',
        field: 'environment-config',
        message: 'Hosted applications should have environment configuration',
        severity: 'medium',
        suggestedFix: 'Add .env.example and environment configuration files'
      })
    }

    return { isAligned: misalignments.length === 0, misalignments }
  }
}

/**
 * Workflow Requirements Alignment Checker
 * Ensures workflow logic supports all requirements
 */
export class WorkflowRequirementsAlignmentChecker implements AlignmentChecker {
  id = 'workflow-requirements-alignment'
  description = 'Checks alignment between workflow and requirements'

  async check(context: ProjectContext): Promise<AlignmentResult> {
    const misalignments: Misalignment[] = []
    const workflow = context.workflow as any
    const prd = context.prd as any

    if (!workflow || !prd) {
      return { isAligned: true, misalignments }
    }

    // Check if workflow covers all business processes
    if (prd.businessProcesses && workflow.processes) {
      const businessProcesses = Array.isArray(prd.businessProcesses) ? 
        prd.businessProcesses : [prd.businessProcesses]
      const workflowProcesses = JSON.stringify(workflow.processes).toLowerCase()

      for (const process of businessProcesses) {
        if (!workflowProcesses.includes(process.toLowerCase())) {
          misalignments.push({
            fromStep: 'prd',
            toStep: 'workflow',
            field: 'business-process',
            message: `Business process "${process}" not covered in workflow`,
            severity: 'medium',
            suggestedFix: `Add workflow for ${process}`
          })
        }
      }
    }

    return { isAligned: misalignments.length === 0, misalignments }
  }
}

/**
 * Tasks Implementation Alignment Checker
 * Ensures implementation tasks cover all requirements
 */
export class TasksImplementationAlignmentChecker implements AlignmentChecker {
  id = 'tasks-implementation-alignment'
  description = 'Checks alignment between implementation tasks and all requirements'

  async check(context: ProjectContext): Promise<AlignmentResult> {
    const misalignments: Misalignment[] = []
    const tasks = context.tasks as any
    const prd = context.prd as any
    const techStack = context.techStack as any

    if (!tasks || !prd) {
      return { isAligned: true, misalignments }
    }

    const taskContent = JSON.stringify(tasks).toLowerCase()

    // Check if all PRD features have corresponding tasks
    if (prd.features) {
      const features = Array.isArray(prd.features) ? prd.features : [prd.features]
      
      for (const feature of features) {
        if (!taskContent.includes(feature.toLowerCase())) {
          misalignments.push({
            fromStep: 'prd',
            toStep: 'tasks',
            field: 'feature-implementation',
            message: `Feature "${feature}" not covered in implementation tasks`,
            severity: 'high',
            suggestedFix: `Add implementation tasks for ${feature}`
          })
        }
      }
    }

    // Check if tech stack setup is included in tasks
    if (techStack && tasks.tasks) {
      const hasSetupTask = tasks.tasks.some((task: any) => 
        (task.title || task.description || '').toLowerCase().includes('setup') ||
        (task.title || task.description || '').toLowerCase().includes('install')
      )

      if (!hasSetupTask) {
        misalignments.push({
          fromStep: 'techstack',
          toStep: 'tasks',
          field: 'setup-tasks',
          message: 'Tech stack setup not included in implementation tasks',
          severity: 'medium',
          suggestedFix: 'Add project setup and dependency installation tasks'
        })
      }
    }

    return { isAligned: misalignments.length === 0, misalignments }
  }
}
