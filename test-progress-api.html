<!DOCTYPE html>
<html>
<head>
    <title>Test Progress API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        button { padding: 10px 20px; margin: 5px; }
    </style>
</head>
<body>
    <h1>🧪 Test Async Orchestration Integration</h1>
    
    <div>
        <button onclick="testPlanningAPI()">Test Planning API</button>
        <button onclick="testProgressAPI()">Test Progress API</button>
        <button onclick="testStepAPI()">Test Step API</button>
        <button onclick="clearLogs()">Clear Logs</button>
    </div>
    
    <div id="logs"></div>

    <script>
        function log(message, type = 'log') {
            const logs = document.getElementById('logs');
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            logs.appendChild(div);
            logs.scrollTop = logs.scrollHeight;
        }

        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }

        async function testPlanningAPI() {
            log('🚀 Testing Planning API...', 'log');
            
            try {
                const response = await fetch('/api/planning', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        prompt: 'Test: Build a simple todo app',
                        isInteractive: false,
                        answers: {}
                    })
                });

                const result = await response.json();
                
                if (response.ok) {
                    log(`✅ Planning API Success!`, 'success');
                    log(`📊 Session ID: ${result.sessionId}`, 'success');
                    log(`📈 Progress: ${result.progress}%`, 'success');
                    log(`🔧 Has results: ${!!result.results}`, 'success');
                    
                    // Store session ID for other tests
                    window.testSessionId = result.sessionId;
                } else {
                    log(`❌ Planning API Error: ${result.error}`, 'error');
                }
            } catch (error) {
                log(`❌ Planning API Exception: ${error.message}`, 'error');
            }
        }

        async function testProgressAPI() {
            if (!window.testSessionId) {
                log('⚠️ No session ID. Run Planning API test first.', 'error');
                return;
            }

            log(`🔍 Testing Progress API for session: ${window.testSessionId}`, 'log');
            
            try {
                const response = await fetch(`/api/planning/progress?sessionId=${window.testSessionId}`);
                const result = await response.json();
                
                if (response.ok) {
                    log(`✅ Progress API Success!`, 'success');
                    log(`📊 Progress: ${result.progress}%`, 'success');
                    log(`🎯 Current Step: ${result.currentStep || 'none'}`, 'success');
                    log(`✅ Completed Steps: ${result.completedSteps?.length || 0}`, 'success');
                } else {
                    log(`❌ Progress API Error: ${result.error}`, 'error');
                }
            } catch (error) {
                log(`❌ Progress API Exception: ${error.message}`, 'error');
            }
        }

        async function testStepAPI() {
            if (!window.testSessionId) {
                log('⚠️ No session ID. Run Planning API test first.', 'error');
                return;
            }

            log(`🔧 Testing Step API for session: ${window.testSessionId}`, 'log');
            
            try {
                const response = await fetch('/api/planning/step', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        step: 'clarify',
                        context: {
                            sessionId: window.testSessionId,
                            prompt: 'Test: Build a simple todo app',
                            isInteractive: false,
                            userAnswers: {},
                            results: { analyze: { test: true } }
                        }
                    })
                });

                const result = await response.json();
                
                if (response.ok) {
                    log(`✅ Step API Success!`, 'success');
                    log(`📊 Session ID: ${result.sessionId}`, 'success');
                    log(`🔧 Has results: ${!!result.results}`, 'success');
                } else {
                    log(`❌ Step API Error: ${result.error}`, 'error');
                }
            } catch (error) {
                log(`❌ Step API Exception: ${error.message}`, 'error');
            }
        }

        // Auto-run tests on load
        window.onload = function() {
            log('🧪 Progress API Test Page Loaded', 'log');
            log('Click buttons above to test the integration', 'log');
        };
    </script>
</body>
</html>
