/**
 * Planning Module - Type Definitions
 * Clean separation of concerns with Zod validation
 */

import { z } from 'zod'

// Core Planning Types
export const PlanningStepSchema = z.enum([
  'analyze',
  'clarify', 
  'summary',
  'techstack',
  'prd',
  'context-profile',
  'wireframes',
  'design',
  'database',
  'filesystem',
  'workflow',
  'tasks',
  'scaffold'
])

export const PlanningStateSchema = z.object({
  prompt: z.string(),
  isInteractive: z.boolean().default(false),
  userAnswers: z.record(z.any()).default({}),
  currentStep: PlanningStepSchema.optional(),
  results: z.record(z.any()).default({}),
  completed: z.boolean().default(false),
  needsInput: z.boolean().default(false),
  question: z.object({
    id: z.string(),
    question: z.string(),
    type: z.enum(['text', 'textarea']),
    placeholder: z.string().optional(),
    optional: z.boolean().optional()
  }).optional(),
  contextIntegrity: z.any().optional(),
  crossReferenceValidation: z.any().optional(),
  contextMetadata: z.any().optional()
})

export const MCPContextSchema = z.object({
  enhancedContext: z.object({
    enrichments: z.array(z.object({
      title: z.string(),
      description: z.string(),
      impact: z.string()
    }))
  }).optional(),
  sequentialThought: z.any().optional(),
  relevantDocs: z.record(z.any()).optional(),
  step: z.string()
})

// API Request/Response Types
export const StartPlanningRequestSchema = z.object({
  prompt: z.string().min(10, 'Prompt must be at least 10 characters'),
  isInteractive: z.boolean().default(false),
  userAnswers: z.record(z.any()).default({})
})

export const PlanningStepRequestSchema = z.object({
  step: PlanningStepSchema,
  context: z.record(z.any()),
  answer: z.string().optional()
})

export const PlanningResponseSchema = z.object({
  success: z.boolean(),
  results: z.record(z.any()).optional(),
  needsInput: z.boolean().default(false),
  question: z.object({
    id: z.string(),
    question: z.string(),
    type: z.enum(['text', 'textarea']),
    placeholder: z.string().optional(),
    optional: z.boolean().optional()
  }).optional(),
  completed: z.boolean().default(false),
  error: z.string().optional()
})

// Derived Types
export type PlanningStep = z.infer<typeof PlanningStepSchema>
export type PlanningState = z.infer<typeof PlanningStateSchema>
export type MCPContext = z.infer<typeof MCPContextSchema>
export type StartPlanningRequest = z.infer<typeof StartPlanningRequestSchema>
export type PlanningStepRequest = z.infer<typeof PlanningStepRequestSchema>
export type PlanningResponse = z.infer<typeof PlanningResponseSchema>

// Domain Events
export const PlanningEventSchema = z.discriminatedUnion('type', [
  z.object({
    type: z.literal('planning_started'),
    payload: z.object({
      prompt: z.string(),
      timestamp: z.string()
    })
  }),
  z.object({
    type: z.literal('step_completed'),
    payload: z.object({
      step: PlanningStepSchema,
      results: z.record(z.any()),
      timestamp: z.string()
    })
  }),
  z.object({
    type: z.literal('planning_completed'),
    payload: z.object({
      results: z.record(z.any()),
      timestamp: z.string()
    })
  }),
  z.object({
    type: z.literal('planning_failed'),
    payload: z.object({
      error: z.string(),
      step: PlanningStepSchema.optional(),
      timestamp: z.string()
    })
  })
])

export type PlanningEvent = z.infer<typeof PlanningEventSchema>

// Configuration
export const PlanningConfigSchema = z.object({
  maxRetries: z.number().default(3),
  timeoutMs: z.number().default(30000),
  enableMCP: z.boolean().default(true),
  enableSequentialThinking: z.boolean().default(true),
  enableContext7: z.boolean().default(true),
  model: z.string().default('gpt-4o-mini'),
  temperature: z.number().min(0).max(2).default(0.7)
})

export type PlanningConfig = z.infer<typeof PlanningConfigSchema>
