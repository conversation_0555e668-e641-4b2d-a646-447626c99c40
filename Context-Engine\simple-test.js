#!/usr/bin/env node

/**
 * Simple test to verify our modules can be imported
 */

console.log('🚀 Starting simple module test...\n');

async function testModules() {
  try {
    // Test 1: Import configuration
    console.log('📋 Test 1: Testing configuration import...');
    const { config } = await import('./src/utils/config.js');
    console.log('✅ Configuration imported successfully');
    console.log(`   Environment: ${config.env}`);

    // Test 2: Import logger
    console.log('\n📋 Test 2: Testing logger import...');
    const { createLogger } = await import('./src/utils/logger.js');
    const testLogger = createLogger('SimpleTest');
    console.log('✅ Logger imported successfully');

    // Test 3: Import AST Processor
    console.log('\n📋 Test 3: Testing AST Processor import...');
    const { ASTProcessor } = await import('./src/core/processors/ASTProcessor.js');
    const astProcessor = new ASTProcessor({});
    await astProcessor.initialize();
    console.log('✅ AST Processor imported and initialized successfully');

    // Test 4: Test simple AST processing
    console.log('\n📋 Test 4: Testing simple AST processing...');
    const testCode = `
class TestClass {
  constructor() {
    this.value = 42;
  }
  
  testMethod() {
    return this.value;
  }
}

function testFunction() {
  return new TestClass();
}
`;

    const testFile = {
      path: 'test.js',
      content: testCode,
      language: 'javascript',
      size: testCode.length
    };

    const result = await astProcessor.process({ file: testFile });
    
    if (result.ast && result.ast.nodes.length > 0) {
      console.log(`✅ AST processing successful - found ${result.ast.nodes.length} nodes`);
      result.ast.nodes.forEach(node => {
        console.log(`   - ${node.nodeType}: ${node.name} (line ${node.startLine})`);
      });
    } else {
      console.log('⚠️  AST processing completed but no nodes found');
    }

    console.log('\n🎉 All simple tests passed!');
    console.log('\n✅ MVP core modules are working correctly');
    console.log('\nNext steps:');
    console.log('1. Start Neo4j: docker-compose up -d neo4j');
    console.log('2. Test with database: npm test');
    console.log('3. Start the full application: npm start');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

testModules();
