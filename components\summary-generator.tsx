"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { FileText, Edit, Check } from "lucide-react"
import type { ModuleProps } from "@/types/planning"

export function SummaryGenerator({ context, onComplete }: ModuleProps) {
  const [summary, setSummary] = useState("")
  const [isGenerating, setIsGenerating] = useState(true)
  const [isEditing, setIsEditing] = useState(false)

  useEffect(() => {
    generateSummary()
  }, [])

  const generateSummary = async () => {
    setIsGenerating(true)

    // Simulate AI generation
    await new Promise((resolve) => setTimeout(resolve, 2000))

    const generatedSummary = createProjectSummary()
    setSummary(generatedSummary)
    setIsGenerating(false)
  }

  const createProjectSummary = (): string => {
    const { originalPrompt, clarifications } = context || {}

    return `Project Overview:

This project aims to develop a ${context?.projectType || "application"} based on the requirement: "${originalPrompt}".

Target Users: ${clarifications?.target_users || "General users"}

Platform: ${clarifications?.platform || "Web-based"}

Key Features:
${context?.features?.map((feature) => `• ${feature}`).join("\n") || "• Core functionality"}

Authentication: ${clarifications?.authentication || "To be determined"}

Data Storage: ${clarifications?.data_storage || "Basic data persistence required"}

${clarifications?.integrations ? `Third-party Integrations: ${clarifications.integrations}` : ""}

This application will provide a user-friendly interface for ${context?.features?.[0]?.toLowerCase() || "the main functionality"} while ensuring scalability and maintainability.`
  }

  const handleEdit = () => {
    setIsEditing(true)
  }

  const handleSave = () => {
    setIsEditing(false)
    onComplete(summary)
  }

  const handleCancel = () => {
    setIsEditing(false)
    setSummary(createProjectSummary())
  }

  return (
    <Card className="border-slate-700" style={{backgroundColor: '#818181'}}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FileText className="w-5 h-5 text-purple-400" />
            <CardTitle className="text-white">Project Summary</CardTitle>
          </div>
          {!isGenerating && !isEditing && (
            <Button
              onClick={handleEdit}
              variant="outline"
              size="sm"
              className="border-slate-600 text-gray-300 bg-transparent"
            >
              <Edit className="w-4 h-4 mr-2" />
              Edit
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {isGenerating ? (
          <div className="flex items-center justify-center py-8">
            <div className="flex items-center gap-3">
              <FileText className="w-5 h-5 text-purple-400 animate-pulse" />
              <span className="text-gray-300">Generating project summary...</span>
            </div>
          </div>
        ) : isEditing ? (
          <>
            <Textarea
              value={summary}
              onChange={(e) => setSummary(e.target.value)}
              className="bg-slate-700 border-slate-600 text-white min-h-[300px]"
            />
            <div className="flex gap-2">
              <Button onClick={handleSave} className="bg-green-600 hover:bg-green-700">
                <Check className="w-4 h-4 mr-2" />
                Save Changes
              </Button>
              <Button
                onClick={handleCancel}
                variant="outline"
                className="border-slate-600 text-gray-300 bg-transparent"
              >
                Cancel
              </Button>
            </div>
          </>
        ) : (
          <>
            <div className="bg-slate-700/50 p-4 rounded-lg">
              <pre className="text-gray-300 whitespace-pre-wrap font-sans">{summary}</pre>
            </div>
            <Button onClick={handleSave} className="w-full bg-purple-600 hover:bg-purple-700">
              Approve Summary & Continue
            </Button>
          </>
        )}
      </CardContent>
    </Card>
  )
}
