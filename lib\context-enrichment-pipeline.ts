import type { ProjectContext } from "@/types/planning"
import { MCPIntegration } from "./mcp-integration"

/**
 * Context Enrichment Pipeline
 * Augments user requirements with inferred industry standards, patterns, and system design norms
 */
export class ContextEnrichmentPipeline {
  private industryStandards: Map<string, IndustryStandard> = new Map()
  private designPatterns: Map<string, DesignPattern> = new Map()
  private bestPractices: Map<string, BestPractice[]> = new Map()
  private domainKnowledge: Map<string, DomainKnowledge> = new Map()
  private mcpIntegration: MCPIntegration

  constructor() {
    this.mcpIntegration = new MCPIntegration()
    this.initializeKnowledgeBase()
  }

  /**
   * Enrich context with industry standards, patterns, and best practices
   * Now enhanced with real-time MCP data
   */
  async enrichContext(context: ProjectContext, step: string): Promise<EnrichedContext> {
    const enrichments: ContextEnrichment[] = []

    // 1. Get real-time industry standards via MCP
    const mcpIndustryStandards = await this.getMCPIndustryStandards(context)
    enrichments.push(...mcpIndustryStandards)

    // 2. Infer industry standards (fallback/supplement)
    const industryStandards = this.inferIndustryStandards(context)
    enrichments.push(...industryStandards)

    // 3. Get real-time tech stack validation via MCP
    const mcpTechValidation = await this.getMCPTechValidation(context)
    enrichments.push(...mcpTechValidation)

    // 4. Recommend design patterns
    const designPatterns = this.recommendDesignPatterns(context, step)
    enrichments.push(...designPatterns)

    // 5. Apply best practices
    const bestPractices = this.applyBestPractices(context, step)
    enrichments.push(...bestPractices)

    // 6. Add domain-specific knowledge
    const domainKnowledge = this.addDomainKnowledge(context)
    enrichments.push(...domainKnowledge)

    // 7. Get project context via MCP (team preferences, org standards)
    const mcpProjectContext = await this.getMCPProjectContext(context)
    enrichments.push(...mcpProjectContext)

    // 8. Infer missing requirements
    const missingRequirements = this.inferMissingRequirements(context)
    enrichments.push(...missingRequirements)

    return {
      originalContext: context,
      enrichments,
      enhancedContext: this.applyEnrichments(context, enrichments),
      confidence: this.calculateConfidence(enrichments),
      recommendations: this.generateRecommendations(enrichments)
    }
  }

  /**
   * Infer industry standards based on project type and domain
   */
  private inferIndustryStandards(context: ProjectContext): ContextEnrichment[] {
    const enrichments: ContextEnrichment[] = []
    const projectType = context.projectType?.toLowerCase() || ''
    const domain = this.detectDomain(context)

    // Web application standards
    if (projectType.includes('web') || projectType.includes('app')) {
      enrichments.push({
        type: 'industry_standard',
        category: 'web_standards',
        title: 'Web Accessibility (WCAG 2.1)',
        description: 'Ensure compliance with Web Content Accessibility Guidelines',
        impact: 'high',
        source: 'W3C Standards',
        recommendation: 'Implement ARIA labels, keyboard navigation, and screen reader support',
        applicableSteps: ['wireframes', 'design', 'implementation']
      })

      enrichments.push({
        type: 'industry_standard',
        category: 'security',
        title: 'OWASP Security Guidelines',
        description: 'Follow OWASP Top 10 security practices',
        impact: 'critical',
        source: 'OWASP Foundation',
        recommendation: 'Implement input validation, authentication, and secure data handling',
        applicableSteps: ['architecture', 'database', 'implementation']
      })
    }

    // Mobile application standards
    if (projectType.includes('mobile')) {
      enrichments.push({
        type: 'industry_standard',
        category: 'mobile_standards',
        title: 'Platform Design Guidelines',
        description: 'Follow iOS Human Interface Guidelines and Material Design',
        impact: 'high',
        source: 'Apple & Google',
        recommendation: 'Adhere to platform-specific UI/UX conventions',
        applicableSteps: ['wireframes', 'design']
      })
    }

    // E-commerce specific standards
    if (domain === 'ecommerce') {
      enrichments.push({
        type: 'industry_standard',
        category: 'compliance',
        title: 'PCI DSS Compliance',
        description: 'Payment Card Industry Data Security Standard',
        impact: 'critical',
        source: 'PCI Security Standards Council',
        recommendation: 'Implement secure payment processing and data protection',
        applicableSteps: ['architecture', 'database', 'security']
      })
    }

    return enrichments
  }

  /**
   * Recommend design patterns based on context and requirements
   */
  private recommendDesignPatterns(context: ProjectContext, step: string): ContextEnrichment[] {
    const enrichments: ContextEnrichment[] = []
    const techStack = context.techStack as any
    const features = context.features || []

    // Frontend patterns
    if (step === 'architecture' && techStack?.frontend) {
      if (techStack.frontend.includes('React')) {
        enrichments.push({
          type: 'design_pattern',
          category: 'frontend_architecture',
          title: 'Component Composition Pattern',
          description: 'Use composition over inheritance for React components',
          impact: 'medium',
          source: 'React Best Practices',
          recommendation: 'Create reusable, composable components with clear prop interfaces',
          applicableSteps: ['architecture', 'implementation']
        })

        if (features.some(f => f.toLowerCase().includes('state') || f.toLowerCase().includes('data'))) {
          enrichments.push({
            type: 'design_pattern',
            category: 'state_management',
            title: 'Flux/Redux Pattern',
            description: 'Unidirectional data flow for predictable state management',
            impact: 'high',
            source: 'Facebook/Redux',
            recommendation: 'Implement centralized state management for complex data flows',
            applicableSteps: ['architecture', 'implementation']
          })
        }
      }
    }

    // Backend patterns
    if (step === 'architecture' && techStack?.backend) {
      enrichments.push({
        type: 'design_pattern',
        category: 'backend_architecture',
        title: 'Repository Pattern',
        description: 'Separate data access logic from business logic',
        impact: 'medium',
        source: 'Domain-Driven Design',
        recommendation: 'Create repository interfaces for data access abstraction',
        applicableSteps: ['architecture', 'database', 'implementation']
      })

      if (features.length > 5) {
        enrichments.push({
          type: 'design_pattern',
          category: 'architecture',
          title: 'Microservices Pattern',
          description: 'Decompose application into loosely coupled services',
          impact: 'high',
          source: 'Microservices Architecture',
          recommendation: 'Consider service boundaries based on business capabilities',
          applicableSteps: ['architecture', 'deployment']
        })
      }
    }

    return enrichments
  }

  /**
   * Apply best practices based on project context
   */
  private applyBestPractices(context: ProjectContext, step: string): ContextEnrichment[] {
    const enrichments: ContextEnrichment[] = []
    const projectType = context.projectType?.toLowerCase() || ''

    // Performance best practices
    if (step === 'architecture' || step === 'implementation') {
      enrichments.push({
        type: 'best_practice',
        category: 'performance',
        title: 'Performance Budget',
        description: 'Set and monitor performance budgets for optimal user experience',
        impact: 'high',
        source: 'Web Performance Best Practices',
        recommendation: 'Target <3s load time, <100ms response time, <50KB initial bundle',
        applicableSteps: ['architecture', 'implementation', 'testing']
      })
    }

    // Security best practices
    if (step === 'database' || step === 'architecture') {
      enrichments.push({
        type: 'best_practice',
        category: 'security',
        title: 'Defense in Depth',
        description: 'Implement multiple layers of security controls',
        impact: 'critical',
        source: 'Security Engineering',
        recommendation: 'Use authentication, authorization, input validation, and encryption',
        applicableSteps: ['architecture', 'database', 'implementation']
      })
    }

    // Testing best practices
    if (step === 'implementation') {
      enrichments.push({
        type: 'best_practice',
        category: 'testing',
        title: 'Testing Pyramid',
        description: 'Balance unit, integration, and end-to-end tests',
        impact: 'medium',
        source: 'Test-Driven Development',
        recommendation: 'Aim for 70% unit tests, 20% integration tests, 10% E2E tests',
        applicableSteps: ['implementation', 'testing']
      })
    }

    return enrichments
  }

  /**
   * Add domain-specific knowledge
   */
  private addDomainKnowledge(context: ProjectContext): ContextEnrichment[] {
    const enrichments: ContextEnrichment[] = []
    const domain = this.detectDomain(context)

    const domainKnowledge = this.domainKnowledge.get(domain)
    if (domainKnowledge) {
      enrichments.push({
        type: 'domain_knowledge',
        category: domain,
        title: domainKnowledge.title,
        description: domainKnowledge.description,
        impact: 'medium',
        source: domainKnowledge.source,
        recommendation: domainKnowledge.keyConsiderations.join('; '),
        applicableSteps: domainKnowledge.applicableSteps
      })
    }

    return enrichments
  }

  /**
   * Infer missing requirements based on project type and features
   */
  private inferMissingRequirements(context: ProjectContext): ContextEnrichment[] {
    const enrichments: ContextEnrichment[] = []
    const features = context.features || []
    const hasAuth = features.some(f => f.toLowerCase().includes('auth') || f.toLowerCase().includes('login'))
    const hasData = features.some(f => f.toLowerCase().includes('data') || f.toLowerCase().includes('crud'))

    // Authentication requirements
    if (hasAuth && !features.some(f => f.toLowerCase().includes('password'))) {
      enrichments.push({
        type: 'missing_requirement',
        category: 'authentication',
        title: 'Password Security Requirements',
        description: 'Password complexity and security measures not specified',
        impact: 'high',
        source: 'Security Analysis',
        recommendation: 'Define password complexity, 2FA, and account lockout policies',
        applicableSteps: ['requirements', 'security']
      })
    }

    // Data privacy requirements
    if (hasData && !features.some(f => f.toLowerCase().includes('privacy'))) {
      enrichments.push({
        type: 'missing_requirement',
        category: 'privacy',
        title: 'Data Privacy Compliance',
        description: 'Data privacy and compliance requirements not specified',
        impact: 'high',
        source: 'Compliance Analysis',
        recommendation: 'Consider GDPR, CCPA, and other privacy regulations',
        applicableSteps: ['requirements', 'legal']
      })
    }

    return enrichments
  }

  /**
   * Apply enrichments to create enhanced context
   */
  private applyEnrichments(context: ProjectContext, enrichments: ContextEnrichment[]): ProjectContext {
    const enhanced = { ...context }

    // Add enrichment metadata
    enhanced._enrichments = {
      appliedAt: new Date().toISOString(),
      enrichmentCount: enrichments.length,
      categories: [...new Set(enrichments.map(e => e.category))],
      highImpactCount: enrichments.filter(e => e.impact === 'high' || e.impact === 'critical').length
    }

    return enhanced
  }

  /**
   * Calculate confidence score for enrichments
   */
  private calculateConfidence(enrichments: ContextEnrichment[]): number {
    if (enrichments.length === 0) return 0

    const weights = { critical: 1.0, high: 0.8, medium: 0.6, low: 0.4 }
    const totalWeight = enrichments.reduce((sum, e) => sum + (weights[e.impact] || 0.5), 0)
    
    return Math.min(100, Math.round((totalWeight / enrichments.length) * 100))
  }

  /**
   * Generate recommendations based on enrichments
   */
  private generateRecommendations(enrichments: ContextEnrichment[]): string[] {
    const recommendations: string[] = []
    const criticalEnrichments = enrichments.filter(e => e.impact === 'critical')
    const highImpactEnrichments = enrichments.filter(e => e.impact === 'high')

    if (criticalEnrichments.length > 0) {
      recommendations.push(`🚨 ${criticalEnrichments.length} critical considerations identified - immediate attention required`)
    }

    if (highImpactEnrichments.length > 0) {
      recommendations.push(`⚠️ ${highImpactEnrichments.length} high-impact recommendations to enhance project quality`)
    }

    const categories = [...new Set(enrichments.map(e => e.category))]
    if (categories.length > 3) {
      recommendations.push(`📋 Consider ${categories.length} different aspects: ${categories.slice(0, 3).join(', ')}...`)
    }

    return recommendations
  }

  /**
   * Detect domain based on project context
   */
  private detectDomain(context: ProjectContext): string {
    const prompt = context.originalPrompt?.toLowerCase() || ''
    const features = context.features?.join(' ').toLowerCase() || ''
    const combined = `${prompt} ${features}`

    if (combined.includes('ecommerce') || combined.includes('shop') || combined.includes('payment')) return 'ecommerce'
    if (combined.includes('health') || combined.includes('medical') || combined.includes('patient')) return 'healthcare'
    if (combined.includes('finance') || combined.includes('bank') || combined.includes('trading')) return 'fintech'
    if (combined.includes('education') || combined.includes('learning') || combined.includes('course')) return 'edtech'
    if (combined.includes('social') || combined.includes('chat') || combined.includes('message')) return 'social'
    
    return 'general'
  }

  /**
   * Get real-time industry standards via MCP
   */
  private async getMCPIndustryStandards(context: ProjectContext): Promise<ContextEnrichment[]> {
    const enrichments: ContextEnrichment[] = []
    const domain = this.detectDomain(context)

    try {
      const standards = await this.mcpIntegration.executeTool(
        'best-practices-server',
        'get_industry_standards',
        { domain, projectType: context.projectType }
      )

      if (standards.standards) {
        Object.entries(standards.standards).forEach(([category, requirements]: [string, any]) => {
          if (Array.isArray(requirements)) {
            requirements.forEach(req => {
              enrichments.push({
                type: 'industry_standard',
                category: `mcp_${category}`,
                title: `${category.toUpperCase()}: ${req}`,
                description: `Real-time industry standard for ${category}`,
                impact: 'high',
                source: `MCP: ${standards.source}`,
                recommendation: `Implement ${req} compliance`,
                applicableSteps: ['requirements', 'architecture', 'security']
              })
            })
          }
        })
      }
    } catch (error) {
      console.warn('MCP industry standards lookup failed:', error)
    }

    return enrichments
  }

  /**
   * Get real-time tech stack validation via MCP
   */
  private async getMCPTechValidation(context: ProjectContext): Promise<ContextEnrichment[]> {
    const enrichments: ContextEnrichment[] = []
    const techStack = context.techStack as any

    if (!techStack) return enrichments

    try {
      // Check tech compatibility
      const compatibility = await this.mcpIntegration.executeTool(
        'tech-stack-server',
        'get_tech_compatibility',
        {
          frontend: techStack.frontend,
          backend: techStack.backend,
          database: techStack.database
        }
      )

      if (compatibility.issues && compatibility.issues.length > 0) {
        compatibility.issues.forEach((issue: any) => {
          enrichments.push({
            type: 'best_practice',
            category: 'mcp_tech_compatibility',
            title: 'Tech Stack Compatibility Issue',
            description: issue.description,
            impact: issue.severity || 'medium',
            source: 'MCP: Real-time Compatibility Check',
            recommendation: issue.solution,
            applicableSteps: ['techstack', 'architecture']
          })
        })
      }

      // Check for security advisories
      const technologies = [techStack.frontend, techStack.backend, techStack.database].filter(Boolean)
      const advisories = await this.mcpIntegration.executeTool(
        'tech-stack-server',
        'check_security_advisories',
        { technologies }
      )

      if (advisories.advisories && advisories.advisories.length > 0) {
        advisories.advisories.forEach((advisory: any) => {
          enrichments.push({
            type: 'industry_standard',
            category: 'mcp_security',
            title: `Security Advisory: ${advisory.technology}`,
            description: advisory.description,
            impact: 'critical',
            source: 'MCP: Security Advisory Database',
            recommendation: advisory.recommendation,
            applicableSteps: ['techstack', 'security']
          })
        })
      }

    } catch (error) {
      console.warn('MCP tech validation failed:', error)
    }

    return enrichments
  }

  /**
   * Get project context via MCP (team preferences, org standards)
   */
  private async getMCPProjectContext(context: ProjectContext): Promise<ContextEnrichment[]> {
    const enrichments: ContextEnrichment[] = []

    try {
      // Get team preferences (if team ID available)
      const teamId = process.env.TEAM_ID || 'default'
      const teamPrefs = await this.mcpIntegration.executeTool(
        'project-context-server',
        'get_team_preferences',
        { teamId }
      )

      if (teamPrefs.preferences) {
        Object.entries(teamPrefs.preferences).forEach(([key, value]: [string, any]) => {
          enrichments.push({
            type: 'best_practice',
            category: 'mcp_team_preference',
            title: `Team Preference: ${key}`,
            description: `Team prefers ${value} for ${key}`,
            impact: 'medium',
            source: 'MCP: Team Standards',
            recommendation: `Consider using ${value} to align with team preferences`,
            applicableSteps: ['techstack', 'implementation']
          })
        })
      }

      // Get organizational standards
      const orgId = process.env.ORGANIZATION_ID || 'default'
      const orgStandards = await this.mcpIntegration.executeTool(
        'project-context-server',
        'check_organizational_standards',
        { organizationId: orgId }
      )

      if (orgStandards.standards) {
        Object.entries(orgStandards.standards).forEach(([key, value]: [string, any]) => {
          enrichments.push({
            type: 'industry_standard',
            category: 'mcp_org_standard',
            title: `Organizational Standard: ${key}`,
            description: value,
            impact: 'high',
            source: 'MCP: Organizational Standards',
            recommendation: `Ensure compliance with organizational ${key} standards`,
            applicableSteps: ['architecture', 'security', 'deployment']
          })
        })
      }

    } catch (error) {
      console.warn('MCP project context lookup failed:', error)
    }

    return enrichments
  }

  /**
   * Get available MCP tools for debugging/monitoring
   */
  getMCPTools(): any {
    return this.mcpIntegration.getAvailableTools()
  }

  /**
   * Get MCP cache statistics
   */
  getMCPStats(): any {
    return this.mcpIntegration.getCacheStats()
  }

  /**
   * Initialize knowledge base with industry standards and patterns
   */
  private initializeKnowledgeBase(): void {
    // Initialize domain knowledge
    this.domainKnowledge.set('ecommerce', {
      title: 'E-commerce Domain Considerations',
      description: 'Specific requirements for e-commerce applications',
      keyConsiderations: [
        'PCI DSS compliance for payment processing',
        'Inventory management and real-time stock updates',
        'Shopping cart persistence and abandonment recovery',
        'Product search and filtering capabilities',
        'Order management and fulfillment workflows'
      ],
      applicableSteps: ['requirements', 'architecture', 'database'],
      source: 'E-commerce Best Practices'
    })

    this.domainKnowledge.set('healthcare', {
      title: 'Healthcare Domain Considerations',
      description: 'Specific requirements for healthcare applications',
      keyConsiderations: [
        'HIPAA compliance for patient data protection',
        'Audit trails for all data access and modifications',
        'Role-based access control for different user types',
        'Integration with existing healthcare systems (HL7/FHIR)',
        'Data backup and disaster recovery procedures'
      ],
      applicableSteps: ['requirements', 'security', 'database'],
      source: 'Healthcare IT Standards'
    })

    // Additional domain knowledge would be added here...
  }
}

// Type definitions
export interface ContextEnrichment {
  type: 'industry_standard' | 'design_pattern' | 'best_practice' | 'domain_knowledge' | 'missing_requirement'
  category: string
  title: string
  description: string
  impact: 'critical' | 'high' | 'medium' | 'low'
  source: string
  recommendation: string
  applicableSteps: string[]
}

export interface EnrichedContext {
  originalContext: ProjectContext
  enrichments: ContextEnrichment[]
  enhancedContext: ProjectContext
  confidence: number
  recommendations: string[]
}

interface IndustryStandard {
  name: string
  description: string
  applicableDomains: string[]
  requirements: string[]
}

interface DesignPattern {
  name: string
  description: string
  applicableContexts: string[]
  benefits: string[]
  implementation: string
}

interface BestPractice {
  name: string
  description: string
  category: string
  impact: string
}

interface DomainKnowledge {
  title: string
  description: string
  keyConsiderations: string[]
  applicableSteps: string[]
  source: string
}
