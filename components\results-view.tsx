"use client"

import type React from "react"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { motion } from "framer-motion"
import { ArrowLeft, FileText, Code, Layout, Database, Workflow, CheckSquare, Download, Brain, Eye, EyeOff, FolderTree, Folder, File, BookOpen, Settings } from "lucide-react"

interface ResultsViewProps {
  results: Record<string, any>
  userPrompt: string
  onBack: () => void
}

const SECTION_ICONS: Record<string, any> = {
  analyze: FileText,
  clarify: FileText,
  summary: FileText,
  techstack: Code,
  prd: FileText,
  "context-profile": Brain,
  wireframes: Layout,
  design: Layout,
  database: Database,
  filesystem: Database,
  workflow: Workflow,
  tasks: CheckSquare,
  scaffold: Code,
}

// Helper function to detect and render common object patterns
const renderObjectAsCard = (obj: any, index?: number) => {
  const hasName = obj.name || obj.title || obj.component || obj.feature
  const hasDescription = obj.description || obj.details || obj.content
  const hasAction = obj.action || obj.task || obj.requirement

  if (hasName || hasDescription || hasAction) {
    return (
      <div key={index} className="bg-gray-800 p-3 rounded-lg border border-gray-700">
        {hasName && (
          <div className="text-white font-medium mb-1">
            {obj.name || obj.title || obj.component || obj.feature}
          </div>
        )}
        {hasAction && !hasName && (
          <div className="text-white font-medium mb-1">
            {obj.action || obj.task || obj.requirement}
          </div>
        )}
        {hasDescription && (
          <div className="text-gray-300 text-sm mb-2">
            {obj.description || obj.details || obj.content}
          </div>
        )}
        {/* Render other properties */}
        <div className="space-y-1">
          {Object.entries(obj).map(([key, val]) => {
            if (['name', 'title', 'component', 'feature', 'description', 'details', 'content', 'action', 'task', 'requirement'].includes(key)) {
              return null
            }
            return (
              <div key={key} className="flex justify-between items-start text-xs">
                <span className="text-gray-400 capitalize">{key.replace(/([A-Z])/g, " $1").trim()}:</span>
                <span className="text-gray-300 ml-2">
                  {Array.isArray(val) ? val.join(", ") : String(val)}
                </span>
              </div>
            )
          })}
        </div>
      </div>
    )
  }
  return null
}

// Helper function to render step objects
const renderStepObject = (step: any, index: number) => {
  if (typeof step === "object" && (step.step || step.action || step.name)) {
    return (
      <div key={index} className="flex items-start gap-3 p-3 bg-gray-800 rounded-lg">
        <span className="flex-shrink-0 w-6 h-6 bg-red-600 text-white text-xs rounded-full flex items-center justify-center font-bold">
          {step.step || index + 1}
        </span>
        <div className="flex-1">
          <div className="text-white font-medium">
            {step.action || step.name || step.title || `Step ${index + 1}`}
          </div>
          {step.description && (
            <div className="text-gray-300 text-sm mt-1">{step.description}</div>
          )}
          {step.result && (
            <div className="text-gray-400 text-sm mt-1">Result: {step.result}</div>
          )}
          {step.details && (
            <div className="text-gray-300 text-sm mt-1">{step.details}</div>
          )}
        </div>
      </div>
    )
  }
  return null
}

// Component for showing raw JSON with toggle
const RawDataToggle = ({ data, label = "Raw Data" }: { data: any; label?: string }) => {
  const [showRaw, setShowRaw] = useState(false)

  return (
    <div className="mt-4 border-t border-gray-700 pt-4">
      <button
        onClick={() => setShowRaw(!showRaw)}
        className="flex items-center gap-2 text-gray-400 hover:text-gray-300 text-sm"
      >
        {showRaw ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
        {showRaw ? "Hide" : "Show"} {label}
      </button>
      {showRaw && (
        <div className="mt-3 bg-gray-900 p-4 rounded overflow-auto max-h-96">
          <pre className="text-gray-300 text-xs whitespace-pre-wrap">
            {JSON.stringify(data, null, 2)}
          </pre>
        </div>
      )}
    </div>
  )
}

export function ResultsView({ results, userPrompt, onBack }: ResultsViewProps) {
  const [activeSection, setActiveSection] = useState<string>("analyze")

  // Filter out sections that have actual data
  const sections = Object.keys(results).filter((key) => {
    const value = results[key]
    if (!value) return false

    // Include objects (normal case)
    if (typeof value === "object") return true

    // Include design section if it's a string containing "Style Guide" (Gemini-generated)
    if (key === "design" && typeof value === "string" && value.includes("Style Guide")) return true

    return false
  })

  // Set the first available section as active if analyze doesn't exist
  useState(() => {
    if (sections.length > 0 && !results.analyze) {
      setActiveSection(sections[0])
    }
  })

  // Helper function to render any object in a human-readable format
  const renderObjectHumanReadable = (obj: any, index?: number): React.ReactNode => {
    if (!obj || typeof obj !== "object") {
      return <span className="text-gray-300">{String(obj)}</span>
    }

    // Extract meaningful properties for display
    const getDisplayText = (item: any): string => {
      // Try common property names for main content
      const mainContent = item.name || item.title || item.action || item.task ||
                         item.requirement || item.feature || item.component ||
                         item.goal || item.benefit || item.description

      if (mainContent) return String(mainContent)

      // If no main content, try to create a summary
      const keys = Object.keys(item)
      if (keys.length === 0) return "Empty object"

      // For objects with role/goal/benefit pattern (user stories)
      if (item.role && item.goal) {
        return `${item.role}: ${item.goal}${item.benefit ? ` (${item.benefit})` : ''}`
      }

      // For objects with service/purpose pattern (integrations)
      if (item.service && item.purpose) {
        return `${item.service}: ${item.purpose}`
      }

      // For objects with source/destination pattern (data flow)
      if (item.source && item.destination) {
        return `${item.source} → ${item.destination}${item.transformation ? ` (${item.transformation})` : ''}`
      }

      // For simple key-value objects, show first few properties
      const firstFewProps = keys.slice(0, 2).map(key => `${key}: ${String(item[key])}`).join(", ")
      return keys.length > 2 ? `${firstFewProps}...` : firstFewProps
    }

    const displayText = getDisplayText(obj)
    const hasSubProperties = obj.description || obj.details || obj.benefit || obj.purpose

    return (
      <div key={index} className="p-3 bg-gray-800 rounded-lg border border-gray-700">
        <div className="text-white font-medium">{displayText}</div>
        {hasSubProperties && (
          <div className="mt-2 space-y-1">
            {obj.description && (
              <div className="text-gray-300 text-sm">
                <span className="text-gray-400">Description:</span> {obj.description}
              </div>
            )}
            {obj.benefit && (
              <div className="text-gray-300 text-sm">
                <span className="text-gray-400">Benefit:</span> {obj.benefit}
              </div>
            )}
            {obj.purpose && (
              <div className="text-gray-300 text-sm">
                <span className="text-gray-400">Purpose:</span> {obj.purpose}
              </div>
            )}
            {obj.details && (
              <div className="text-gray-300 text-sm">
                <span className="text-gray-400">Details:</span> {obj.details}
              </div>
            )}
          </div>
        )}
      </div>
    )
  }

  const renderValue = (value: any): React.ReactNode => {
    if (value === null || value === undefined) {
      return <span className="text-gray-500">Not specified</span>
    }

    if (typeof value === "string") {
      return <span className="text-gray-300">{value}</span>
    }

    if (typeof value === "boolean") {
      return <span className="text-gray-300">{value ? "Yes" : "No"}</span>
    }

    if (typeof value === "number") {
      return <span className="text-gray-300">{value}</span>
    }

    if (Array.isArray(value)) {
      // Check if this looks like a steps array
      const isStepsArray = value.some(item =>
        typeof item === "object" && (item.step || item.action || item.name)
      )

      if (isStepsArray) {
        return (
          <div className="space-y-2">
            {value.map((item, index) => {
              const stepRender = renderStepObject(item, index)
              if (stepRender) return stepRender
              return renderObjectHumanReadable(item, index)
            })}
          </div>
        )
      }

      // Check if this is an array of objects that can be rendered as cards
      const hasCardableObjects = value.some(item =>
        typeof item === "object" && (
          item.name || item.title || item.component || item.feature ||
          item.description || item.action || item.task || item.requirement ||
          item.role || item.goal || item.service || item.source
        )
      )

      if (hasCardableObjects) {
        return (
          <div className="space-y-3">
            {value.map((item, index) => {
              const cardRender = renderObjectAsCard(item, index)
              if (cardRender) return cardRender
              return renderObjectHumanReadable(item, index)
            })}
          </div>
        )
      }

      // Default array rendering - handle mixed content better
      return (
        <ul className="text-gray-300 space-y-1">
          {value.map((item, index) => (
            <li key={index} className="flex items-start gap-2">
              <span className="text-gray-500 mt-1">•</span>
              <div className="flex-1">
                {typeof item === "object" ? renderObjectHumanReadable(item, index) : String(item)}
              </div>
            </li>
          ))}
        </ul>
      )
    }

    if (typeof value === "object") {
      // Try to render as a card first
      const cardRender = renderObjectAsCard(value)
      if (cardRender) return cardRender

      // Enhanced object rendering with better nested object handling
      return (
        <div className="space-y-2">
          {Object.entries(value).map(([key, val]) => (
            <div key={key} className="flex justify-between items-start">
              <span className="text-gray-400 capitalize">{key.replace(/([A-Z])/g, " $1").trim()}:</span>
              <span className="text-gray-300 ml-2 flex-1 text-right">
                {Array.isArray(val) ? (
                  val.length <= 3 ?
                    val.map(v => typeof v === "object" ? (v.name || v.title || "Object") : String(v)).join(", ") :
                    `${val.slice(0, 3).map(v => typeof v === "object" ? (v.name || v.title || "Object") : String(v)).join(", ")}... (+${val.length - 3} more)`
                ) : typeof val === "object" && val !== null ? (
                  Object.keys(val).length <= 2 ?
                    Object.entries(val).map(([k, v]) => `${k}: ${typeof v === "object" ? (v?.name || v?.title || "Object") : String(v)}`).join(", ") :
                    `{${Object.keys(val).length} properties}`
                ) : String(val)}
              </span>
            </div>
          ))}
        </div>
      )
    }

    return <span className="text-gray-300">{String(value)}</span>
  }

  const renderSectionContent = (sectionId: string, data: any) => {
    // Safety check for data - allow strings for design section (style guides)
    if (!data || (typeof data !== "object" && !(sectionId === "design" && typeof data === "string"))) {
      return <p className="text-gray-300">No data available for this section</p>
    }

    switch (sectionId) {
      case "analyze":
        return (
          <div className="space-y-4">
            <div>
              <h4 className="text-white font-medium mb-2">Project Type</h4>
              {renderValue(data.projectType)}
            </div>
            <div>
              <h4 className="text-white font-medium mb-2">Key Features</h4>
              {renderValue(data.features)}
            </div>
            <div>
              <h4 className="text-white font-medium mb-2">Complexity</h4>
              {renderValue(data.complexity)}
            </div>
            {data.domain && (
              <div>
                <h4 className="text-white font-medium mb-2">Domain</h4>
                {renderValue(data.domain)}
              </div>
            )}
            {data.technicalHints && (
              <div>
                <h4 className="text-white font-medium mb-2">Technical Hints</h4>
                {renderValue(data.technicalHints)}
              </div>
            )}
          </div>
        )

      case "clarify":
        return (
          <div className="space-y-4">
            <div>
              <h4 className="text-white font-medium mb-2">Target Users</h4>
              {renderValue(data.targetUsers)}
            </div>
            <div>
              <h4 className="text-white font-medium mb-2">Platform</h4>
              {renderValue(data.platform)}
            </div>
            <div>
              <h4 className="text-white font-medium mb-2">Requirements</h4>
              {renderValue(data.requirements)}
            </div>
            {data.scope && (
              <div>
                <h4 className="text-white font-medium mb-2">Scope</h4>
                {renderValue(data.scope)}
              </div>
            )}
          </div>
        )

      case "summary":
        return (
          <div className="space-y-4">
            <div>
              <h4 className="text-white font-medium mb-2">Overview</h4>
              {renderValue(data.overview)}
            </div>
            {data.scope && (
              <div>
                <h4 className="text-white font-medium mb-2">Scope</h4>
                {renderValue(data.scope)}
              </div>
            )}
            {data.goals && (
              <div>
                <h4 className="text-white font-medium mb-2">Goals</h4>
                {renderValue(data.goals)}
              </div>
            )}
            {data.keyFeatures && (
              <div>
                <h4 className="text-white font-medium mb-2">Key Features</h4>
                {renderValue(data.keyFeatures)}
              </div>
            )}
          </div>
        )

      case "techstack":
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h4 className="text-white font-medium mb-2">Frontend</h4>
                {renderValue(data.frontend)}
              </div>
              <div>
                <h4 className="text-white font-medium mb-2">Backend</h4>
                {renderValue(data.backend)}
              </div>
              <div>
                <h4 className="text-white font-medium mb-2">Database</h4>
                {renderValue(data.database)}
              </div>
              <div>
                <h4 className="text-white font-medium mb-2">Hosting</h4>
                {renderValue(data.hosting)}
              </div>
            </div>
            {data.authentication && (
              <div>
                <h4 className="text-white font-medium mb-2">Authentication</h4>
                {renderValue(data.authentication)}
              </div>
            )}
            {data.preferences && (
              <div>
                <h4 className="text-white font-medium mb-2">Preferences</h4>
                {renderValue(data.preferences)}
              </div>
            )}
          </div>
        )

      case "prd":
        return (
          <div className="space-y-4">
            <div>
              <h4 className="text-white font-medium mb-2">Timeline</h4>
              {renderValue(data.timeline)}
            </div>
            {data.features && (
              <div>
                <h4 className="text-white font-medium mb-2">Features</h4>
                {renderValue(data.features)}
              </div>
            )}
            {data.userStories && (
              <div>
                <h4 className="text-white font-medium mb-2">User Stories</h4>
                {renderValue(data.userStories)}
              </div>
            )}
            {data.requirements && (
              <div>
                <h4 className="text-white font-medium mb-2">Requirements</h4>
                {renderValue(data.requirements)}
              </div>
            )}
            {data.acceptanceCriteria && (
              <div>
                <h4 className="text-white font-medium mb-2">Acceptance Criteria</h4>
                {renderValue(data.acceptanceCriteria)}
              </div>
            )}
          </div>
        )

      case "context-profile":
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-gray-800 p-4 rounded-lg">
                <h4 className="text-white font-medium mb-3 flex items-center gap-2">
                  <Brain className="w-4 h-4" />
                  Agent Identity
                </h4>
                <div className="space-y-2 text-sm">
                  <div><span className="text-gray-400">Name:</span> <span className="text-white">{data.identity?.name}</span></div>
                  <div><span className="text-gray-400">Role:</span> <span className="text-white">{data.identity?.role}</span></div>
                  <div><span className="text-gray-400">Organization:</span> <span className="text-white">{data.identity?.organization}</span></div>
                  <div><span className="text-gray-400">Profile ID:</span> <span className="text-white font-mono text-xs">{data.profile_id}</span></div>
                </div>
              </div>

              <div className="bg-gray-800 p-4 rounded-lg">
                <h4 className="text-white font-medium mb-3">Goals</h4>
                <div className="space-y-3 text-sm">
                  <div>
                    <span className="text-gray-400 block mb-1">Short-term:</span>
                    <ul className="list-disc list-inside text-white space-y-1">
                      {data.goals?.short_term?.map((goal: string, index: number) => (
                        <li key={index}>{goal}</li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <span className="text-gray-400 block mb-1">Long-term:</span>
                    <ul className="list-disc list-inside text-white space-y-1">
                      {data.goals?.long_term?.map((goal: string, index: number) => (
                        <li key={index}>{goal}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-gray-800 p-4 rounded-lg">
                <h4 className="text-white font-medium mb-3">Capabilities</h4>
                <div className="space-y-3 text-sm">
                  <div>
                    <span className="text-gray-400 block mb-2">Enabled Tools:</span>
                    <div className="flex flex-wrap gap-1">
                      {data.capabilities?.tools_enabled?.map((tool: string, index: number) => (
                        <span key={index} className="bg-blue-600 text-white px-2 py-1 rounded text-xs">{tool}</span>
                      ))}
                    </div>
                  </div>
                  <div>
                    <span className="text-gray-400">Platform:</span> <span className="text-white">{data.capabilities?.environment?.platform}</span>
                  </div>
                </div>
              </div>

              <div className="bg-gray-800 p-4 rounded-lg">
                <h4 className="text-white font-medium mb-3">Memory & Constraints</h4>
                <div className="space-y-2 text-sm">
                  <div><span className="text-gray-400">Scope:</span> <span className="text-white">{data.memory?.scope}</span></div>
                  <div><span className="text-gray-400">Persistence:</span> <span className="text-white">{data.memory?.persistence}</span></div>
                  <div><span className="text-gray-400">Rate Limit:</span> <span className="text-white">{data.constraints?.rate_limit}</span></div>
                  <div><span className="text-gray-400">Budget:</span> <span className="text-white">${data.constraints?.budget?.monthly}/month</span></div>
                </div>
              </div>
            </div>

            <div className="bg-gray-800 p-4 rounded-lg">
              <h4 className="text-white font-medium mb-3">Complete Profile JSON</h4>
              <div className="bg-gray-900 p-4 rounded overflow-auto max-h-96">
                <pre className="text-gray-300 text-xs whitespace-pre-wrap">{JSON.stringify(data, null, 2)}</pre>
              </div>
              <div className="mt-3 flex gap-2">
                <button
                  onClick={() => navigator.clipboard.writeText(JSON.stringify(data, null, 2))}
                  className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm"
                >
                  Copy JSON
                </button>
                <button
                  onClick={() => {
                    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
                    const url = URL.createObjectURL(blob)
                    const a = document.createElement('a')
                    a.href = url
                    a.download = `${data.profile_id || 'context-profile'}.json`
                    document.body.appendChild(a)
                    a.click()
                    document.body.removeChild(a)
                    URL.revokeObjectURL(url)
                  }}
                  className="bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-sm"
                >
                  Download JSON
                </button>
              </div>
            </div>
          </div>
        )

      case "wireframes":
        return (
          <div className="space-y-6">
            {/* Enhanced Pages with Wireframes */}
            {data.pages && Array.isArray(data.pages) && (
              <div>
                <h4 className="text-white font-medium mb-4">Page Wireframes</h4>
                <div className="space-y-6">
                  {data.pages.map((page: any, index: number) => (
                    <div key={index} className="border border-gray-700 rounded-lg p-4">
                      <div className="flex items-center gap-2 mb-3">
                        <h5 className="text-white font-semibold text-lg">{page.name || `Page ${index + 1}`}</h5>
                        {page.type && (
                          <span className="px-2 py-1 bg-red-600 text-white text-xs rounded-full">
                            {page.type}
                          </span>
                        )}
                      </div>

                      {page.purpose && (
                        <p className="text-gray-300 text-sm mb-4">{page.purpose}</p>
                      )}

                      {page.wireframe && (
                        <div className="mb-4">
                          <h6 className="text-gray-300 font-medium mb-2">Layout</h6>
                          <div className="bg-black border border-gray-600 p-6 rounded-lg overflow-x-auto">
                            <pre className="text-green-400 font-mono text-sm leading-relaxed whitespace-pre">
                              {page.wireframe}
                            </pre>
                          </div>
                        </div>
                      )}

                      {page.components && Array.isArray(page.components) && (
                        <div className="mb-3">
                          <h6 className="text-gray-300 font-medium mb-2">Components</h6>
                          <div className="flex flex-wrap gap-2">
                            {page.components.map((component: string, idx: number) => (
                              <span key={idx} className="px-2 py-1 bg-gray-700 text-gray-300 text-xs rounded">
                                {component}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}

                      {page.interactions && Array.isArray(page.interactions) && (
                        <div>
                          <h6 className="text-gray-300 font-medium mb-2">Interactions</h6>
                          <ul className="text-gray-400 text-sm space-y-1">
                            {page.interactions.map((interaction: string, idx: number) => (
                              <li key={idx} className="flex items-start gap-2">
                                <span className="text-red-400 mt-1">•</span>
                                {interaction}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Legacy mockups support */}
            {data.mockups && typeof data.mockups === "object" && (
              <div>
                <h4 className="text-white font-medium mb-4">Page Mockups</h4>
                {Object.entries(data.mockups).map(([pageName, mockup]) => (
                  <div key={pageName} className="mb-4 border border-gray-700 rounded-lg p-4">
                    <h5 className="text-white font-semibold mb-3">
                      {pageName.replace(/([A-Z])/g, " $1").trim()}
                    </h5>
                    <div className="bg-black border border-gray-600 p-6 rounded-lg overflow-x-auto">
                      <pre className="text-green-400 font-mono text-sm leading-relaxed whitespace-pre">
                        {String(mockup)}
                      </pre>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Components */}
            {data.components && (
              <div>
                <h4 className="text-white font-medium mb-2">Components</h4>
                {renderValue(data.components)}
              </div>
            )}

            {/* User Flow */}
            {data.userFlow && Array.isArray(data.userFlow) && (
              <div>
                <h4 className="text-white font-medium mb-3">User Flow</h4>
                <div className="space-y-2">
                  {data.userFlow.map((step: any, index: number) => (
                    <div key={index} className="flex items-start gap-3 p-3 bg-gray-800 rounded-lg">
                      <span className="flex-shrink-0 w-6 h-6 bg-red-600 text-white text-xs rounded-full flex items-center justify-center font-bold">
                        {step.step || index + 1}
                      </span>
                      <div className="flex-1">
                        <div className="text-white font-medium">{step.action}</div>
                        {step.page && <div className="text-gray-400 text-sm">Page: {step.page}</div>}
                        {step.result && <div className="text-gray-300 text-sm mt-1">{step.result}</div>}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Responsive Design */}
            <div>
              <h4 className="text-white font-medium mb-2">Responsive Design</h4>
              {renderValue(data.responsive)}
            </div>

            {/* Navigation */}
            {data.navigation && (
              <div>
                <h4 className="text-white font-medium mb-2">Navigation</h4>
                {renderValue(data.navigation)}
              </div>
            )}
          </div>
        )

      case "design":
        // Check if this is a comprehensive style guide from image analysis
        if (typeof data === "string" && data.includes("Style Guide")) {
          return (
            <div className="space-y-4">
              <div className="bg-blue-900/20 border border-blue-700 rounded-lg p-4 mb-4">
                <div className="flex items-center gap-2 mb-2">
                  <svg className="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <span className="text-blue-400 font-medium">Generated from Reference Images</span>
                </div>
                <p className="text-blue-300 text-sm">
                  This comprehensive style guide was automatically generated by analyzing your uploaded reference images using AI.
                </p>
              </div>
              <div className="prose prose-invert max-w-none">
                <div
                  className="text-gray-300 whitespace-pre-wrap"
                  dangerouslySetInnerHTML={{
                    __html: data
                      .replace(/\*\*(.*?)\*\*/g, '<strong class="text-white">$1</strong>')
                      .replace(/\*(.*?)\*/g, '<em class="text-gray-400">$1</em>')
                      .replace(/`(.*?)`/g, '<code class="bg-gray-800 px-1 py-0.5 rounded text-sm text-green-400">$1</code>')
                      .replace(/```css\n([\s\S]*?)\n```/g, '<pre class="bg-gray-900 p-4 rounded-lg overflow-x-auto"><code class="text-green-400">$1</code></pre>')
                      .replace(/```([\s\S]*?)```/g, '<pre class="bg-gray-900 p-4 rounded-lg overflow-x-auto"><code class="text-gray-300">$1</code></pre>')
                      .replace(/^(#{1,6})\s+(.+)$/gm, (match, hashes, title) => {
                        const level = hashes.length;
                        const className = level === 1 ? 'text-2xl font-bold text-white mt-8 mb-4 border-b border-gray-700 pb-2' :
                                         level === 2 ? 'text-xl font-semibold text-white mt-6 mb-3' :
                                         level === 3 ? 'text-lg font-medium text-white mt-5 mb-2' :
                                         'text-base font-medium text-white mt-4 mb-2';
                        return `<h${level} class="${className}">${title}</h${level}>`;
                      })
                      .replace(/\|(.*?)\|/g, (match, content) => {
                        // Simple table formatting
                        const cells = content.split('|').map(cell => cell.trim());
                        return `<div class="inline-block bg-gray-800 px-2 py-1 rounded text-sm mr-2 mb-1">${cells.join(' | ')}</div>`;
                      })
                      .replace(/#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})/g, '<span class="inline-block w-4 h-4 rounded border border-gray-600 mr-1" style="background-color: $&"></span><code class="text-blue-400">$&</code>')
                  }}
                />
              </div>
            </div>
          )
        }

        // Original design format for AI-generated guidelines
        return (
          <div className="space-y-4">
            <div>
              <h4 className="text-white font-medium mb-2">Theme</h4>
              {renderValue(data.theme)}
            </div>
            <div>
              <h4 className="text-white font-medium mb-2">Color Palette</h4>
              {data.colorPalette && typeof data.colorPalette === "object" ? (
                <div className="grid grid-cols-2 gap-2">
                  {Object.entries(data.colorPalette).map(([key, value]) => (
                    <div key={key} className="flex items-center gap-2">
                      <div
                        className="w-4 h-4 rounded border border-gray-600"
                        style={{ backgroundColor: typeof value === "string" ? value.split(" ")[0] : "#000" }}
                      ></div>
                      <span className="text-gray-300 capitalize text-sm">
                        {key}: {String(value)}
                      </span>
                    </div>
                  ))}
                </div>
              ) : (
                renderValue(data.colorPalette)
              )}
            </div>
            {data.typography && (
              <div>
                <h4 className="text-white font-medium mb-2">Typography</h4>
                {renderValue(data.typography)}
              </div>
            )}
            {data.effects && (
              <div>
                <h4 className="text-white font-medium mb-2">Visual Effects</h4>
                {renderValue(data.effects)}
              </div>
            )}
            {data.animations && (
              <div>
                <h4 className="text-white font-medium mb-2">Animations</h4>
                {renderValue(data.animations)}
              </div>
            )}
            {data.layout && (
              <div>
                <h4 className="text-white font-medium mb-2">Layout</h4>
                {renderValue(data.layout)}
              </div>
            )}
            {data.interactive && (
              <div>
                <h4 className="text-white font-medium mb-2">Interactive Elements</h4>
                {renderValue(data.interactive)}
              </div>
            )}
          </div>
        )

      case "database":
        return (
          <div className="space-y-6">
            {/* Database Type */}
            {data.databaseType && (
              <div>
                <h4 className="text-white font-medium mb-2">Database Type</h4>
                <span className="px-3 py-1 bg-red-600 text-white text-sm rounded-full">
                  {data.databaseType}
                </span>
              </div>
            )}

            {/* Tables */}
            {data.tables && Array.isArray(data.tables) && (
              <div>
                <h4 className="text-white font-medium mb-4">Tables</h4>
                <div className="space-y-4">
                  {data.tables.map((table: any, index: number) => (
                    <div key={index} className="border border-gray-700 rounded-lg p-4">
                      <div className="flex items-center gap-2 mb-3">
                        <h5 className="text-white font-semibold text-lg">{table.name}</h5>
                        {table.purpose && (
                          <span className="text-gray-400 text-sm">- {table.purpose}</span>
                        )}
                      </div>

                      {/* Columns */}
                      {table.columns && Array.isArray(table.columns) && (
                        <div className="mb-4">
                          <h6 className="text-gray-300 font-medium mb-2">Columns</h6>
                          <div className="overflow-x-auto">
                            <table className="w-full text-sm">
                              <thead>
                                <tr className="border-b border-gray-600">
                                  <th className="text-left text-gray-300 py-2">Name</th>
                                  <th className="text-left text-gray-300 py-2">Type</th>
                                  <th className="text-left text-gray-300 py-2">Constraints</th>
                                  <th className="text-left text-gray-300 py-2">Description</th>
                                </tr>
                              </thead>
                              <tbody>
                                {table.columns.map((column: any, colIndex: number) => (
                                  <tr key={colIndex} className="border-b border-gray-700">
                                    <td className="py-2 text-white font-mono">{column.name}</td>
                                    <td className="py-2 text-blue-400 font-mono">{column.type}</td>
                                    <td className="py-2">
                                      {column.constraints && Array.isArray(column.constraints) && (
                                        <div className="flex flex-wrap gap-1">
                                          {column.constraints.map((constraint: string, cIndex: number) => (
                                            <span key={cIndex} className="px-1 py-0.5 bg-yellow-600 text-black text-xs rounded">
                                              {constraint}
                                            </span>
                                          ))}
                                        </div>
                                      )}
                                    </td>
                                    <td className="py-2 text-gray-300 text-xs">{column.description}</td>
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                        </div>
                      )}

                      {/* Indexes */}
                      {table.indexes && Array.isArray(table.indexes) && table.indexes.length > 0 && (
                        <div className="mb-4">
                          <h6 className="text-gray-300 font-medium mb-2">Indexes</h6>
                          <div className="space-y-2">
                            {table.indexes.map((index: any, idxIndex: number) => (
                              <div key={idxIndex} className="bg-gray-800 p-3 rounded">
                                <div className="flex items-center gap-2 mb-1">
                                  <span className="text-white font-mono text-sm">{index.name}</span>
                                  <span className="px-2 py-0.5 bg-purple-600 text-white text-xs rounded">
                                    {index.type}
                                  </span>
                                </div>
                                <div className="text-gray-400 text-xs">
                                  Columns: {Array.isArray(index.columns) ? index.columns.join(', ') : index.columns}
                                </div>
                                {index.purpose && (
                                  <div className="text-gray-400 text-xs mt-1">{index.purpose}</div>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Relationships */}
                      {table.relationships && Array.isArray(table.relationships) && table.relationships.length > 0 && (
                        <div>
                          <h6 className="text-gray-300 font-medium mb-2">Relationships</h6>
                          <div className="space-y-2">
                            {table.relationships.map((rel: any, relIndex: number) => (
                              <div key={relIndex} className="flex items-center gap-2 text-sm">
                                <span className="px-2 py-1 bg-green-600 text-white text-xs rounded">
                                  {rel.type}
                                </span>
                                <span className="text-white">{rel.relatedTable}</span>
                                <span className="text-gray-400">via {rel.foreignKey}</span>
                                {rel.description && (
                                  <span className="text-gray-500">- {rel.description}</span>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Views */}
            {data.views && Array.isArray(data.views) && data.views.length > 0 && (
              <div>
                <h4 className="text-white font-medium mb-3">Views</h4>
                <div className="space-y-3">
                  {data.views.map((view: any, index: number) => (
                    <div key={index} className="bg-gray-800 p-4 rounded-lg">
                      <h5 className="text-white font-semibold mb-2">{view.name}</h5>
                      {view.purpose && (
                        <p className="text-gray-300 text-sm mb-2">{view.purpose}</p>
                      )}
                      {view.tables && Array.isArray(view.tables) && (
                        <div className="text-gray-400 text-xs">
                          Tables: {view.tables.join(', ')}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Performance Considerations */}
            {data.performance && (
              <div>
                <h4 className="text-white font-medium mb-3">Performance & Scaling</h4>
                <div className="bg-gray-800 p-4 rounded-lg space-y-3">
                  {data.performance.considerations && (
                    <div>
                      <h6 className="text-gray-300 font-medium mb-2">Considerations</h6>
                      {renderValue(data.performance.considerations)}
                    </div>
                  )}
                  {data.performance.optimizations && (
                    <div>
                      <h6 className="text-gray-300 font-medium mb-2">Optimizations</h6>
                      {renderValue(data.performance.optimizations)}
                    </div>
                  )}
                  {data.performance.scalingStrategy && (
                    <div>
                      <h6 className="text-gray-300 font-medium mb-2">Scaling Strategy</h6>
                      <p className="text-gray-300 text-sm">{data.performance.scalingStrategy}</p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Security */}
            {data.security && (
              <div>
                <h4 className="text-white font-medium mb-3">Security</h4>
                <div className="bg-gray-800 p-4 rounded-lg space-y-3">
                  {data.security.authentication && (
                    <div>
                      <h6 className="text-gray-300 font-medium mb-2">Authentication</h6>
                      <p className="text-gray-300 text-sm">{data.security.authentication}</p>
                    </div>
                  )}
                  {data.security.authorization && (
                    <div>
                      <h6 className="text-gray-300 font-medium mb-2">Authorization</h6>
                      <p className="text-gray-300 text-sm">{data.security.authorization}</p>
                    </div>
                  )}
                  {data.security.dataProtection && (
                    <div>
                      <h6 className="text-gray-300 font-medium mb-2">Data Protection</h6>
                      {renderValue(data.security.dataProtection)}
                    </div>
                  )}
                  {data.security.compliance && (
                    <div>
                      <h6 className="text-gray-300 font-medium mb-2">Compliance</h6>
                      <div className="flex flex-wrap gap-2">
                        {Array.isArray(data.security.compliance) ?
                          data.security.compliance.map((comp: string, index: number) => (
                            <span key={index} className="px-2 py-1 bg-blue-600 text-white text-xs rounded">
                              {comp}
                            </span>
                          )) :
                          <span className="text-gray-300">{data.security.compliance}</span>
                        }
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        )

      case "filesystem":
        return (
          <div className="space-y-6">
            {/* Project Structure Tree */}
            {data.structure && data.structure.folders && (
              <div>
                <h4 className="text-white font-medium mb-4 flex items-center gap-2">
                  <FolderTree className="w-5 h-5 text-purple-400" />
                  Project Structure
                </h4>
                <div className="bg-gray-900 p-4 rounded-lg">
                  <div className="space-y-2 font-mono text-sm">
                    {data.structure.folders.map((folder: any, index: number) => (
                      <div key={index} className="space-y-1">
                        <div className="flex items-center gap-2 text-blue-400">
                          <Folder className="w-4 h-4" />
                          <span className="font-semibold">{folder.name}/</span>
                          {folder.purpose && (
                            <span className="text-gray-400 text-xs">- {folder.purpose}</span>
                          )}
                        </div>
                        {folder.subfolders && folder.subfolders.length > 0 && (
                          <div className="ml-6 space-y-1">
                            {folder.subfolders.map((subfolder: string, subIndex: number) => (
                              <div key={subIndex} className="flex items-center gap-2 text-blue-300">
                                <Folder className="w-3 h-3" />
                                <span>{subfolder}/</span>
                              </div>
                            ))}
                          </div>
                        )}
                        {folder.keyFiles && folder.keyFiles.length > 0 && (
                          <div className="ml-6 space-y-1">
                            {folder.keyFiles.map((file: string, fileIndex: number) => (
                              <div key={fileIndex} className="flex items-center gap-2 text-gray-300">
                                <File className="w-3 h-3" />
                                <span>{file}</span>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Key Files */}
            {data.keyFiles && Array.isArray(data.keyFiles) && (
              <div>
                <h4 className="text-white font-medium mb-4 flex items-center gap-2">
                  <File className="w-5 h-5 text-green-400" />
                  Key Files
                </h4>
                <div className="space-y-3">
                  {data.keyFiles.map((file: any, index: number) => (
                    <div key={index} className="p-4 bg-gray-800 rounded-lg border border-gray-700">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <File className="w-4 h-4 text-green-400" />
                          <span className="text-white font-mono">{file.path}</span>
                        </div>
                        {file.priority && (
                          <span className={`px-2 py-1 text-xs rounded ${
                            file.priority === 'high' ? 'bg-red-600 text-white' :
                            file.priority === 'medium' ? 'bg-yellow-600 text-white' :
                            'bg-gray-600 text-gray-300'
                          }`}>
                            {file.priority}
                          </span>
                        )}
                      </div>
                      {file.purpose && (
                        <p className="text-gray-300 text-sm mb-2">{file.purpose}</p>
                      )}
                      {file.dependencies && file.dependencies.length > 0 && (
                        <div className="text-xs text-gray-400">
                          <span className="font-medium">Dependencies:</span> {file.dependencies.join(", ")}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Conventions */}
            {data.conventions && (
              <div>
                <h4 className="text-white font-medium mb-4 flex items-center gap-2">
                  <BookOpen className="w-5 h-5 text-yellow-400" />
                  Development Conventions
                </h4>
                <div className="space-y-3">
                  {data.conventions.naming && (
                    <div className="p-3 bg-gray-800 rounded-lg">
                      <h5 className="text-yellow-400 font-medium mb-2">Naming Conventions</h5>
                      <p className="text-gray-300 text-sm">{data.conventions.naming}</p>
                    </div>
                  )}
                  {data.conventions.organization && (
                    <div className="p-3 bg-gray-800 rounded-lg">
                      <h5 className="text-yellow-400 font-medium mb-2">File Organization</h5>
                      <p className="text-gray-300 text-sm">{data.conventions.organization}</p>
                    </div>
                  )}
                  {data.conventions.imports && (
                    <div className="p-3 bg-gray-800 rounded-lg">
                      <h5 className="text-yellow-400 font-medium mb-2">Import/Export Patterns</h5>
                      <p className="text-gray-300 text-sm">{data.conventions.imports}</p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Build System */}
            {data.buildSystem && (
              <div>
                <h4 className="text-white font-medium mb-4 flex items-center gap-2">
                  <Settings className="w-5 h-5 text-orange-400" />
                  Build System
                </h4>
                <div className="space-y-3">
                  {data.buildSystem.configFiles && data.buildSystem.configFiles.length > 0 && (
                    <div className="p-3 bg-gray-800 rounded-lg">
                      <h5 className="text-orange-400 font-medium mb-2">Configuration Files</h5>
                      <div className="flex flex-wrap gap-2">
                        {data.buildSystem.configFiles.map((file: string, index: number) => (
                          <span key={index} className="px-2 py-1 bg-gray-700 text-gray-300 text-xs rounded font-mono">
                            {file}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                  {data.buildSystem.scripts && (
                    <div className="p-3 bg-gray-800 rounded-lg">
                      <h5 className="text-orange-400 font-medium mb-2">Build Scripts</h5>
                      <div className="space-y-2">
                        {Object.entries(data.buildSystem.scripts).map(([script, command]: [string, any]) => (
                          <div key={script} className="flex items-center gap-3">
                            <span className="text-gray-400 font-mono text-sm w-16">{script}:</span>
                            <span className="text-gray-300 font-mono text-sm bg-gray-700 px-2 py-1 rounded">
                              {String(command)}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Fallback for legacy structure */}
            {!data.structure && data.folders && (
              <div>
                <h4 className="text-white font-medium mb-2">Key Folders</h4>
                {renderValue(data.folders)}
              </div>
            )}
            {!data.keyFiles && data.files && (
              <div>
                <h4 className="text-white font-medium mb-2">Files</h4>
                {renderValue(data.files)}
              </div>
            )}
            {!data.conventions && data.organization && (
              <div>
                <h4 className="text-white font-medium mb-2">Organization</h4>
                {renderValue(data.organization)}
              </div>
            )}
          </div>
        )

      case "workflow":
        return (
          <div className="space-y-4">
            {data.steps && (
              <div>
                <h4 className="text-white font-medium mb-2">Process Steps</h4>
                {Array.isArray(data.steps) ? (
                  <div className="space-y-2">
                    {data.steps.map((step: any, index: number) => {
                      const stepRender = renderStepObject(step, index)
                      if (stepRender) return stepRender
                      return (
                        <div key={index} className="flex items-start gap-3 p-3 bg-gray-800 rounded-lg">
                          <span className="flex-shrink-0 w-6 h-6 bg-red-600 text-white text-xs rounded-full flex items-center justify-center font-bold">
                            {index + 1}
                          </span>
                          <div className="flex-1 text-gray-300">
                            {typeof step === "object" ? JSON.stringify(step) : String(step)}
                          </div>
                        </div>
                      )
                    })}
                  </div>
                ) : (
                  renderValue(data.steps)
                )}
              </div>
            )}
            {data.logic && (
              <div>
                <h4 className="text-white font-medium mb-2">Logic</h4>
                {renderValue(data.logic)}
              </div>
            )}
            {data.integrations && (
              <div>
                <h4 className="text-white font-medium mb-2">Integrations</h4>
                {renderValue(data.integrations)}
              </div>
            )}
            {data.dataFlow && (
              <div>
                <h4 className="text-white font-medium mb-2">Data Flow</h4>
                {renderValue(data.dataFlow)}
              </div>
            )}
          </div>
        )

      case "tasks":
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h4 className="text-white font-medium mb-2">Total Tasks</h4>
                {renderValue(data.totalTasks)}
              </div>
              <div>
                <h4 className="text-white font-medium mb-2">Estimate</h4>
                {renderValue(data.estimate)}
              </div>
            </div>
            {data.categories && (
              <div>
                <h4 className="text-white font-medium mb-2">Categories</h4>
                {renderValue(data.categories)}
              </div>
            )}
            {data.priority && (
              <div>
                <h4 className="text-white font-medium mb-2">Priority</h4>
                {renderValue(data.priority)}
              </div>
            )}
            {data.phases && (
              <div>
                <h4 className="text-white font-medium mb-2">Phases</h4>
                {renderValue(data.phases)}
              </div>
            )}
          </div>
        )

      case "scaffold":
        return (
          <div className="space-y-6">
            {/* Setup Instructions */}
            {data.setupInstructions && Array.isArray(data.setupInstructions) && (
              <div>
                <h4 className="text-white font-medium mb-4">Setup Instructions</h4>
                <div className="space-y-3">
                  {data.setupInstructions.map((instruction: any, index: number) => (
                    <div key={index} className="flex items-start gap-3 p-4 bg-gray-800 rounded-lg">
                      <span className="flex-shrink-0 w-6 h-6 bg-red-600 text-white text-xs rounded-full flex items-center justify-center font-bold">
                        {instruction.step}
                      </span>
                      <div className="flex-1">
                        <h5 className="text-white font-semibold mb-1">{instruction.title}</h5>
                        {instruction.command && (
                          <div className="bg-black p-2 rounded mb-2">
                            <code className="text-green-400 font-mono text-sm">{instruction.command}</code>
                          </div>
                        )}
                        <p className="text-gray-300 text-sm">{instruction.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Project Structure */}
            {data.projectStructure && (
              <div>
                <h4 className="text-white font-medium mb-4">Project Structure</h4>

                {/* Root Files */}
                {data.projectStructure.rootFiles && Array.isArray(data.projectStructure.rootFiles) && (
                  <div className="mb-6">
                    <h5 className="text-gray-300 font-medium mb-3">Root Files</h5>
                    <div className="space-y-3">
                      {data.projectStructure.rootFiles.map((file: any, index: number) => (
                        <div key={index} className="border border-gray-700 rounded-lg">
                          <div className="flex items-center justify-between p-3 bg-gray-800 rounded-t-lg">
                            <div className="flex items-center gap-2">
                              <span className="text-white font-mono text-sm">{file.name}</span>
                              {file.description && (
                                <span className="text-gray-400 text-xs">- {file.description}</span>
                              )}
                            </div>
                          </div>
                          {file.content && (
                            <div className="p-4 bg-black rounded-b-lg overflow-x-auto">
                              <pre className="text-green-400 font-mono text-xs whitespace-pre">
                                {file.content}
                              </pre>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Folders */}
                {data.projectStructure.folders && Array.isArray(data.projectStructure.folders) && (
                  <div>
                    <h5 className="text-gray-300 font-medium mb-3">Folders & Files</h5>
                    <div className="space-y-4">
                      {data.projectStructure.folders.map((folder: any, folderIndex: number) => (
                        <div key={folderIndex} className="border border-gray-700 rounded-lg">
                          <div className="p-3 bg-gray-800 rounded-t-lg">
                            <h6 className="text-white font-semibold">📁 {folder.name}/</h6>
                          </div>
                          {folder.files && Array.isArray(folder.files) && (
                            <div className="p-3 space-y-3">
                              {folder.files.map((file: any, fileIndex: number) => (
                                <div key={fileIndex} className="border border-gray-600 rounded">
                                  <div className="flex items-center justify-between p-2 bg-gray-700 rounded-t">
                                    <div className="flex items-center gap-2">
                                      <span className="text-white font-mono text-sm">📄 {file.name}</span>
                                      {file.description && (
                                        <span className="text-gray-400 text-xs">- {file.description}</span>
                                      )}
                                    </div>
                                  </div>
                                  {file.content && (
                                    <div className="p-3 bg-black rounded-b overflow-x-auto">
                                      <pre className="text-green-400 font-mono text-xs whitespace-pre">
                                        {file.content}
                                      </pre>
                                    </div>
                                  )}
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Environment Setup */}
            {data.environmentSetup && (
              <div>
                <h4 className="text-white font-medium mb-4">Environment Setup</h4>
                <div className="space-y-4">
                  {data.environmentSetup.envVariables && Array.isArray(data.environmentSetup.envVariables) && (
                    <div>
                      <h5 className="text-gray-300 font-medium mb-3">Environment Variables</h5>
                      <div className="space-y-2">
                        {data.environmentSetup.envVariables.map((envVar: any, index: number) => (
                          <div key={index} className="p-3 bg-gray-800 rounded-lg">
                            <div className="flex items-center gap-2 mb-1">
                              <span className="text-white font-mono text-sm">{envVar.name}</span>
                              {envVar.required && (
                                <span className="px-2 py-0.5 bg-red-600 text-white text-xs rounded">Required</span>
                              )}
                            </div>
                            <p className="text-gray-300 text-sm mb-1">{envVar.description}</p>
                            {envVar.example && (
                              <div className="bg-black p-2 rounded">
                                <code className="text-green-400 font-mono text-xs">{envVar.example}</code>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {data.environmentSetup.configFiles && Array.isArray(data.environmentSetup.configFiles) && (
                    <div>
                      <h5 className="text-gray-300 font-medium mb-3">Configuration Files</h5>
                      <div className="space-y-3">
                        {data.environmentSetup.configFiles.map((configFile: any, index: number) => (
                          <div key={index} className="border border-gray-700 rounded-lg">
                            <div className="p-3 bg-gray-800 rounded-t-lg">
                              <div className="flex items-center gap-2">
                                <span className="text-white font-mono text-sm">{configFile.name}</span>
                                {configFile.description && (
                                  <span className="text-gray-400 text-xs">- {configFile.description}</span>
                                )}
                              </div>
                            </div>
                            {configFile.content && (
                              <div className="p-3 bg-black rounded-b-lg overflow-x-auto">
                                <pre className="text-green-400 font-mono text-xs whitespace-pre">
                                  {configFile.content}
                                </pre>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Scripts */}
            {data.scripts && (
              <div>
                <h4 className="text-white font-medium mb-4">Available Scripts</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {data.scripts.development && Array.isArray(data.scripts.development) && (
                    <div>
                      <h5 className="text-gray-300 font-medium mb-3">Development</h5>
                      <div className="space-y-2">
                        {data.scripts.development.map((script: any, index: number) => (
                          <div key={index} className="p-3 bg-gray-800 rounded-lg">
                            <div className="text-white font-semibold mb-1">{script.name}</div>
                            <div className="bg-black p-2 rounded mb-1">
                              <code className="text-green-400 font-mono text-sm">{script.command}</code>
                            </div>
                            <p className="text-gray-300 text-xs">{script.description}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {data.scripts.production && Array.isArray(data.scripts.production) && (
                    <div>
                      <h5 className="text-gray-300 font-medium mb-3">Production</h5>
                      <div className="space-y-2">
                        {data.scripts.production.map((script: any, index: number) => (
                          <div key={index} className="p-3 bg-gray-800 rounded-lg">
                            <div className="text-white font-semibold mb-1">{script.name}</div>
                            <div className="bg-black p-2 rounded mb-1">
                              <code className="text-green-400 font-mono text-sm">{script.command}</code>
                            </div>
                            <p className="text-gray-300 text-xs">{script.description}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Documentation */}
            {data.documentation && (
              <div>
                <h4 className="text-white font-medium mb-4">Documentation</h4>
                <div className="space-y-4">
                  {data.documentation.readme && (
                    <div className="border border-gray-700 rounded-lg">
                      <div className="p-3 bg-gray-800 rounded-t-lg">
                        <h5 className="text-white font-semibold">README.md</h5>
                      </div>
                      <div className="p-4 bg-black rounded-b-lg overflow-x-auto">
                        <pre className="text-green-400 font-mono text-xs whitespace-pre-wrap">
                          {data.documentation.readme}
                        </pre>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Next Steps */}
            {data.nextSteps && Array.isArray(data.nextSteps) && (
              <div>
                <h4 className="text-white font-medium mb-4">Next Steps</h4>
                <div className="bg-gray-800 p-4 rounded-lg">
                  <ol className="space-y-2">
                    {data.nextSteps.map((step: string, index: number) => (
                      <li key={index} className="flex items-start gap-3">
                        <span className="flex-shrink-0 w-6 h-6 bg-red-600 text-white text-xs rounded-full flex items-center justify-center font-bold">
                          {index + 1}
                        </span>
                        <span className="text-gray-300">{step}</span>
                      </li>
                    ))}
                  </ol>
                </div>
              </div>
            )}
          </div>
        )

      default:
        return (
          <div className="space-y-4">
            <h4 className="text-white font-medium mb-2">Section Data</h4>
            <p className="text-gray-400 text-sm mb-4">
              This section doesn't have a custom display format yet. You can view the raw data below.
            </p>
            <RawDataToggle data={data} label="Section JSON" />
          </div>
        )
    }
  }

  const getSectionTitle = (sectionId: string) => {
    const titles: Record<string, string> = {
      analyze: "Analysis",
      clarify: "Clarification",
      summary: "Summary",
      techstack: "Tech Stack",
      prd: "Requirements",
      "context-profile": "AI Agent Context Profile",
      wireframes: "Wireframes",
      design: "Design",
      database: "Database Schema",
      filesystem: "File Structure",
      workflow: "Workflow",
      tasks: "Tasks",
      scaffold: "Project Scaffold",
    }
    return titles[sectionId] || sectionId.charAt(0).toUpperCase() + sectionId.slice(1)
  }

  // If no sections available, show a message
  if (sections.length === 0) {
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">No Results Available</h2>
          <p className="text-gray-400 mb-6">The planning process hasn't generated any results yet.</p>
          <Button onClick={onBack} className="bg-red-600 hover:bg-red-700">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Planning
          </Button>
        </div>
      </div>
    )
  }

  // Add export functionality
  const handleExport = () => {
    const exportData = {
      prompt: userPrompt,
      timestamp: new Date().toISOString(),
      results: results,
    }

    const dataStr = JSON.stringify(exportData, null, 2)
    const dataBlob = new Blob([dataStr], { type: "application/json" })
    const url = URL.createObjectURL(dataBlob)

    const link = document.createElement("a")
    link.href = url
    link.download = `project-plan-${new Date().toISOString().split("T")[0]}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  return (
    <div className="min-h-screen bg-black text-white relative">
      {/* Powered by AP3X - Bottom Right Corner */}
      <div className="fixed bottom-4 right-4 z-50">
        <div className="text-gray-400 text-xs" style={{opacity:0.4}}>
          <span style={{ fontWeight: 'bold' }}>
            Powered by{' '}
            <span style={{ color: 'white' }}>AP3</span>
            <span style={{ color: '#ff2d55', textShadow: '0 0 8px #ff2d55, 0 0 16px #ff2d55' }}>X</span>
          </span>
        </div>
      </div>
      {/* Header */}
      <div className="border-b border-gray-800 p-4">
        <div className="max-w-6xl mx-auto flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button onClick={onBack} variant="ghost" size="sm" className="text-gray-400 hover:text-white">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-xl font-semibold">Project Plan</h1>
            </div>
          </div>
          <Button
            onClick={handleExport}
            variant="outline"
            size="sm"
            className="border-gray-600 text-gray-300 hover:bg-gray-800 bg-transparent"
          >
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      <div className="max-w-6xl mx-auto flex">
        {/* Sidebar */}
        <div className="w-64 border-r border-gray-800 min-h-screen p-4">
          <nav className="space-y-2">
            {sections.map((sectionId) => {
              const Icon = SECTION_ICONS[sectionId] || FileText
              const isActive = activeSection === sectionId

              return (
                <button
                  key={sectionId}
                  onClick={() => setActiveSection(sectionId)}
                  className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left transition-colors ${
                    isActive ? "bg-red-600 text-white" : "text-gray-400 hover:text-white hover:bg-gray-800"
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span className="text-sm">{getSectionTitle(sectionId)}</span>
                </button>
              )
            })}
          </nav>
        </div>

        {/* Content */}
        <div className="flex-1 p-6">
          <motion.div
            key={activeSection}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <h2 className="text-2xl font-bold mb-6">{getSectionTitle(activeSection)}</h2>
            <div className="bg-gray-900/50 rounded-lg p-6">
              {renderSectionContent(activeSection, results[activeSection])}
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  )
}
