/**
 * Unified Context Engine for AG3NT System
 * Combines agent coordination, codebase analysis, and real-time context management
 * Merges capabilities from both TypeScript and standalone context engines
 */

import type { ProjectContext } from "@/types/planning"
import type {
  ContextNode,
  ContextualHint,
  CrossReference,
  ContextIntegrityReport,
  ContextIntegrityIssue
} from "@/types/context-engine"
import { CrossReferenceValidator } from "./cross-reference-validator"
import { ContextEnrichmentPipeline } from "./context-enrichment-pipeline"
import { UniversalMCPIntegration } from "./universal-mcp-integration"
import { ContextIntegrityTracker } from "./context-integrity-tracker"
import { AgenticRAGEngine } from "./agentic-rag-engine"
import type { Agentic<PERSON>uery, SemanticSearchResult } from "./agentic-rag-engine"
import { HybridIntelligenceEngine } from "./hybrid-intelligence-engine"
import type { HybridQuery, HybridResult } from "./hybrid-intelligence-engine"
import neo4j from 'neo4j-driver'
import Redis from 'ioredis'
import { EventEmitter } from 'events'

/**
 * Agent Types supported by the unified context engine
 */
export type AgentType = 'planner' | 'executor' | 'workflow' | 'project-planner' | 'task-breakdown' | 'code-generator'

export interface AgentContextScope {
  agentType: AgentType
  operationId: string
  parentContext?: string
  requiredCapabilities: string[]
  contextFilters: string[]
}

export interface CodebaseContext {
  projectPath: string
  files: Map<string, FileContext>
  dependencies: Map<string, string[]>
  symbols: Map<string, SymbolContext>
  lastAnalyzed: Date
}

export interface FileContext {
  path: string
  language: string
  ast: any
  symbols: string[]
  dependencies: string[]
  lastModified: Date
  complexity: number
}

export interface SymbolContext {
  name: string
  type: 'function' | 'class' | 'variable' | 'interface' | 'type'
  file: string
  line: number
  scope: string
  references: string[]
  documentation?: string
}

export interface UnifiedContextConfig {
  // Neo4j Configuration
  neo4j: {
    uri: string
    user: string
    password: string
    database?: string
  }
  
  // Redis Configuration
  redis: {
    host: string
    port: number
    password?: string
  }
  
  // Processing Configuration
  processing: {
    maxConcurrent: number
    batchSize: number
    enableRealTimeUpdates: boolean
  }
  
  // MCP Configuration
  mcp: {
    enableContext7: boolean
    enableSequentialThinking: boolean
    timeout: number
  }
}

/**
 * Unified Context Engine
 * Provides comprehensive context management for all AG3NT agents
 */
export class UnifiedContextEngine extends EventEmitter {
  // Agent coordination (from TypeScript engine)
  private context: ProjectContext
  private memoryStore: Map<string, any> = new Map()
  private decisionGraph: Map<string, ContextNode> = new Map()
  private contextVersions: Map<string, ProjectContext> = new Map()
  private crossReferences: Map<string, string[]> = new Map()
  private agentContexts: Map<string, AgentContextScope> = new Map()
  private sharedState: Map<string, any> = new Map()
  
  // Codebase analysis (from standalone engine)
  private neo4jDriver: neo4j.Driver
  private redisClient: Redis
  private codebaseContext: CodebaseContext | null = null
  private isInitialized: boolean = false
  
  // Shared components
  private contextIntegrity: ContextIntegrityTracker
  private crossReferenceValidator: CrossReferenceValidator
  private enrichmentPipeline: ContextEnrichmentPipeline
  private universalMCP: UniversalMCPIntegration
  private agenticRAG: AgenticRAGEngine
  private hybridIntelligence: HybridIntelligenceEngine
  
  // Configuration
  private config: UnifiedContextConfig

  constructor(initialContext: Partial<ProjectContext>, config: UnifiedContextConfig) {
    super()
    
    this.config = config
    this.context = {
      originalPrompt: "",
      projectType: "",
      features: [],
      clarifications: {},
      summary: "",
      techStack: {},
      prd: {},
      wireframes: [],
      filesystem: {},
      workflow: {},
      tasks: [],
      ...initialContext,
    }

    // Initialize shared components
    this.contextIntegrity = new ContextIntegrityTracker()
    this.crossReferenceValidator = new CrossReferenceValidator()
    this.enrichmentPipeline = new ContextEnrichmentPipeline()
    this.universalMCP = new UniversalMCPIntegration()
    this.agenticRAG = new AgenticRAGEngine()
    this.hybridIntelligence = new HybridIntelligenceEngine(this.neo4jDriver, this.agenticRAG)
    
    // Initialize databases and components
    this.initializeDatabases()
    this.initializeDecisionGraph()
    this.initializeSharedState()
    this.initializeAgenticRAG()
  }

  /**
   * Initialize database connections
   */
  private async initializeDatabases(): Promise<void> {
    try {
      // Initialize Neo4j
      this.neo4jDriver = neo4j.driver(
        this.config.neo4j.uri,
        neo4j.auth.basic(this.config.neo4j.user, this.config.neo4j.password)
      )
      
      // Test Neo4j connection
      const session = this.neo4jDriver.session()
      await session.run('RETURN 1')
      await session.close()
      
      // Initialize Redis
      this.redisClient = new Redis({
        host: this.config.redis.host,
        port: this.config.redis.port,
        password: this.config.redis.password,
        retryDelayOnFailover: 100,
        maxRetriesPerRequest: 3
      })
      
      // Test Redis connection
      await this.redisClient.ping()
      
      this.isInitialized = true
      this.emit('initialized')
      
    } catch (error) {
      console.error('Failed to initialize databases:', error)
      throw error
    }
  }

  /**
   * Initialize decision graph for planning coordination
   */
  private initializeDecisionGraph(): void {
    const planningSteps = [
      'analyze', 'clarify', 'summary', 'techstack', 'prd', 
      'wireframes', 'design', 'database', 'filesystem', 'workflow', 'tasks'
    ]

    planningSteps.forEach((step, index) => {
      const dependencies = index > 0 ? [planningSteps[index - 1]] : []
      
      this.decisionGraph.set(step, {
        id: step,
        dependencies,
        outputs: index < planningSteps.length - 1 ? [planningSteps[index + 1]] : [],
        contextRequirements: this.getStepContextRequirements(step),
        validationRules: this.getStepValidationRules(step),
        crossReferences: [],
        timestamp: new Date().toISOString(),
        version: "1.0.0"
      })
    })
  }

  /**
   * Initialize shared state for agent coordination
   */
  private initializeSharedState(): void {
    this.sharedState.set('project_status', 'initializing')
    this.sharedState.set('active_agents', new Set())
    this.sharedState.set('execution_timeline', [])
    this.sharedState.set('agent_handoffs', [])
    this.sharedState.set('current_task', null)
    this.sharedState.set('system_health', { status: 'healthy', timestamp: new Date().toISOString() })
  }

  /**
   * Register an agent with the unified context engine
   */
  registerAgent(agentType: AgentType, operationId: string, scope: Partial<AgentContextScope> = {}): string {
    const agentId = `${agentType}_${operationId}_${Date.now()}`

    const agentScope: AgentContextScope = {
      agentType,
      operationId,
      parentContext: scope.parentContext,
      requiredCapabilities: scope.requiredCapabilities || this.getDefaultCapabilities(agentType),
      contextFilters: scope.contextFilters || this.getDefaultFilters(agentType)
    }

    this.agentContexts.set(agentId, agentScope)
    
    // Update shared state
    const activeAgents = this.sharedState.get('active_agents') as Set<string>
    activeAgents.add(agentId)
    
    this.emit('agent_registered', { agentId, agentType, operationId })
    return agentId
  }

  /**
   * Get comprehensive context for a specific agent
   */
  async getAgentContext(agentId: string): Promise<any> {
    const agentScope = this.agentContexts.get(agentId)
    if (!agentScope) {
      throw new Error(`Agent ${agentId} not registered`)
    }

    // Get planning context
    const planningContext = this.getRelevantContext('all')
    
    // Get codebase context if available
    const codebaseContext = await this.getCodebaseContext(agentScope)
    
    // Filter context for agent-specific needs
    const filteredContext = this.filterContextForAgent(planningContext, agentScope)

    return {
      ...filteredContext,
      codebase: codebaseContext,
      agentMetadata: {
        agentId,
        agentType: agentScope.agentType,
        operationId: agentScope.operationId,
        capabilities: agentScope.requiredCapabilities,
        sharedState: this.getSharedStateForAgent(agentScope.agentType)
      }
    }
  }

  /**
   * Process and analyze a codebase
   */
  async processCodebase(projectPath: string): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('Context engine not initialized')
    }

    try {
      this.emit('codebase_analysis_started', { projectPath })

      // Initialize codebase context
      this.codebaseContext = {
        projectPath,
        files: new Map(),
        dependencies: new Map(),
        symbols: new Map(),
        lastAnalyzed: new Date()
      }

      // Process files and build AST
      await this.analyzeCodebaseFiles(projectPath)

      // Build dependency graph
      await this.buildDependencyGraph()

      // Store in Neo4j
      await this.storeCodebaseInGraph()

      // Cache in Redis
      await this.cacheCodebaseContext()

      // Process with agentic RAG for semantic understanding
      await this.agenticRAG.processCodebase(this.codebaseContext)

      this.emit('codebase_analysis_completed', {
        projectPath,
        fileCount: this.codebaseContext.files.size,
        symbolCount: this.codebaseContext.symbols.size
      })

    } catch (error) {
      this.emit('codebase_analysis_failed', { projectPath, error: error.message })
      throw error
    }
  }

  /**
   * Get codebase context for an agent
   */
  private async getCodebaseContext(agentScope: AgentContextScope): Promise<any> {
    if (!this.codebaseContext) {
      return null
    }

    // Filter codebase context based on agent capabilities
    const relevantFiles = this.filterRelevantFiles(agentScope)
    const relevantSymbols = this.filterRelevantSymbols(agentScope)

    return {
      projectPath: this.codebaseContext.projectPath,
      files: relevantFiles,
      symbols: relevantSymbols,
      dependencies: this.getRelevantDependencies(relevantFiles),
      lastAnalyzed: this.codebaseContext.lastAnalyzed
    }
  }

  /**
   * Update context with new data (from planning steps)
   */
  updateContext(step: string, data: any): void {
    const versionKey = `${step}_${Date.now()}`
    this.contextVersions.set(versionKey, { ...this.context })

    // Update main context
    this.context = { ...this.context, [step]: data }

    // Enhanced memory store with full context propagation
    const contextNode = this.decisionGraph.get(step)
    if (contextNode) {
      const enrichedData = this.enrichContextData(step, data, contextNode)

      this.memoryStore.set(`step_${step}`, {
        data: enrichedData,
        originalData: data,
        timestamp: new Date().toISOString(),
        dependencies: contextNode.dependencies,
        upstreamContext: this.gatherUpstreamContext(contextNode.dependencies),
        crossReferences: this.updateCrossReferences(step, enrichedData),
        integrityHash: this.contextIntegrity.generateHash(enrichedData),
        version: versionKey
      })

      // Update decision graph node
      contextNode.timestamp = new Date().toISOString()
      contextNode.crossReferences = this.findCrossReferences(step, enrichedData)
    }

    this.emit('context_updated', { step, data })
  }

  /**
   * Get relevant context for current planning step
   */
  getRelevantContext(step: string): any {
    const dependencies = this.getStepDependencies(step)
    const relevantContext: any = {}

    dependencies.forEach((dep) => {
      const stepData = this.memoryStore.get(`step_${dep}`)
      if (stepData) {
        relevantContext[dep] = stepData.data
      }
    })

    return {
      current: this.context,
      dependencies: relevantContext,
      metadata: {
        projectType: this.context.projectType,
        complexity: this.assessComplexity(),
        timestamp: new Date().toISOString(),
      },
    }
  }

  /**
   * Enhanced context with RAG and MCP integration
   */
  async enhanceWithRAG(step: string, prompt: string): Promise<any> {
    const baseContext = this.getRelevantContext(step)

    // Get enriched context from pipeline
    const enrichedContext = await this.enrichmentPipeline.enrichContext(this.context, step)

    // Get external knowledge via MCP
    const ragKnowledge = await this.universalMCP.getDocumentation(
      this.context.techStack?.frontend || 'react',
      step
    )

    return {
      context: baseContext,
      enrichedContext: enrichedContext.enhancedContext,
      enrichments: enrichedContext.enrichments,
      enrichmentConfidence: enrichedContext.confidence,
      enrichmentRecommendations: enrichedContext.recommendations,
      externalKnowledge: ragKnowledge,
      bestPractices: this.getBestPractices(step),
      templates: this.getTemplates(step),
      contextualHints: this.generateContextualHints(step, this.context, baseContext.dependencies)
    }
  }

  /**
   * Perform sequential thinking via MCP
   */
  async performSequentialThinking(thought: string, context: any = {}): Promise<any> {
    return await this.universalMCP.think(thought, context)
  }

  /**
   * Get documentation via MCP
   */
  async getDocumentation(libraryName: string, topic?: string): Promise<any> {
    return await this.universalMCP.getDocumentation(libraryName, topic)
  }

  /**
   * Perform intelligent semantic search using agentic RAG
   */
  async semanticSearch(query: AgenticQuery): Promise<SemanticSearchResult> {
    if (!this.isInitialized) {
      throw new Error('Context engine not initialized')
    }

    return await this.agenticRAG.search(query)
  }

  /**
   * Perform hybrid search combining Neo4j structural intelligence with agentic RAG semantic intelligence
   */
  async hybridSearch(query: HybridQuery): Promise<HybridResult> {
    if (!this.isInitialized) {
      throw new Error('Context engine not initialized')
    }

    return await this.hybridIntelligence.executeHybridQuery(query)
  }

  /**
   * Intelligent codebase query combining the best of both worlds
   */
  async intelligentQuery(naturalLanguageQuery: string, options: {
    strategy?: 'semantic-first' | 'structural-first' | 'balanced' | 'adaptive'
    maxResults?: number
    includeRelationships?: boolean
    semanticThreshold?: number
  } = {}): Promise<{
    results: any[]
    explanation: string
    confidence: number
    strategy: string
    processingTime: number
  }> {
    const startTime = Date.now()

    const hybridQuery: HybridQuery = {
      naturalLanguage: naturalLanguageQuery,
      structuralConstraints: {
        maxDepth: 5,
        relationshipTypes: ['DEPENDS_ON', 'CALLS', 'DEFINES'],
        nodeTypes: ['File', 'Function', 'Class']
      },
      semanticConstraints: {
        similarity: options.semanticThreshold || 0.7,
        concepts: this.extractConcepts(naturalLanguageQuery)
      },
      fusionStrategy: options.strategy || 'adaptive'
    }

    const result = await this.hybridSearch(hybridQuery)

    return {
      results: result.fusedResults.slice(0, options.maxResults || 10),
      explanation: result.explanation,
      confidence: result.confidence,
      strategy: result.queryStrategy,
      processingTime: Date.now() - startTime
    }
  }

  /**
   * Get intelligent code summary for massive codebases
   */
  async getIntelligentCodeSummary(filePath: string, options: {
    includeContext?: boolean
    abstractionLevel?: 'high' | 'medium' | 'detailed'
    focusAreas?: string[]
  } = {}): Promise<{
    summary: string
    keyComponents: string[]
    dependencies: string[]
    complexity: string
    recommendations: string[]
    semanticInsights: any
  }> {
    // Get basic summary from agentic RAG
    const basicSummary = await this.agenticRAG.getCodeSummary(filePath, options)

    // Enhance with semantic insights
    const semanticInsights = await this.getSemanticInsights(filePath)

    return {
      ...basicSummary,
      semanticInsights
    }
  }

  /**
   * Navigate large codebase intelligently
   */
  async navigateCodebase(intent: string, currentContext: any = {}): Promise<{
    suggestedFiles: string[]
    navigationPath: string[]
    reasoning: string
    nextSteps: string[]
    confidence: number
    semanticClusters: string[]
  }> {
    const navigation = await this.agenticRAG.navigateCodebase(intent, currentContext)

    // Add semantic clustering information
    const semanticClusters = await this.getSemanticClusters(navigation.suggestedFiles)

    return {
      ...navigation,
      semanticClusters
    }
  }

  /**
   * Learn from user interactions to improve understanding
   */
  async learnFromInteraction(query: string, selectedResults: string[], feedback: {
    helpful: boolean
    accuracy: number
    suggestions?: string[]
  }): Promise<void> {
    await this.agenticRAG.learnFromInteraction(query, selectedResults, feedback)

    // Update context engine memory with learned patterns
    this.updateLearningMemory(query, selectedResults, feedback)
  }

  /**
   * Get memory insights for large codebase management
   */
  getCodebaseMemoryInsights(): {
    recentQueries: string[]
    workingSet: string[]
    learnedPatterns: string[]
    conceptualMap: Record<string, string[]>
    complexityHotspots: string[]
    navigationPatterns: string[]
  } {
    const ragInsights = this.agenticRAG.getMemoryInsights()

    return {
      ...ragInsights,
      complexityHotspots: this.getComplexityHotspots(),
      navigationPatterns: this.getNavigationPatterns()
    }
  }

  /**
   * Analyze codebase at scale for architectural insights
   */
  async analyzeCodebaseArchitecture(): Promise<{
    overallPattern: any
    layers: string[]
    dataFlow: string[]
    concerns: string[]
    suggestions: string[]
    scalabilityInsights: any
  }> {
    if (!this.codebaseContext) {
      throw new Error('Codebase not processed')
    }

    // Get architectural analysis from semantic analyzer
    const embeddings = Array.from(this.embeddings?.values() || [])
    const architecture = await this.semanticAnalyzer?.analyzeArchitecture(embeddings)

    // Add scalability insights for large codebases
    const scalabilityInsights = await this.analyzeScalabilityInsights()

    return {
      ...architecture,
      scalabilityInsights
    }
  }

  /**
   * Validate context integrity
   */
  validateContext(): ContextIntegrityReport {
    return this.contextIntegrity.validateContext(this.context, this.decisionGraph)
  }

  /**
   * Query codebase using natural language
   */
  async queryCodebase(query: string, options: any = {}): Promise<any> {
    if (!this.isInitialized || !this.codebaseContext) {
      throw new Error('Codebase not analyzed')
    }

    const session = this.neo4jDriver.session()

    try {
      // Convert natural language query to Cypher
      const cypherQuery = this.buildCypherQuery(query, options)

      // Execute query
      const result = await session.run(cypherQuery, options)

      // Format results
      return this.formatQueryResults(result.records)

    } finally {
      await session.close()
    }
  }

  /**
   * Get file analysis for specific file
   */
  async getFileAnalysis(filePath: string): Promise<FileContext | null> {
    if (!this.codebaseContext) {
      return null
    }

    return this.codebaseContext.files.get(filePath) || null
  }

  /**
   * Get symbol information
   */
  async getSymbolInfo(symbolName: string, filePath?: string): Promise<SymbolContext[]> {
    if (!this.codebaseContext) {
      return []
    }

    const symbols: SymbolContext[] = []

    for (const [key, symbol] of this.codebaseContext.symbols) {
      if (symbol.name === symbolName && (!filePath || symbol.file === filePath)) {
        symbols.push(symbol)
      }
    }

    return symbols
  }

  /**
   * Get dependencies for a file
   */
  async getFileDependencies(filePath: string): Promise<string[]> {
    if (!this.codebaseContext) {
      return []
    }

    return this.codebaseContext.dependencies.get(filePath) || []
  }

  /**
   * Get files that depend on a specific file
   */
  async getFileDependents(filePath: string): Promise<string[]> {
    if (!this.codebaseContext) {
      return []
    }

    const dependents: string[] = []

    for (const [file, deps] of this.codebaseContext.dependencies) {
      if (deps.includes(filePath)) {
        dependents.push(file)
      }
    }

    return dependents
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    if (this.neo4jDriver) {
      await this.neo4jDriver.close()
    }

    if (this.redisClient) {
      await this.redisClient.quit()
    }

    this.emit('cleanup_completed')
  }

  // Helper methods (simplified implementations)
  private getDefaultCapabilities(agentType: AgentType): string[] {
    const capabilities = {
      'planner': ['task_breakdown', 'dependency_analysis'],
      'executor': ['code_generation', 'file_operations'],
      'workflow': ['process_management', 'coordination'],
      'project-planner': ['requirements_analysis', 'tech_stack_selection'],
      'task-breakdown': ['task_analysis', 'estimation'],
      'code-generator': ['code_generation', 'testing']
    }
    return capabilities[agentType] || []
  }

  private getDefaultFilters(agentType: AgentType): string[] {
    const filters = {
      'planner': ['requirements', 'architecture'],
      'executor': ['implementation', 'testing'],
      'workflow': ['coordination', 'status'],
      'project-planner': ['all'],
      'task-breakdown': ['tasks', 'dependencies'],
      'code-generator': ['code', 'patterns']
    }
    return filters[agentType] || ['all']
  }

  private getStepContextRequirements(step: string): string[] {
    return [`${step}_input`, `${step}_validation`]
  }

  private getStepValidationRules(step: string): string[] {
    return [`${step}_required_fields`, `${step}_format_validation`]
  }

  private getStepDependencies(step: string): string[] {
    const node = this.decisionGraph.get(step)
    return node ? node.dependencies : []
  }

  private assessComplexity(): string {
    const features = this.context.features?.length || 0
    if (features < 3) return 'Simple'
    if (features < 8) return 'Medium'
    return 'Complex'
  }

  private filterContextForAgent(context: any, agentScope: AgentContextScope): any {
    // Simplified filtering based on agent capabilities
    return context
  }

  private getSharedStateForAgent(agentType: AgentType): any {
    const sharedState: any = {}

    switch (agentType) {
      case 'project-planner':
        this.sharedState.forEach((value, key) => {
          sharedState[key] = value
        })
        break
      case 'planner':
        sharedState.project_status = this.sharedState.get('project_status')
        sharedState.execution_timeline = this.sharedState.get('execution_timeline')
        break
      default:
        sharedState.system_health = this.sharedState.get('system_health')
    }

    return sharedState
  }

  // Codebase analysis helper methods
  private async getSourceFiles(projectPath: string): Promise<string[]> {
    const fs = await import('fs/promises')
    const path = await import('path')
    const files: string[] = []

    async function scanDirectory(dir: string): Promise<void> {
      try {
        const entries = await fs.readdir(dir, { withFileTypes: true })

        for (const entry of entries) {
          const fullPath = path.join(dir, entry.name)

          if (entry.isDirectory()) {
            // Skip node_modules, .git, etc.
            if (!['node_modules', '.git', 'dist', 'build', '.next'].includes(entry.name)) {
              await scanDirectory(fullPath)
            }
          } else if (entry.isFile()) {
            // Include source files
            const ext = path.extname(entry.name).toLowerCase()
            if (['.ts', '.tsx', '.js', '.jsx', '.py', '.java', '.go', '.rs'].includes(ext)) {
              files.push(fullPath)
            }
          }
        }
      } catch (error) {
        console.warn(`Failed to scan directory ${dir}:`, error.message)
      }
    }

    await scanDirectory(projectPath)
    return files
  }

  private detectLanguage(filePath: string): string {
    const ext = filePath.split('.').pop()?.toLowerCase()
    const languageMap: Record<string, string> = {
      'ts': 'typescript',
      'tsx': 'typescript',
      'js': 'javascript',
      'jsx': 'javascript',
      'py': 'python',
      'java': 'java',
      'go': 'go',
      'rs': 'rust'
    }
    return languageMap[ext || ''] || 'unknown'
  }

  private async parseAST(content: string, language: string): Promise<any> {
    // Simplified AST parsing - in production, use tree-sitter or language-specific parsers
    try {
      if (language === 'javascript' || language === 'typescript') {
        // Basic regex-based parsing for demo
        const functions = content.match(/function\s+(\w+)|(\w+)\s*=\s*\(/g) || []
        const classes = content.match(/class\s+(\w+)/g) || []
        const imports = content.match(/import.*from\s+['"]([^'"]+)['"]/g) || []

        return {
          type: 'Program',
          functions: functions.map(f => ({ name: f.split(/\s+/)[1] || 'anonymous' })),
          classes: classes.map(c => ({ name: c.split(/\s+/)[1] })),
          imports: imports.map(i => i.match(/['"]([^'"]+)['"]/)?.[1] || '')
        }
      }

      return { type: 'Program', body: [] }
    } catch (error) {
      console.warn(`Failed to parse AST for ${language}:`, error.message)
      return { type: 'Program', body: [] }
    }
  }

  private extractSymbols(ast: any, filePath: string): SymbolContext[] {
    const symbols: SymbolContext[] = []

    // Extract functions
    if (ast.functions) {
      ast.functions.forEach((func: any, index: number) => {
        symbols.push({
          name: func.name,
          type: 'function',
          file: filePath,
          line: index + 1, // Simplified line numbering
          scope: 'global',
          references: []
        })
      })
    }

    // Extract classes
    if (ast.classes) {
      ast.classes.forEach((cls: any, index: number) => {
        symbols.push({
          name: cls.name,
          type: 'class',
          file: filePath,
          line: index + 1,
          scope: 'global',
          references: []
        })
      })
    }

    return symbols
  }

  private extractDependencies(ast: any, content: string): string[] {
    const dependencies: string[] = []

    // Extract imports
    if (ast.imports) {
      dependencies.push(...ast.imports.filter((imp: string) => imp && imp.length > 0))
    }

    // Extract require statements
    const requires = content.match(/require\(['"]([^'"]+)['"]\)/g) || []
    requires.forEach(req => {
      const match = req.match(/['"]([^'"]+)['"]/)
      if (match) {
        dependencies.push(match[1])
      }
    })

    return dependencies
  }

  private calculateComplexity(ast: any): number {
    // Simplified complexity calculation
    let complexity = 1

    if (ast.functions) complexity += ast.functions.length * 2
    if (ast.classes) complexity += ast.classes.length * 3
    if (ast.imports) complexity += ast.imports.length

    return complexity
  }

  private async resolveDependency(dep: string, fromFile: string): Promise<string | null> {
    const path = await import('path')
    const fs = await import('fs/promises')

    // Handle relative imports
    if (dep.startsWith('./') || dep.startsWith('../')) {
      const resolved = path.resolve(path.dirname(fromFile), dep)

      // Try different extensions
      const extensions = ['.ts', '.tsx', '.js', '.jsx']
      for (const ext of extensions) {
        const fullPath = resolved + ext
        try {
          await fs.access(fullPath)
          return fullPath
        } catch {
          // File doesn't exist, try next extension
        }
      }
    }

    return null
  }

  private filterRelevantFiles(agentScope: AgentContextScope): Map<string, FileContext> {
    if (!this.codebaseContext) return new Map()

    // Filter based on agent capabilities and context filters
    const relevantFiles = new Map<string, FileContext>()

    for (const [filePath, fileContext] of this.codebaseContext.files) {
      // Include files based on agent type
      if (this.isFileRelevantForAgent(fileContext, agentScope)) {
        relevantFiles.set(filePath, fileContext)
      }
    }

    return relevantFiles
  }

  private filterRelevantSymbols(agentScope: AgentContextScope): Map<string, SymbolContext> {
    if (!this.codebaseContext) return new Map()

    const relevantSymbols = new Map<string, SymbolContext>()

    for (const [symbolKey, symbol] of this.codebaseContext.symbols) {
      if (this.isSymbolRelevantForAgent(symbol, agentScope)) {
        relevantSymbols.set(symbolKey, symbol)
      }
    }

    return relevantSymbols
  }

  private getRelevantDependencies(files: Map<string, FileContext>): Map<string, string[]> {
    if (!this.codebaseContext) return new Map()

    const relevantDeps = new Map<string, string[]>()

    for (const filePath of files.keys()) {
      const deps = this.codebaseContext.dependencies.get(filePath)
      if (deps) {
        relevantDeps.set(filePath, deps)
      }
    }

    return relevantDeps
  }

  private isFileRelevantForAgent(file: FileContext, agentScope: AgentContextScope): boolean {
    // Simple relevance check based on agent type
    switch (agentScope.agentType) {
      case 'code-generator':
        return true // Code generators need access to all files
      case 'planner':
        return file.language === 'typescript' || file.language === 'javascript'
      default:
        return agentScope.contextFilters.includes('all') ||
               agentScope.contextFilters.includes(file.language)
    }
  }

  private isSymbolRelevantForAgent(symbol: SymbolContext, agentScope: AgentContextScope): boolean {
    // Filter symbols based on agent capabilities
    if (agentScope.requiredCapabilities.includes('code_generation')) {
      return true // Code generation agents need all symbols
    }

    if (agentScope.requiredCapabilities.includes('task_breakdown')) {
      return symbol.type === 'function' || symbol.type === 'class'
    }

    return false
  }

  // Additional helper methods for context management
  private enrichContextData(step: string, data: any, contextNode: ContextNode): any {
    return {
      ...data,
      _enriched: true,
      _step: step,
      _timestamp: new Date().toISOString(),
      _dependencies: contextNode.dependencies
    }
  }

  private gatherUpstreamContext(dependencies: string[]): any {
    const upstreamContext: any = {}

    dependencies.forEach(dep => {
      const stepData = this.memoryStore.get(`step_${dep}`)
      if (stepData) {
        upstreamContext[dep] = stepData.data
      }
    })

    return upstreamContext
  }

  private updateCrossReferences(step: string, data: any): string[] {
    const crossRefs: string[] = []

    // Find references to other steps in the data
    const dataStr = JSON.stringify(data)
    for (const [otherStep] of this.decisionGraph) {
      if (otherStep !== step && dataStr.includes(otherStep)) {
        crossRefs.push(otherStep)
      }
    }

    return crossRefs
  }

  private findCrossReferences(step: string, data: any): string[] {
    return this.updateCrossReferences(step, data)
  }

  private getBestPractices(step: string): string[] {
    const practices: Record<string, string[]> = {
      'analyze': ['Use clear requirements', 'Consider edge cases'],
      'techstack': ['Choose proven technologies', 'Consider team expertise'],
      'prd': ['Define clear acceptance criteria', 'Include non-functional requirements'],
      'tasks': ['Break down into manageable chunks', 'Define clear dependencies']
    }

    return practices[step] || []
  }

  private getTemplates(step: string): Record<string, any> {
    const templates: Record<string, any> = {
      'analyze': { structure: 'problem-solution-requirements' },
      'techstack': { categories: ['frontend', 'backend', 'database', 'deployment'] },
      'prd': { sections: ['overview', 'features', 'requirements', 'acceptance'] },
      'tasks': { format: 'user-story-tasks-acceptance' }
    }

    return templates[step] || {}
  }

  private generateContextualHints(step: string, context: ProjectContext, dependencies: any): any[] {
    const hints: any[] = []

    // Generate hints based on current context and step
    if (step === 'techstack' && context.features) {
      const hasUIFeatures = context.features.some(f =>
        f.toLowerCase().includes('dashboard') || f.toLowerCase().includes('ui')
      )

      if (hasUIFeatures) {
        hints.push({
          type: 'suggestion',
          message: 'Consider modern UI frameworks like React or Vue for dashboard features',
          relevance: 0.8
        })
      }
    }

    return hints
  }

  private buildCypherQuery(query: string, options: any): string {
    // Simplified Cypher query builder
    // In production, use proper query parsing and building

    if (query.toLowerCase().includes('function')) {
      return `
        MATCH (s:Symbol {type: 'function'})
        MATCH (f:File)-[:DEFINES]->(s)
        RETURN s.name, s.file, f.language
        LIMIT ${options.limit || 10}
      `
    }

    if (query.toLowerCase().includes('class')) {
      return `
        MATCH (s:Symbol {type: 'class'})
        MATCH (f:File)-[:DEFINES]->(s)
        RETURN s.name, s.file, f.language
        LIMIT ${options.limit || 10}
      `
    }

    if (query.toLowerCase().includes('dependencies')) {
      return `
        MATCH (f1:File)-[:DEPENDS_ON]->(f2:File)
        RETURN f1.path, f2.path
        LIMIT ${options.limit || 20}
      `
    }

    // Default query
    return `
      MATCH (n)
      RETURN n
      LIMIT ${options.limit || 5}
    `
  }

  private formatQueryResults(records: any[]): any {
    return {
      results: records.map(record => record.toObject()),
      count: records.length,
      timestamp: new Date().toISOString()
    }
  }

  // Agentic RAG initialization and helper methods
  private async initializeAgenticRAG(): Promise<void> {
    try {
      await this.agenticRAG.initialize()
      this.emit('agentic_rag_initialized')
    } catch (error) {
      console.error('Failed to initialize agentic RAG:', error)
      // Continue without RAG if initialization fails
    }
  }

  private async getSemanticInsights(filePath: string): Promise<any> {
    try {
      // Get semantic insights from the agentic RAG engine
      const insights = {
        semanticTags: [],
        relatedConcepts: [],
        architecturalRole: 'unknown',
        complexityAnalysis: {},
        usagePatterns: []
      }

      // This would be enhanced with actual semantic analysis
      return insights
    } catch (error) {
      console.warn('Failed to get semantic insights:', error.message)
      return {}
    }
  }

  private async getSemanticClusters(files: string[]): Promise<string[]> {
    try {
      // Group files by semantic similarity
      const clusters: string[] = []

      // This would use the agentic RAG engine to find semantic clusters
      files.forEach(file => {
        if (file.includes('component')) clusters.push('UI Components')
        else if (file.includes('api')) clusters.push('API Layer')
        else if (file.includes('util')) clusters.push('Utilities')
        else clusters.push('Core Logic')
      })

      return [...new Set(clusters)]
    } catch (error) {
      console.warn('Failed to get semantic clusters:', error.message)
      return []
    }
  }

  private updateLearningMemory(query: string, selectedResults: string[], feedback: any): void {
    // Store learning data in context engine memory
    const learningEntry = {
      query,
      selectedResults,
      feedback,
      timestamp: new Date().toISOString(),
      contextSnapshot: this.getRelevantContext('all')
    }

    this.memoryStore.set(`learning_${Date.now()}`, learningEntry)

    // Update shared state with learning insights
    const learningInsights = this.sharedState.get('learning_insights') || []
    learningInsights.push({
      type: 'user_feedback',
      helpful: feedback.helpful,
      accuracy: feedback.accuracy,
      timestamp: new Date().toISOString()
    })

    // Keep only recent insights
    if (learningInsights.length > 100) {
      learningInsights.splice(0, learningInsights.length - 50)
    }

    this.sharedState.set('learning_insights', learningInsights)
  }

  private getComplexityHotspots(): string[] {
    if (!this.codebaseContext) return []

    const hotspots: string[] = []

    for (const [filePath, fileContext] of this.codebaseContext.files) {
      if (fileContext.complexity > 15) {
        hotspots.push(filePath)
      }
    }

    return hotspots.sort((a, b) => {
      const complexityA = this.codebaseContext!.files.get(a)?.complexity || 0
      const complexityB = this.codebaseContext!.files.get(b)?.complexity || 0
      return complexityB - complexityA
    }).slice(0, 10)
  }

  private getNavigationPatterns(): string[] {
    const patterns: string[] = []

    // Analyze recent navigation patterns from memory
    const recentQueries = Array.from(this.memoryStore.keys())
      .filter(key => key.startsWith('learning_'))
      .slice(-20)

    recentQueries.forEach(key => {
      const entry = this.memoryStore.get(key)
      if (entry && entry.query) {
        if (entry.query.includes('find')) patterns.push('search-oriented')
        else if (entry.query.includes('understand')) patterns.push('exploration-oriented')
        else if (entry.query.includes('debug')) patterns.push('problem-solving')
      }
    })

    return [...new Set(patterns)]
  }

  private async analyzeScalabilityInsights(): Promise<any> {
    if (!this.codebaseContext) return {}

    const insights = {
      fileCount: this.codebaseContext.files.size,
      averageComplexity: 0,
      dependencyDepth: 0,
      modularityScore: 0,
      recommendations: [] as string[]
    }

    // Calculate average complexity
    const complexities = Array.from(this.codebaseContext.files.values()).map(f => f.complexity)
    insights.averageComplexity = complexities.reduce((sum, c) => sum + c, 0) / complexities.length

    // Analyze dependency depth
    insights.dependencyDepth = this.calculateMaxDependencyDepth()

    // Calculate modularity score
    insights.modularityScore = this.calculateModularityScore()

    // Generate recommendations for large codebases
    if (insights.fileCount > 1000) {
      insights.recommendations.push('Consider implementing micro-frontend architecture')
      insights.recommendations.push('Implement lazy loading for better performance')
    }

    if (insights.averageComplexity > 10) {
      insights.recommendations.push('Focus on refactoring high-complexity modules')
      insights.recommendations.push('Implement automated code quality gates')
    }

    if (insights.dependencyDepth > 8) {
      insights.recommendations.push('Review and flatten dependency hierarchies')
      insights.recommendations.push('Consider dependency injection patterns')
    }

    return insights
  }

  private calculateMaxDependencyDepth(): number {
    if (!this.codebaseContext) return 0

    let maxDepth = 0

    // Simple depth calculation - in production this would be more sophisticated
    for (const [filePath, dependencies] of this.codebaseContext.dependencies) {
      const depth = this.calculateDepthRecursive(filePath, new Set(), 0)
      maxDepth = Math.max(maxDepth, depth)
    }

    return maxDepth
  }

  private calculateDepthRecursive(filePath: string, visited: Set<string>, currentDepth: number): number {
    if (visited.has(filePath) || currentDepth > 20) return currentDepth

    visited.add(filePath)
    const dependencies = this.codebaseContext?.dependencies.get(filePath) || []

    let maxChildDepth = currentDepth
    for (const dep of dependencies) {
      const childDepth = this.calculateDepthRecursive(dep, new Set(visited), currentDepth + 1)
      maxChildDepth = Math.max(maxChildDepth, childDepth)
    }

    return maxChildDepth
  }

  private calculateModularityScore(): number {
    if (!this.codebaseContext) return 0

    // Simple modularity calculation based on file organization and dependencies
    const totalFiles = this.codebaseContext.files.size
    const totalDependencies = Array.from(this.codebaseContext.dependencies.values())
      .reduce((sum, deps) => sum + deps.length, 0)

    // Lower dependency ratio indicates better modularity
    const dependencyRatio = totalDependencies / totalFiles
    const modularityScore = Math.max(0, 1 - (dependencyRatio / 10))

    return Math.round(modularityScore * 100) / 100
  }

  private extractConcepts(query: string): string[] {
    const concepts: string[] = []
    const lowerQuery = query.toLowerCase()

    // Extract technical concepts
    if (lowerQuery.includes('auth') || lowerQuery.includes('login')) {
      concepts.push('authentication', 'security', 'user-management')
    }
    if (lowerQuery.includes('payment') || lowerQuery.includes('billing')) {
      concepts.push('payment-processing', 'financial', 'transactions')
    }
    if (lowerQuery.includes('api') || lowerQuery.includes('endpoint')) {
      concepts.push('api', 'http', 'rest', 'graphql')
    }
    if (lowerQuery.includes('database') || lowerQuery.includes('data')) {
      concepts.push('data-persistence', 'database', 'storage')
    }

    return concepts
  }
}
