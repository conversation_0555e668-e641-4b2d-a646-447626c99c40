export interface Task {
  id: string
  title: string
  completed?: boolean
  url?: string
}

export const demoTasks: Task[] = [
  { id: "1", title: "Implement new feature", url: "#" },
  { id: "2", title: "Fix critical bug", url: "#" },
  { id: "3", title: "Update documentation", url: "#" },
  { id: "4", title: "Prepare for team meeting", url: "#" },
  { id: "5", title: "Review pull requests", url: "#" },
]
