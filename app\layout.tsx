import type React from "react"
import "./globals.css"
import { <PERSON><PERSON><PERSON> } from "next/font/google"

const geist = Geist({ subsets: ["latin"] })

export const metadata = {
  title: "AG3NT",
  description: "AI-powered project planning and context generation",
  generator: 'v0.dev',
  icons: {
    icon: '/AG3NT.png',
    shortcut: '/AG3NT.png',
    apple: '/AG3NT.png',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <head>
        <link rel="icon" href="/AG3NT.png" type="image/png" />
        <link rel="shortcut icon" href="/AG3NT.png" type="image/png" />
        <link rel="apple-touch-icon" href="/AG3NT.png" />
      </head>
      <body className={geist.className}>{children}</body>
    </html>
  )
}
