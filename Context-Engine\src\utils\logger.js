import winston from 'winston';
import { config } from './config.js';

/**
 * Custom log format for structured logging
 */
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, service, ...meta }) => {
    return JSON.stringify({
      timestamp,
      level,
      service: service || 'context-engine',
      message,
      ...meta
    });
  })
);

/**
 * Console format for development
 */
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'HH:mm:ss'
  }),
  winston.format.printf(({ timestamp, level, message, service, ...meta }) => {
    const metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
    return `${timestamp} [${service || 'context-engine'}] ${level}: ${message} ${metaStr}`;
  })
);

/**
 * Create Winston logger instance
 */
const logger = winston.createLogger({
  level: config.logLevel,
  format: logFormat,
  defaultMeta: {
    service: 'context-engine'
  },
  transports: [
    // File transport for errors
    new winston.transports.File({
      filename: 'logs/error.log',
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5
    }),
    
    // File transport for all logs
    new winston.transports.File({
      filename: 'logs/combined.log',
      maxsize: 5242880, // 5MB
      maxFiles: 5
    })
  ]
});

/**
 * Add console transport for non-production environments
 */
if (config.env !== 'production') {
  logger.add(new winston.transports.Console({
    format: consoleFormat
  }));
}

/**
 * Create child logger with additional context
 */
export const createLogger = (service, context = {}) => {
  return logger.child({
    service,
    ...context
  });
};

/**
 * Log performance metrics
 */
export const logPerformance = (operation, duration, metadata = {}) => {
  logger.info('Performance metric', {
    operation,
    duration,
    unit: 'ms',
    ...metadata
  });
};

/**
 * Log API requests
 */
export const logRequest = (req, res, duration) => {
  logger.info('API Request', {
    method: req.method,
    url: req.url,
    statusCode: res.statusCode,
    duration,
    userAgent: req.get('User-Agent'),
    ip: req.ip
  });
};

/**
 * Log errors with context
 */
export const logError = (error, context = {}) => {
  logger.error('Error occurred', {
    error: {
      message: error.message,
      stack: error.stack,
      name: error.name
    },
    ...context
  });
};

/**
 * Log ingestion events
 */
export const logIngestion = (event, file, metadata = {}) => {
  logger.info('Ingestion event', {
    event,
    file: {
      path: file.path,
      size: file.size,
      language: file.language
    },
    ...metadata
  });
};

/**
 * Log graph operations
 */
export const logGraph = (operation, nodeCount, relationshipCount, duration) => {
  logger.info('Graph operation', {
    operation,
    nodeCount,
    relationshipCount,
    duration,
    unit: 'ms'
  });
};

/**
 * Log retrieval operations
 */
export const logRetrieval = (query, resultCount, sources, duration) => {
  logger.info('Context retrieval', {
    query: query.substring(0, 100), // Truncate long queries
    resultCount,
    sources,
    duration,
    unit: 'ms'
  });
};

export default logger;
