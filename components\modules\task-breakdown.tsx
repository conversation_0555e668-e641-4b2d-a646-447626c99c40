"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { CheckSquare, Clock, AlertCircle } from "lucide-react"
import type { ModuleProps } from "@/types/planning"

interface Task {
  id: string
  title: string
  description: string
  category: "setup" | "frontend" | "backend" | "database" | "testing" | "deployment"
  priority: "high" | "medium" | "low"
  estimatedHours: number
  dependencies?: string[]
  skills: string[]
}

export function TaskBreakdown({ context, onComplete }: ModuleProps) {
  const [tasks, setTasks] = useState<Task[]>([])
  const [isGenerating, setIsGenerating] = useState(true)

  useEffect(() => {
    generateTasks()
  }, [])

  const generateTasks = async () => {
    setIsGenerating(true)

    // Simulate AI generation
    await new Promise((resolve) => setTimeout(resolve, 2500))

    const generatedTasks = createTasks()
    setTasks(generatedTasks)
    setIsGenerating(false)
  }

  const createTasks = (): Task[] => {
    const { techStack, clarifications, features } = context || {}
    const needsAuth = clarifications?.authentication?.includes("Yes")
    const isReact = techStack?.Frontend === "React" || techStack?.Frontend === "Next.js"
    const isNode = techStack?.Backend === "Node.js"

    const tasks: Task[] = []

    // Setup tasks
    tasks.push({
      id: "setup_project",
      title: "Project Setup and Configuration",
      description: "Initialize project structure, install dependencies, and configure development environment",
      category: "setup",
      priority: "high",
      estimatedHours: 4,
      skills: ["Git", "Node.js", techStack?.Frontend || "Frontend Framework"],
    })

    tasks.push({
      id: "setup_database",
      title: "Database Setup",
      description: `Configure ${techStack?.Database || "database"} and create initial schema`,
      category: "database",
      priority: "high",
      estimatedHours: 3,
      dependencies: ["setup_project"],
      skills: [techStack?.Database || "Database", "SQL"],
    })

    // Frontend tasks
    if (isReact) {
      tasks.push({
        id: "create_components",
        title: "Create Core Components",
        description: "Build reusable UI components including layout, navigation, and common elements",
        category: "frontend",
        priority: "high",
        estimatedHours: 8,
        dependencies: ["setup_project"],
        skills: ["React", "CSS", "JavaScript"],
      })

      tasks.push({
        id: "implement_routing",
        title: "Implement Application Routing",
        description: "Set up client-side routing and navigation between pages",
        category: "frontend",
        priority: "medium",
        estimatedHours: 3,
        dependencies: ["create_components"],
        skills: ["React Router", "React"],
      })
    }

    // Backend tasks
    if (isNode) {
      tasks.push({
        id: "create_api_endpoints",
        title: "Create API Endpoints",
        description: "Develop RESTful API endpoints for core functionality",
        category: "backend",
        priority: "high",
        estimatedHours: 12,
        dependencies: ["setup_database"],
        skills: ["Node.js", "Express", "API Design"],
      })

      tasks.push({
        id: "implement_middleware",
        title: "Implement Middleware",
        description: "Add logging, error handling, and security middleware",
        category: "backend",
        priority: "medium",
        estimatedHours: 4,
        dependencies: ["create_api_endpoints"],
        skills: ["Node.js", "Express", "Security"],
      })
    }

    // Authentication tasks
    if (needsAuth) {
      tasks.push({
        id: "implement_auth",
        title: "Implement Authentication System",
        description: "Build user registration, login, and session management",
        category: "backend",
        priority: "high",
        estimatedHours: 10,
        dependencies: ["setup_database"],
        skills: [techStack?.Authentication || "Authentication", "Security", "JWT"],
      })

      tasks.push({
        id: "auth_frontend",
        title: "Authentication UI Components",
        description: "Create login, registration, and profile management interfaces",
        category: "frontend",
        priority: "high",
        estimatedHours: 6,
        dependencies: ["implement_auth", "create_components"],
        skills: ["React", "Forms", "Validation"],
      })
    }

    // Feature-specific tasks
    features?.forEach((feature, index) => {
      tasks.push({
        id: `feature_${index}`,
        title: `Implement ${feature}`,
        description: `Develop the ${feature.toLowerCase()} functionality including UI and backend logic`,
        category: feature.toLowerCase().includes("ui") ? "frontend" : "backend",
        priority: index === 0 ? "high" : "medium",
        estimatedHours: 8,
        dependencies: ["create_api_endpoints", "create_components"],
        skills: ["Full Stack Development", techStack?.Frontend || "Frontend", techStack?.Backend || "Backend"],
      })
    })

    // Testing tasks
    tasks.push({
      id: "unit_testing",
      title: "Unit Testing",
      description: "Write unit tests for core functionality and components",
      category: "testing",
      priority: "medium",
      estimatedHours: 12,
      dependencies: ["create_api_endpoints", "create_components"],
      skills: ["Testing", "Jest", "React Testing Library"],
    })

    tasks.push({
      id: "integration_testing",
      title: "Integration Testing",
      description: "Test API endpoints and database interactions",
      category: "testing",
      priority: "medium",
      estimatedHours: 8,
      dependencies: ["unit_testing"],
      skills: ["Integration Testing", "API Testing"],
    })

    // Deployment tasks
    tasks.push({
      id: "deployment_setup",
      title: "Deployment Configuration",
      description: `Configure deployment pipeline for ${techStack?.Hosting || "hosting platform"}`,
      category: "deployment",
      priority: "medium",
      estimatedHours: 6,
      dependencies: ["integration_testing"],
      skills: ["DevOps", techStack?.Hosting || "Cloud Platform", "CI/CD"],
    })

    tasks.push({
      id: "production_deployment",
      title: "Production Deployment",
      description: "Deploy application to production environment and configure monitoring",
      category: "deployment",
      priority: "low",
      estimatedHours: 4,
      dependencies: ["deployment_setup"],
      skills: ["DevOps", "Monitoring", "Production Management"],
    })

    return tasks
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "setup":
        return "⚙️"
      case "frontend":
        return "🎨"
      case "backend":
        return "⚡"
      case "database":
        return "🗄️"
      case "testing":
        return "🧪"
      case "deployment":
        return "🚀"
      default:
        return "📋"
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "border-red-400 text-red-300"
      case "medium":
        return "border-yellow-400 text-yellow-300"
      case "low":
        return "border-green-400 text-green-300"
      default:
        return "border-gray-400 text-gray-300"
    }
  }

  const groupTasksByCategory = () => {
    const grouped: Record<string, Task[]> = {}
    tasks.forEach((task) => {
      if (!grouped[task.category]) {
        grouped[task.category] = []
      }
      grouped[task.category].push(task)
    })
    return grouped
  }

  const getTotalEstimate = () => {
    return tasks.reduce((total, task) => total + task.estimatedHours, 0)
  }

  const handleContinue = () => {
    onComplete(tasks)
  }

  const groupedTasks = groupTasksByCategory()

  return (
    <Card className="border-slate-700" style={{backgroundColor: '#818181'}}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <CheckSquare className="w-5 h-5 text-purple-400" />
            <CardTitle className="text-white">Task Breakdown</CardTitle>
          </div>
          {!isGenerating && (
            <div className="flex items-center gap-4 text-sm">
              <div className="flex items-center gap-1">
                <CheckSquare className="w-4 h-4 text-purple-400" />
                <span className="text-gray-300">{tasks.length} tasks</span>
              </div>
              <div className="flex items-center gap-1">
                <Clock className="w-4 h-4 text-purple-400" />
                <span className="text-gray-300">{getTotalEstimate()}h estimated</span>
              </div>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {isGenerating ? (
          <div className="flex items-center justify-center py-12">
            <div className="flex items-center gap-3">
              <CheckSquare className="w-5 h-5 text-purple-400 animate-pulse" />
              <span className="text-gray-300">Breaking down project into actionable tasks...</span>
            </div>
          </div>
        ) : (
          <>
            <Tabs defaultValue="all" className="w-full">
              <TabsList className="grid w-full grid-cols-4 lg:grid-cols-7 bg-slate-700">
                <TabsTrigger value="all" className="text-xs">
                  All
                </TabsTrigger>
                {Object.keys(groupedTasks).map((category) => (
                  <TabsTrigger key={category} value={category} className="text-xs capitalize">
                    {getCategoryIcon(category)} {category}
                  </TabsTrigger>
                ))}
              </TabsList>

              <TabsContent value="all" className="mt-4">
                <div className="space-y-4">
                  {Object.entries(groupedTasks).map(([category, categoryTasks]) => (
                    <div key={category}>
                      <h4 className="text-white font-semibold mb-3 flex items-center gap-2">
                        <span>{getCategoryIcon(category)}</span>
                        <span className="capitalize">{category}</span>
                        <Badge variant="secondary" className="bg-slate-600 text-xs">
                          {categoryTasks.length} tasks
                        </Badge>
                      </h4>
                      <div className="space-y-2">
                        {categoryTasks.map((task) => (
                          <div key={task.id} className="p-3 bg-slate-700/50 rounded-lg">
                            <div className="flex items-start justify-between mb-2">
                              <h5 className="text-white font-medium">{task.title}</h5>
                              <div className="flex items-center gap-2">
                                <Badge variant="outline" className={`text-xs ${getPriorityColor(task.priority)}`}>
                                  {task.priority}
                                </Badge>
                                <div className="flex items-center gap-1 text-xs text-gray-400">
                                  <Clock className="w-3 h-3" />
                                  {task.estimatedHours}h
                                </div>
                              </div>
                            </div>
                            <p className="text-gray-300 text-sm mb-3">{task.description}</p>
                            <div className="flex flex-wrap gap-1 mb-2">
                              {task.skills.map((skill) => (
                                <Badge
                                  key={skill}
                                  variant="secondary"
                                  className="text-xs bg-purple-600/20 text-purple-300"
                                >
                                  {skill}
                                </Badge>
                              ))}
                            </div>
                            {task.dependencies && task.dependencies.length > 0 && (
                              <div className="flex items-center gap-2 text-xs text-gray-400">
                                <AlertCircle className="w-3 h-3" />
                                <span>Depends on: {task.dependencies.length} task(s)</span>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </TabsContent>

              {Object.entries(groupedTasks).map(([category, categoryTasks]) => (
                <TabsContent key={category} value={category} className="mt-4">
                  <div className="space-y-3">
                    {categoryTasks.map((task) => (
                      <div key={task.id} className="p-4 bg-slate-700/50 rounded-lg">
                        <div className="flex items-start justify-between mb-2">
                          <h5 className="text-white font-medium">{task.title}</h5>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className={`text-xs ${getPriorityColor(task.priority)}`}>
                              {task.priority}
                            </Badge>
                            <div className="flex items-center gap-1 text-xs text-gray-400">
                              <Clock className="w-3 h-3" />
                              {task.estimatedHours}h
                            </div>
                          </div>
                        </div>
                        <p className="text-gray-300 text-sm mb-3">{task.description}</p>
                        <div className="flex flex-wrap gap-1 mb-2">
                          {task.skills.map((skill) => (
                            <Badge key={skill} variant="secondary" className="text-xs bg-purple-600/20 text-purple-300">
                              {skill}
                            </Badge>
                          ))}
                        </div>
                        {task.dependencies && task.dependencies.length > 0 && (
                          <div className="flex items-center gap-2 text-xs text-gray-400">
                            <AlertCircle className="w-3 h-3" />
                            <span>Depends on: {task.dependencies.length} task(s)</span>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </TabsContent>
              ))}
            </Tabs>

            <div className="mt-6 p-4 bg-slate-700/30 rounded-lg">
              <h4 className="text-white font-semibold mb-2">Project Summary</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-gray-400">Total Tasks:</span>
                  <div className="text-white font-medium">{tasks.length}</div>
                </div>
                <div>
                  <span className="text-gray-400">Estimated Hours:</span>
                  <div className="text-white font-medium">{getTotalEstimate()}h</div>
                </div>
                <div>
                  <span className="text-gray-400">High Priority:</span>
                  <div className="text-white font-medium">{tasks.filter((t) => t.priority === "high").length}</div>
                </div>
                <div>
                  <span className="text-gray-400">Categories:</span>
                  <div className="text-white font-medium">{Object.keys(groupedTasks).length}</div>
                </div>
              </div>
            </div>

            <Button onClick={handleContinue} className="w-full mt-6 bg-purple-600 hover:bg-purple-700">
              Complete Planning Process
            </Button>
          </>
        )}
      </CardContent>
    </Card>
  )
}
