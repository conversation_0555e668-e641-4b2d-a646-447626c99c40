{"mcpServers": {"Context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "timeout": 30000, "description": "Real-time documentation access for thousands of libraries", "capabilities": ["documentation", "code_examples", "library_search"]}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "timeout": 30000, "description": "Advanced reasoning through sequential thought processes", "capabilities": ["reasoning", "problem_solving", "thought_branching"]}}, "mcp_servers": {"tech-stack-server": {"name": "tech-stack-server", "version": "1.0.0", "description": "Real-time technology stack information and compatibility checking", "endpoints": {"compatibility_api": "https://api.techstack.dev/compatibility", "versions_api": "https://api.techstack.dev/versions", "security_api": "https://api.techstack.dev/security"}, "cache_ttl": 300, "tools": [{"name": "get_tech_compatibility", "description": "Check compatibility between frontend, backend, and database technologies", "external_api": "compatibility_api", "rate_limit": "100/hour"}, {"name": "get_latest_versions", "description": "Get latest stable versions of specified technologies", "external_api": "versions_api", "rate_limit": "200/hour"}, {"name": "check_security_advisories", "description": "Check for security vulnerabilities in specified technologies", "external_api": "security_api", "rate_limit": "50/hour"}]}, "best-practices-server": {"name": "best-practices-server", "version": "1.0.0", "description": "Industry standards, best practices, and compliance requirements", "endpoints": {"standards_api": "https://api.standards.dev/industry", "compliance_api": "https://api.compliance.dev/requirements", "patterns_api": "https://api.patterns.dev/recommendations"}, "cache_ttl": 3600, "tools": [{"name": "get_industry_standards", "description": "Get current industry standards for specific domains", "external_api": "standards_api", "rate_limit": "100/hour"}, {"name": "validate_architecture", "description": "Validate architecture against current best practices", "external_api": "patterns_api", "rate_limit": "50/hour"}, {"name": "get_compliance_requirements", "description": "Get compliance requirements for specific domains and regions", "external_api": "compliance_api", "rate_limit": "100/hour"}]}, "project-context-server": {"name": "project-context-server", "version": "1.0.0", "description": "Project-specific context including team preferences and organizational standards", "endpoints": {"team_api": "https://api.internal.company/teams", "org_api": "https://api.internal.company/standards", "codebase_api": "https://api.internal.company/codebase"}, "cache_ttl": 1800, "authentication": {"type": "bearer_token", "token_env": "INTERNAL_API_TOKEN"}, "tools": [{"name": "analyze_existing_codebase", "description": "Analyze existing codebase for patterns and conventions", "external_api": "codebase_api", "rate_limit": "20/hour"}, {"name": "get_team_preferences", "description": "Get team-specific coding preferences and standards", "external_api": "team_api", "rate_limit": "100/hour"}, {"name": "check_organizational_standards", "description": "Check organizational coding and architecture standards", "external_api": "org_api", "rate_limit": "100/hour"}]}, "documentation-server": {"name": "documentation-server", "version": "1.0.0", "description": "Access to real-time documentation and API references", "endpoints": {"docs_api": "https://api.docs.dev/search", "examples_api": "https://api.examples.dev/patterns", "tutorials_api": "https://api.tutorials.dev/guides"}, "cache_ttl": 7200, "tools": [{"name": "search_documentation", "description": "Search for relevant documentation and guides", "external_api": "docs_api", "rate_limit": "200/hour"}, {"name": "get_code_examples", "description": "Get code examples for specific patterns or technologies", "external_api": "examples_api", "rate_limit": "100/hour"}, {"name": "find_tutorials", "description": "Find tutorials and learning resources", "external_api": "tutorials_api", "rate_limit": "100/hour"}]}, "validation-server": {"name": "validation-server", "version": "1.0.0", "description": "Real-time validation services for architecture and code quality", "endpoints": {"architecture_api": "https://api.validation.dev/architecture", "security_api": "https://api.validation.dev/security", "performance_api": "https://api.validation.dev/performance"}, "cache_ttl": 600, "tools": [{"name": "validate_architecture_design", "description": "Validate architecture design against best practices", "external_api": "architecture_api", "rate_limit": "50/hour"}, {"name": "security_assessment", "description": "Perform security assessment of the planned architecture", "external_api": "security_api", "rate_limit": "30/hour"}, {"name": "performance_analysis", "description": "Analyze performance implications of technology choices", "external_api": "performance_api", "rate_limit": "50/hour"}]}}, "global_settings": {"default_cache_ttl": 1800, "max_concurrent_requests": 10, "retry_attempts": 3, "retry_delay": 1000, "timeout": 30000, "enable_caching": true, "enable_rate_limiting": true, "log_level": "info"}, "environment_variables": {"required": ["OPENROUTER_API_KEY"], "optional": ["TEAM_ID", "ORGANIZATION_ID", "INTERNAL_API_TOKEN", "TECH_STACK_API_KEY", "COMPLIANCE_API_KEY"]}, "fallback_behavior": {"on_api_failure": "use_cached_data", "on_rate_limit": "queue_request", "on_timeout": "return_partial_results", "cache_stale_threshold": 86400}, "monitoring": {"enable_metrics": true, "metrics_endpoint": "/metrics", "health_check_interval": 60, "alert_on_failure_rate": 0.1}}