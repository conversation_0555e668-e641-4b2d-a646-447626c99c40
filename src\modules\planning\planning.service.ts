/**
 * Planning Service - Clean business logic layer
 * Implements retry, circuit-breaker, and error handling
 */

import {
  PlanningState,
  PlanningStep,
  PlanningConfig,
  PlanningEvent,
  StartPlanningRequest,
  PlanningStepRequest,
  PlanningResponse
} from './planning.types'
import { PlanningGraph } from '@/lib/planning-graph'
import { EventEmitter } from 'events'
import { sendPlanningEvent } from '@/src/lib/inngest/client'
import { nanoid } from 'nanoid'

export class PlanningService extends EventEmitter {
  private planningGraph: PlanningGraph
  private config: PlanningConfig
  private retryCount = new Map<string, number>()

  constructor(config: PlanningConfig) {
    super()
    this.config = config
    this.planningGraph = new PlanningGraph()
  }

  /**
   * Start a new planning session with Inngest workflow
   */
  async startPlanning(request: StartPlanningRequest): Promise<PlanningResponse> {
    const sessionId = this.generateSessionId()

    try {
      // Send event to start Inngest workflow
      await sendPlanningEvent("planning/session.started", {
        sessionId,
        prompt: request.prompt,
        userId: request.userAnswers?.userId,
        isInteractive: request.isInteractive,
        timestamp: new Date().toISOString()
      })

      this.emit('planning_started', {
        type: 'planning_started',
        payload: {
          prompt: request.prompt,
          timestamp: new Date().toISOString()
        }
      } as PlanningEvent)

      // For interactive mode, return immediately with session ID
      if (request.isInteractive) {
        return {
          success: true,
          results: { sessionId },
          completed: false,
          needsInput: false
        }
      }

      // For non-interactive mode, execute synchronously (fallback)
      const initialState: PlanningState = {
        prompt: request.prompt,
        isInteractive: request.isInteractive,
        userAnswers: request.userAnswers,
        results: {},
        completed: false,
        needsInput: false
      }

      const result = await this.executeWithRetry(
        () => this.planningGraph.execute(initialState),
        sessionId
      )

      this.emit('planning_completed', {
        type: 'planning_completed',
        payload: {
          results: result.results,
          timestamp: new Date().toISOString()
        }
      } as PlanningEvent)

      return {
        success: true,
        results: result.results,
        completed: result.completed,
        needsInput: result.needsInput,
        question: result.question
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'

      this.emit('planning_failed', {
        type: 'planning_failed',
        payload: {
          error: errorMessage,
          timestamp: new Date().toISOString()
        }
      } as PlanningEvent)

      return {
        success: false,
        error: errorMessage,
        completed: false,
        needsInput: false
      }
    }
  }

  /**
   * Start planning session with Inngest (async workflow)
   */
  async startPlanningAsync(request: StartPlanningRequest): Promise<{ sessionId: string }> {
    const sessionId = this.generateSessionId()

    await sendPlanningEvent("planning/session.started", {
      sessionId,
      prompt: request.prompt,
      userId: request.userAnswers?.userId,
      isInteractive: request.isInteractive,
      timestamp: new Date().toISOString()
    })

    return { sessionId }
  }

  /**
   * Execute a single planning step
   */
  async executeStep(request: PlanningStepRequest): Promise<PlanningResponse> {
    const stepId = `${request.step}-${Date.now()}`
    
    try {
      const state: PlanningState = {
        prompt: request.context.prompt || '',
        isInteractive: request.context.isInteractive || false,
        userAnswers: request.context.userAnswers || {},
        currentStep: request.step,
        results: request.context.results || {},
        completed: false,
        needsInput: false
      }

      // Add answer if provided
      if (request.answer && request.context.question) {
        state.userAnswers[request.context.question.id] = request.answer
      }

      const result = await this.executeWithRetry(
        () => this.planningGraph.executeStepWithContext(request.step, state),
        stepId
      )

      this.emit('step_completed', {
        type: 'step_completed',
        payload: {
          step: request.step,
          results: result.results,
          timestamp: new Date().toISOString()
        }
      } as PlanningEvent)

      return {
        success: true,
        results: result.results,
        completed: result.completed,
        needsInput: result.needsInput,
        question: result.question
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      
      this.emit('planning_failed', {
        type: 'planning_failed',
        payload: {
          error: errorMessage,
          step: request.step,
          timestamp: new Date().toISOString()
        }
      } as PlanningEvent)

      return {
        success: false,
        error: errorMessage,
        completed: false,
        needsInput: false
      }
    }
  }

  /**
   * Execute with retry logic and circuit breaker
   */
  private async executeWithRetry<T>(
    operation: () => Promise<T>,
    operationId: string
  ): Promise<T> {
    const maxRetries = this.config.maxRetries
    let lastError: Error

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        // Add timeout
        const result = await Promise.race([
          operation(),
          this.createTimeout(this.config.timeoutMs)
        ])

        // Reset retry count on success
        this.retryCount.delete(operationId)
        return result

      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error')
        
        console.warn(`Attempt ${attempt}/${maxRetries} failed for ${operationId}:`, lastError.message)
        
        // Don't retry on the last attempt
        if (attempt === maxRetries) {
          break
        }

        // Exponential backoff
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000)
        await this.sleep(delay)
      }
    }

    // Track retry count for circuit breaker
    this.retryCount.set(operationId, (this.retryCount.get(operationId) || 0) + 1)
    throw lastError!
  }

  /**
   * Create a timeout promise
   */
  private createTimeout(ms: number): Promise<never> {
    return new Promise((_, reject) => {
      setTimeout(() => reject(new Error(`Operation timed out after ${ms}ms`)), ms)
    })
  }

  /**
   * Sleep utility
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * Generate unique session ID
   */
  private generateSessionId(): string {
    return `planning-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Get service health status
   */
  getHealthStatus() {
    return {
      status: 'healthy',
      config: this.config,
      activeRetries: this.retryCount.size,
      timestamp: new Date().toISOString()
    }
  }
}
