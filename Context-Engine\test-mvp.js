#!/usr/bin/env node

/**
 * MVP Test Script for Advanced Code Context Engine
 * 
 * This script tests the basic functionality of our Context Engine MVP
 */

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Test configuration
const TEST_CONFIG = {
  neo4j: {
    uri: 'bolt://localhost:7687',
    user: 'neo4j',
    password: 'contextengine123',
    database: 'neo4j'
  },
  processing: {
    maxConcurrent: 2,
    batchSize: 5,
    supportedLanguages: ['javascript', 'typescript', 'python'],
    fileExtensions: {
      javascript: ['.js', '.jsx', '.mjs'],
      typescript: ['.ts', '.tsx'],
      python: ['.py']
    },
    maxFileSize: 1024 * 1024, // 1MB
    excludePatterns: ['node_modules/**', '.git/**', 'dist/**']
  }
};

/**
 * Create a test repository with sample files
 */
async function createTestRepository() {
  const testRepoPath = path.join(__dirname, 'test-repo');
  
  try {
    await fs.mkdir(testRepoPath, { recursive: true });
    
    // Create sample JavaScript file
    const jsContent = `
// Sample JavaScript file for testing
class UserManager {
  constructor(database) {
    this.database = database;
    this.users = new Map();
  }

  async createUser(userData) {
    const user = {
      id: this.generateId(),
      ...userData,
      createdAt: new Date()
    };
    
    this.users.set(user.id, user);
    await this.database.save('users', user);
    return user;
  }

  async findUser(id) {
    if (this.users.has(id)) {
      return this.users.get(id);
    }
    
    const user = await this.database.find('users', id);
    if (user) {
      this.users.set(id, user);
    }
    return user;
  }

  generateId() {
    return Math.random().toString(36).substr(2, 9);
  }
}

export default UserManager;
`;

    // Create sample Python file
    const pyContent = `
# Sample Python file for testing
import datetime
from typing import Dict, Optional

class UserManager:
    def __init__(self, database):
        self.database = database
        self.users: Dict[str, dict] = {}
    
    async def create_user(self, user_data: dict) -> dict:
        user = {
            'id': self.generate_id(),
            **user_data,
            'created_at': datetime.datetime.now()
        }
        
        self.users[user['id']] = user
        await self.database.save('users', user)
        return user
    
    async def find_user(self, user_id: str) -> Optional[dict]:
        if user_id in self.users:
            return self.users[user_id]
        
        user = await self.database.find('users', user_id)
        if user:
            self.users[user_id] = user
        return user
    
    def generate_id(self) -> str:
        import random
        import string
        return ''.join(random.choices(string.ascii_lowercase + string.digits, k=9))
`;

    // Write test files
    await fs.writeFile(path.join(testRepoPath, 'UserManager.js'), jsContent);
    await fs.writeFile(path.join(testRepoPath, 'user_manager.py'), pyContent);
    
    // Create package.json
    const packageJson = {
      name: 'test-repository',
      version: '1.0.0',
      description: 'Test repository for Context Engine MVP',
      main: 'UserManager.js',
      type: 'module'
    };
    
    await fs.writeFile(
      path.join(testRepoPath, 'package.json'), 
      JSON.stringify(packageJson, null, 2)
    );

    console.log(`✅ Created test repository at: ${testRepoPath}`);
    return testRepoPath;
    
  } catch (error) {
    console.error('❌ Failed to create test repository:', error.message);
    throw error;
  }
}

/**
 * Test the Context Engine components
 */
async function testContextEngine() {
  console.log('🚀 Starting Context Engine MVP Test\n');

  try {
    // Import our modules
    const { ContextEngine } = await import('./src/core/engine/ContextEngine.js');
    const { GitIngester } = await import('./src/ingestion/sources/GitIngester.js');
    const { ASTProcessor } = await import('./src/core/processors/ASTProcessor.js');
    
    console.log('✅ Successfully imported Context Engine modules');

    // Create test repository
    const testRepoPath = await createTestRepository();

    // Test 1: Initialize Context Engine
    console.log('\n📋 Test 1: Initializing Context Engine...');
    const contextEngine = new ContextEngine(TEST_CONFIG);
    
    // Note: We'll skip full initialization for this test since it requires Neo4j
    console.log('✅ Context Engine created successfully');

    // Test 2: Test Git Ingester
    console.log('\n📋 Test 2: Testing Git Ingester...');
    const gitIngester = new GitIngester(testRepoPath, TEST_CONFIG.processing);
    
    // Test file detection without Git (since test repo isn't a git repo)
    try {
      await gitIngester.initialize();
      console.log('⚠️  Git ingester requires a Git repository');
    } catch (error) {
      console.log('✅ Git ingester correctly detected non-Git directory');
    }

    // Test 3: Test AST Processor
    console.log('\n📋 Test 3: Testing AST Processor...');
    const astProcessor = new ASTProcessor(TEST_CONFIG.processing);
    await astProcessor.initialize();
    
    // Read test file and process AST
    const jsFilePath = path.join(testRepoPath, 'UserManager.js');
    const jsContent = await fs.readFile(jsFilePath, 'utf-8');
    
    const testFile = {
      path: 'UserManager.js',
      content: jsContent,
      language: 'javascript',
      size: jsContent.length
    };

    const context = await astProcessor.process({ file: testFile });
    
    if (context.ast && context.ast.nodes.length > 0) {
      console.log(`✅ AST processing successful - found ${context.ast.nodes.length} nodes`);
      console.log(`   - Functions: ${context.ast.nodes.filter(n => n.nodeType === 'function').length}`);
      console.log(`   - Classes: ${context.ast.nodes.filter(n => n.nodeType === 'class').length}`);
    } else {
      console.log('⚠️  AST processing completed but no nodes found');
    }

    // Test 4: Test Symbol Table Builder
    console.log('\n📋 Test 4: Testing Symbol Table Builder...');
    const { SymbolTableBuilder } = await import('./src/core/processors/SymbolTableBuilder.js');
    const symbolBuilder = new SymbolTableBuilder(TEST_CONFIG.processing);
    await symbolBuilder.initialize();
    
    const symbolContext = await symbolBuilder.process(context);
    
    if (symbolContext.symbols && symbolContext.symbols.symbols.length > 0) {
      console.log(`✅ Symbol table building successful - found ${symbolContext.symbols.symbols.length} symbols`);
      console.log(`   - Functions: ${symbolContext.symbols.symbols.filter(s => s.type === 'function').length}`);
      console.log(`   - Classes: ${symbolContext.symbols.symbols.filter(s => s.type === 'class').length}`);
      console.log(`   - Variables: ${symbolContext.symbols.symbols.filter(s => s.type === 'variable').length}`);
    } else {
      console.log('⚠️  Symbol table building completed but no symbols found');
    }

    // Test 5: Test Configuration
    console.log('\n📋 Test 5: Testing Configuration...');
    const { config } = await import('./src/utils/config.js');
    console.log(`✅ Configuration loaded successfully`);
    console.log(`   - Environment: ${config.env}`);
    console.log(`   - Supported languages: ${config.processing.supportedLanguages.join(', ')}`);

    // Test 6: Test Logger
    console.log('\n📋 Test 6: Testing Logger...');
    const { createLogger } = await import('./src/utils/logger.js');
    const testLogger = createLogger('MVPTest');
    testLogger.info('Test log message');
    console.log('✅ Logger working correctly');

    console.log('\n🎉 All MVP tests completed successfully!');
    console.log('\n📊 Test Summary:');
    console.log('   ✅ Module imports');
    console.log('   ✅ Context Engine creation');
    console.log('   ✅ Git Ingester validation');
    console.log('   ✅ AST Processing');
    console.log('   ✅ Symbol Table Building');
    console.log('   ✅ Configuration loading');
    console.log('   ✅ Logging system');

    console.log('\n🚀 MVP is ready for integration testing with Neo4j!');
    console.log('\nNext steps:');
    console.log('1. Start Neo4j: docker-compose up -d neo4j');
    console.log('2. Run full integration test: npm test');
    console.log('3. Start the API server: npm start');

  } catch (error) {
    console.error('\n❌ MVP test failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

/**
 * Cleanup test files
 */
async function cleanup() {
  try {
    const testRepoPath = path.join(__dirname, 'test-repo');
    await fs.rm(testRepoPath, { recursive: true, force: true });
    console.log('\n🧹 Cleaned up test files');
  } catch (error) {
    console.warn('⚠️  Cleanup warning:', error.message);
  }
}

// Run the test
if (import.meta.url === `file://${process.argv[1]}`) {
  testContextEngine()
    .then(() => cleanup())
    .catch((error) => {
      console.error('Test failed:', error);
      cleanup().finally(() => process.exit(1));
    });
}

export { testContextEngine, createTestRepository };
