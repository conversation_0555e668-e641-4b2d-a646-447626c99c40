"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Copy, Download, Edit, User, Settings, Target, Brain, Shield, Clock } from "lucide-react"
import type { ModuleProps } from "@/types/planning"

interface ContextProfile {
  profile_id: string
  identity: {
    name: string
    role: string
    organization: string
    timezone: string
    language: string
  }
  goals: {
    short_term: string[]
    long_term: string[]
  }
  preferences: {
    communication_style: string
    response_format: string
    tone: string
    visuals: boolean
    default_output_type: string
  }
  capabilities: {
    tools_enabled: string[]
    environment: {
      platform: string
      extensions: string[]
    }
  }
  memory: {
    scope: string
    persistence: string
    structure: string
    data_points: string[]
  }
  constraints: {
    rate_limit: string
    budget: {
      monthly: number
      used: number
    }
    operational_constraints: string[]
  }
  behavioral_flags: Record<string, boolean>
  metadata: {
    created_at: string
    last_updated: string
    version: string
  }
}

export function ContextPro<PERSON>leViewer({ context, onComplete }: ModuleProps) {
  const [isEditing, setIsEditing] = useState(false)
  const contextProfile = context?.results?.["context-profile"] as ContextProfile

  if (!contextProfile) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="w-5 h-5 text-red-600" />
            Context Profile Template
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600">No context profile generated yet.</p>
        </CardContent>
      </Card>
    )
  }

  const copyToClipboard = () => {
    navigator.clipboard.writeText(JSON.stringify(contextProfile, null, 2))
  }

  const downloadProfile = () => {
    const blob = new Blob([JSON.stringify(contextProfile, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${contextProfile.profile_id}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Brain className="w-5 h-5 text-red-600" />
            AI Agent Context Profile
          </CardTitle>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={copyToClipboard}>
              <Copy className="w-4 h-4 mr-2" />
              Copy JSON
            </Button>
            <Button variant="outline" size="sm" onClick={downloadProfile}>
              <Download className="w-4 h-4 mr-2" />
              Download
            </Button>
            <Button variant="outline" size="sm" onClick={() => setIsEditing(!isEditing)}>
              <Edit className="w-4 h-4 mr-2" />
              {isEditing ? "View" : "Edit"}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="identity">Identity</TabsTrigger>
            <TabsTrigger value="capabilities">Capabilities</TabsTrigger>
            <TabsTrigger value="memory">Memory</TabsTrigger>
            <TabsTrigger value="constraints">Constraints</TabsTrigger>
            <TabsTrigger value="json">JSON</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <User className="w-4 h-4" />
                    Agent Identity
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div>
                    <span className="font-medium">Name:</span> {contextProfile.identity.name}
                  </div>
                  <div>
                    <span className="font-medium">Role:</span> {contextProfile.identity.role}
                  </div>
                  <div>
                    <span className="font-medium">Organization:</span> {contextProfile.identity.organization}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Target className="w-4 h-4" />
                    Goals
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div>
                    <span className="font-medium">Short-term:</span>
                    <ul className="list-disc list-inside text-sm mt-1">
                      {contextProfile.goals.short_term.map((goal, index) => (
                        <li key={index}>{goal}</li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <span className="font-medium">Long-term:</span>
                    <ul className="list-disc list-inside text-sm mt-1">
                      {contextProfile.goals.long_term.map((goal, index) => (
                        <li key={index}>{goal}</li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Profile ID</CardTitle>
              </CardHeader>
              <CardContent>
                <Badge variant="outline" className="font-mono">
                  {contextProfile.profile_id}
                </Badge>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="identity" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Identity Configuration</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="font-medium">Name:</label>
                    <p className="text-sm text-gray-600">{contextProfile.identity.name}</p>
                  </div>
                  <div>
                    <label className="font-medium">Role:</label>
                    <p className="text-sm text-gray-600">{contextProfile.identity.role}</p>
                  </div>
                  <div>
                    <label className="font-medium">Organization:</label>
                    <p className="text-sm text-gray-600">{contextProfile.identity.organization}</p>
                  </div>
                  <div>
                    <label className="font-medium">Timezone:</label>
                    <p className="text-sm text-gray-600">{contextProfile.identity.timezone}</p>
                  </div>
                  <div>
                    <label className="font-medium">Language:</label>
                    <p className="text-sm text-gray-600">{contextProfile.identity.language}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Preferences</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="font-medium">Communication Style:</label>
                    <p className="text-sm text-gray-600">{contextProfile.preferences.communication_style}</p>
                  </div>
                  <div>
                    <label className="font-medium">Response Format:</label>
                    <p className="text-sm text-gray-600">{contextProfile.preferences.response_format}</p>
                  </div>
                  <div>
                    <label className="font-medium">Tone:</label>
                    <p className="text-sm text-gray-600">{contextProfile.preferences.tone}</p>
                  </div>
                  <div>
                    <label className="font-medium">Default Output:</label>
                    <p className="text-sm text-gray-600">{contextProfile.preferences.default_output_type}</p>
                  </div>
                  <div>
                    <label className="font-medium">Visuals:</label>
                    <Badge variant={contextProfile.preferences.visuals ? "default" : "secondary"}>
                      {contextProfile.preferences.visuals ? "Enabled" : "Disabled"}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="capabilities" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Tools & Environment</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="font-medium">Enabled Tools:</label>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {contextProfile.capabilities.tools_enabled.map((tool, index) => (
                      <Badge key={index} variant="outline">{tool}</Badge>
                    ))}
                  </div>
                </div>
                <div>
                  <label className="font-medium">Platform:</label>
                  <p className="text-sm text-gray-600">{contextProfile.capabilities.environment.platform}</p>
                </div>
                <div>
                  <label className="font-medium">Extensions:</label>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {contextProfile.capabilities.environment.extensions.map((ext, index) => (
                      <Badge key={index} variant="secondary">{ext}</Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="memory" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Memory Configuration</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="font-medium">Scope:</label>
                    <p className="text-sm text-gray-600">{contextProfile.memory.scope}</p>
                  </div>
                  <div>
                    <label className="font-medium">Persistence:</label>
                    <p className="text-sm text-gray-600">{contextProfile.memory.persistence}</p>
                  </div>
                  <div>
                    <label className="font-medium">Structure:</label>
                    <p className="text-sm text-gray-600">{contextProfile.memory.structure}</p>
                  </div>
                </div>
                <div>
                  <label className="font-medium">Data Points:</label>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {contextProfile.memory.data_points.map((point, index) => (
                      <Badge key={index} variant="outline">{point}</Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="constraints" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center gap-2">
                  <Shield className="w-4 h-4" />
                  Operational Constraints
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="font-medium">Rate Limit:</label>
                  <p className="text-sm text-gray-600">{contextProfile.constraints.rate_limit}</p>
                </div>
                <div>
                  <label className="font-medium">Budget:</label>
                  <div className="text-sm text-gray-600">
                    Monthly: ${contextProfile.constraints.budget.monthly} | 
                    Used: ${contextProfile.constraints.budget.used}
                  </div>
                </div>
                <div>
                  <label className="font-medium">Operational Constraints:</label>
                  <ul className="list-disc list-inside text-sm mt-1">
                    {contextProfile.constraints.operational_constraints.map((constraint, index) => (
                      <li key={index}>{constraint}</li>
                    ))}
                  </ul>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Behavioral Flags</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-2">
                  {Object.entries(contextProfile.behavioral_flags).map(([flag, value]) => (
                    <div key={flag} className="flex items-center justify-between">
                      <span className="text-sm">{flag.replace(/_/g, ' ')}:</span>
                      <Badge variant={value ? "default" : "secondary"}>
                        {value ? "On" : "Off"}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="json" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Raw JSON Profile</CardTitle>
              </CardHeader>
              <CardContent>
                <pre className="bg-gray-50 p-4 rounded-lg text-xs overflow-auto max-h-96">
                  {JSON.stringify(contextProfile, null, 2)}
                </pre>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="flex justify-end mt-6">
          <Button onClick={() => onComplete?.(context)}>
            Continue to Next Step
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
