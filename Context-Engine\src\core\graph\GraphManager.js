import { createLogger, logGraph } from '../../utils/logger.js';

const logger = createLogger('GraphManager');

/**
 * Graph manager for storing code context in Neo4j
 */
export class GraphManager {
  constructor(neo4jClient) {
    this.neo4jClient = neo4jClient;
    this.isInitialized = false;
  }

  /**
   * Initialize the graph manager
   */
  async initialize() {
    try {
      logger.info('Initializing graph manager');
      this.isInitialized = true;
      logger.info('Graph manager initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize graph manager', { error: error.message });
      throw error;
    }
  }

  /**
   * Store context in the graph database
   */
  async storeContext(context) {
    if (!this.isInitialized) {
      throw new Error('Graph manager not initialized');
    }

    const startTime = Date.now();
    let nodesCreated = 0;
    let relationshipsCreated = 0;

    const session = this.neo4jClient.session();

    try {
      await session.beginTransaction();

      // Create file node
      const fileNode = await this.createFileNode(context.file, session);
      nodesCreated++;

      // Process AST nodes if available
      if (context.ast && context.ast.nodes) {
        const astResults = await this.createASTNodes(context.ast, fileNode, session);
        nodesCreated += astResults.nodesCreated;
        relationshipsCreated += astResults.relationshipsCreated;
      }

      // Process symbols if available
      if (context.symbols && context.symbols.symbols) {
        const symbolResults = await this.createSymbolNodes(context.symbols, fileNode, session);
        nodesCreated += symbolResults.nodesCreated;
        relationshipsCreated += symbolResults.relationshipsCreated;
      }

      await session.commitTransaction();

      const duration = Date.now() - startTime;
      logGraph('store_context', nodesCreated, relationshipsCreated, duration);

      return {
        nodesCreated,
        relationshipsCreated,
        duration
      };

    } catch (error) {
      await session.rollbackTransaction();
      const duration = Date.now() - startTime;
      logger.error('Failed to store context in graph', {
        file: context.file.path,
        duration,
        error: error.message
      });
      throw error;
    } finally {
      await session.close();
    }
  }

  /**
   * Create file node in the graph
   */
  async createFileNode(file, session) {
    const query = `
      MERGE (f:File {path: $path})
      SET f.language = $language,
          f.size = $size,
          f.lastModified = datetime($lastModified),
          f.version = $version,
          f.updatedAt = datetime()
      RETURN f
    `;

    const parameters = {
      path: file.path,
      language: file.language,
      size: file.size,
      lastModified: file.lastModified.toISOString(),
      version: file.version
    };

    const result = await this.neo4jClient.run(query, parameters, session);
    return result.records[0].get('f');
  }

  /**
   * Create AST nodes in the graph
   */
  async createASTNodes(ast, fileNode, session) {
    let nodesCreated = 0;
    let relationshipsCreated = 0;

    for (const astNode of ast.nodes) {
      try {
        // Create the AST node
        const graphNode = await this.createCodeNode(astNode, session);
        nodesCreated++;

        // Create relationship to file
        await this.createContainsRelationship(fileNode, graphNode, session);
        relationshipsCreated++;

        // Create additional relationships based on node type
        if (astNode.nodeType === 'function') {
          const funcRels = await this.createFunctionRelationships(astNode, graphNode, session);
          relationshipsCreated += funcRels;
        } else if (astNode.nodeType === 'class') {
          const classRels = await this.createClassRelationships(astNode, graphNode, session);
          relationshipsCreated += classRels;
        }

      } catch (error) {
        logger.warn('Failed to create AST node', {
          nodeType: astNode.nodeType,
          name: astNode.name,
          error: error.message
        });
      }
    }

    return { nodesCreated, relationshipsCreated };
  }

  /**
   * Create symbol nodes in the graph
   */
  async createSymbolNodes(symbols, fileNode, session) {
    let nodesCreated = 0;
    let relationshipsCreated = 0;

    // Create symbol nodes
    for (const symbol of symbols.symbols) {
      try {
        const symbolNode = await this.createSymbolNode(symbol, session);
        nodesCreated++;

        // Create relationship to file
        await this.createDefinesRelationship(fileNode, symbolNode, session);
        relationshipsCreated++;

      } catch (error) {
        logger.warn('Failed to create symbol node', {
          symbolType: symbol.type,
          name: symbol.name,
          error: error.message
        });
      }
    }

    // Create scope relationships
    for (const scope of symbols.scopes) {
      try {
        const scopeRels = await this.createScopeRelationships(scope, symbols.symbols, session);
        relationshipsCreated += scopeRels;
      } catch (error) {
        logger.warn('Failed to create scope relationships', {
          scope: scope.name,
          error: error.message
        });
      }
    }

    // Create import/export relationships
    const importExportRels = await this.createImportExportRelationships(symbols, fileNode, session);
    relationshipsCreated += importExportRels;

    return { nodesCreated, relationshipsCreated };
  }

  /**
   * Create a code node (function, class, etc.)
   */
  async createCodeNode(astNode, session) {
    const nodeType = this.getGraphNodeType(astNode.nodeType);
    
    const query = `
      CREATE (n:${nodeType}:CodeElement {
        name: $name,
        signature: $signature,
        startLine: $startLine,
        endLine: $endLine,
        startColumn: $startColumn,
        endColumn: $endColumn,
        nodeType: $nodeType,
        createdAt: datetime()
      })
      RETURN n
    `;

    const parameters = {
      name: astNode.name || 'anonymous',
      signature: astNode.signature || '',
      startLine: astNode.startLine || 0,
      endLine: astNode.endLine || 0,
      startColumn: astNode.startColumn || 0,
      endColumn: astNode.endColumn || 0,
      nodeType: astNode.nodeType
    };

    const result = await this.neo4jClient.run(query, parameters, session);
    return result.records[0].get('n');
  }

  /**
   * Create a symbol node
   */
  async createSymbolNode(symbol, session) {
    const nodeType = this.getGraphNodeType(symbol.type);
    
    const query = `
      CREATE (s:${nodeType}:Symbol {
        id: $id,
        name: $name,
        type: $type,
        dataType: $dataType,
        scope: $scope,
        startLine: $startLine,
        endLine: $endLine,
        visibility: $visibility,
        createdAt: datetime()
      })
      RETURN s
    `;

    const parameters = {
      id: symbol.id,
      name: symbol.name,
      type: symbol.type,
      dataType: symbol.dataType || 'unknown',
      scope: symbol.scope || 'global',
      startLine: symbol.location ? symbol.location.startLine : 0,
      endLine: symbol.location ? symbol.location.endLine : 0,
      visibility: symbol.metadata ? symbol.metadata.visibility : 'public'
    };

    const result = await this.neo4jClient.run(query, parameters, session);
    return result.records[0].get('s');
  }

  /**
   * Create CONTAINS relationship between file and code element
   */
  async createContainsRelationship(fileNode, codeNode, session) {
    const query = `
      MATCH (f:File), (c:CodeElement)
      WHERE id(f) = $fileId AND id(c) = $codeId
      CREATE (f)-[:CONTAINS]->(c)
    `;

    await this.neo4jClient.run(query, {
      fileId: fileNode.identity.toNumber(),
      codeId: codeNode.identity.toNumber()
    }, session);
  }

  /**
   * Create DEFINES relationship between file and symbol
   */
  async createDefinesRelationship(fileNode, symbolNode, session) {
    const query = `
      MATCH (f:File), (s:Symbol)
      WHERE id(f) = $fileId AND id(s) = $symbolId
      CREATE (f)-[:DEFINES]->(s)
    `;

    await this.neo4jClient.run(query, {
      fileId: fileNode.identity.toNumber(),
      symbolId: symbolNode.identity.toNumber()
    }, session);
  }

  /**
   * Create function-specific relationships
   */
  async createFunctionRelationships(astNode, graphNode, session) {
    let relationshipsCreated = 0;

    // Create parameter relationships
    if (astNode.parameters) {
      for (const param of astNode.parameters) {
        try {
          const query = `
            MATCH (f:Function)
            WHERE id(f) = $functionId
            CREATE (p:Parameter {
              name: $name,
              type: $type,
              index: $index
            })
            CREATE (f)-[:HAS_PARAMETER]->(p)
          `;

          await this.neo4jClient.run(query, {
            functionId: graphNode.identity.toNumber(),
            name: param.name,
            type: param.type || 'unknown',
            index: param.index || 0
          }, session);

          relationshipsCreated++;
        } catch (error) {
          logger.warn('Failed to create parameter relationship', {
            function: astNode.name,
            parameter: param.name,
            error: error.message
          });
        }
      }
    }

    return relationshipsCreated;
  }

  /**
   * Create class-specific relationships
   */
  async createClassRelationships(astNode, graphNode, session) {
    let relationshipsCreated = 0;

    // Create inheritance relationship if superclass exists
    if (astNode.superclass) {
      try {
        const query = `
          MATCH (c:Class)
          WHERE id(c) = $classId
          MERGE (s:Class {name: $superclassName})
          CREATE (c)-[:EXTENDS]->(s)
        `;

        await this.neo4jClient.run(query, {
          classId: graphNode.identity.toNumber(),
          superclassName: astNode.superclass
        }, session);

        relationshipsCreated++;
      } catch (error) {
        logger.warn('Failed to create inheritance relationship', {
          class: astNode.name,
          superclass: astNode.superclass,
          error: error.message
        });
      }
    }

    return relationshipsCreated;
  }

  /**
   * Create scope relationships
   */
  async createScopeRelationships(scope, symbols, session) {
    let relationshipsCreated = 0;

    // Find symbols in this scope
    const scopeSymbols = symbols.filter(symbol => symbol.scope === scope.id);

    for (const symbol of scopeSymbols) {
      try {
        const query = `
          MATCH (s:Symbol {id: $symbolId})
          MERGE (scope:Scope {
            id: $scopeId,
            type: $scopeType,
            name: $scopeName,
            startLine: $startLine,
            endLine: $endLine
          })
          CREATE (scope)-[:CONTAINS_SYMBOL]->(s)
        `;

        await this.neo4jClient.run(query, {
          symbolId: symbol.id,
          scopeId: scope.id,
          scopeType: scope.type,
          scopeName: scope.name,
          startLine: scope.startLine,
          endLine: scope.endLine
        }, session);

        relationshipsCreated++;
      } catch (error) {
        logger.warn('Failed to create scope relationship', {
          scope: scope.name,
          symbol: symbol.name,
          error: error.message
        });
      }
    }

    return relationshipsCreated;
  }

  /**
   * Create import/export relationships
   */
  async createImportExportRelationships(symbols, fileNode, session) {
    let relationshipsCreated = 0;

    // Process imports
    for (const importNode of symbols.imports) {
      try {
        const query = `
          MATCH (f:File)
          WHERE id(f) = $fileId
          MERGE (dep:Dependency {source: $source})
          CREATE (f)-[:IMPORTS {
            isDefault: $isDefault,
            specifiers: $specifiers
          }]->(dep)
        `;

        await this.neo4jClient.run(query, {
          fileId: fileNode.identity.toNumber(),
          source: importNode.source,
          isDefault: importNode.isDefault,
          specifiers: JSON.stringify(importNode.specifiers)
        }, session);

        relationshipsCreated++;
      } catch (error) {
        logger.warn('Failed to create import relationship', {
          source: importNode.source,
          error: error.message
        });
      }
    }

    return relationshipsCreated;
  }

  /**
   * Get appropriate Neo4j node type
   */
  getGraphNodeType(nodeType) {
    const typeMap = {
      'function': 'Function',
      'class': 'Class',
      'variable': 'Variable',
      'parameter': 'Parameter',
      'method': 'Method',
      'property': 'Property',
      'import': 'Import',
      'export': 'Export'
    };

    return typeMap[nodeType] || 'CodeElement';
  }
}

export default GraphManager;
