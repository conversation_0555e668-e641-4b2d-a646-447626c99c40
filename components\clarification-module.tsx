"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { MessageSquare, CheckCircle } from "lucide-react"
import type { ModuleProps } from "@/types/planning"

interface Question {
  id: string
  question: string
  type: "text" | "textarea" | "radio" | "checkbox"
  options?: string[]
  required: boolean
}

export function ClarificationModule({ context, onComplete }: ModuleProps) {
  const [questions, setQuestions] = useState<Question[]>([])
  const [answers, setAnswers] = useState<Record<string, any>>({})
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)

  useEffect(() => {
    generateQuestions()
  }, [])

  const generateQuestions = () => {
    const baseQuestions: Question[] = [
      {
        id: "target_users",
        question: "Who are the primary users of this application?",
        type: "textarea",
        required: true,
      },
      {
        id: "platform",
        question: "What platforms should this support?",
        type: "radio",
        options: ["Web only", "Mobile only", "Both web and mobile", "Desktop application"],
        required: true,
      },
      {
        id: "authentication",
        question: "Do you need user authentication and accounts?",
        type: "radio",
        options: ["Yes, full user accounts", "Yes, simple login", "No authentication needed"],
        required: true,
      },
      {
        id: "data_storage",
        question: "What type of data will you be storing?",
        type: "textarea",
        required: true,
      },
      {
        id: "integrations",
        question: "Do you need any third-party integrations? (APIs, services, etc.)",
        type: "textarea",
        required: false,
      },
    ]

    // Add project-type specific questions
    if (context?.projectType === "AI Agent") {
      baseQuestions.push({
        id: "ai_capabilities",
        question: "What AI capabilities do you need?",
        type: "textarea",
        required: true,
      })
    }

    setQuestions(baseQuestions)
  }

  const handleAnswerChange = (questionId: string, value: any) => {
    setAnswers((prev) => ({ ...prev, [questionId]: value }))
  }

  const handleNext = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex((prev) => prev + 1)
    } else {
      onComplete(answers)
    }
  }

  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex((prev) => prev - 1)
    }
  }

  const currentQuestion = questions[currentQuestionIndex]
  const isLastQuestion = currentQuestionIndex === questions.length - 1
  const canProceed = !currentQuestion?.required || answers[currentQuestion.id]

  if (!currentQuestion) {
    return <div>Loading questions...</div>
  }

  const renderQuestionInput = () => {
    switch (currentQuestion.type) {
      case "text":
        return (
          <Input
            value={answers[currentQuestion.id] || ""}
            onChange={(e) => handleAnswerChange(currentQuestion.id, e.target.value)}
            className="bg-slate-700 border-slate-600 text-white"
            placeholder="Enter your answer..."
          />
        )

      case "textarea":
        return (
          <Textarea
            value={answers[currentQuestion.id] || ""}
            onChange={(e) => handleAnswerChange(currentQuestion.id, e.target.value)}
            className="bg-slate-700 border-slate-600 text-white min-h-[100px]"
            placeholder="Provide details..."
          />
        )

      case "radio":
        return (
          <RadioGroup
            value={answers[currentQuestion.id] || ""}
            onValueChange={(value) => handleAnswerChange(currentQuestion.id, value)}
          >
            {currentQuestion.options?.map((option) => (
              <div key={option} className="flex items-center space-x-2">
                <RadioGroupItem value={option} id={option} />
                <Label htmlFor={option} className="text-gray-300">
                  {option}
                </Label>
              </div>
            ))}
          </RadioGroup>
        )

      default:
        return null
    }
  }

  return (
    <Card className="border-slate-700" style={{backgroundColor: '#818181'}}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <MessageSquare className="w-5 h-5 text-purple-400" />
            <CardTitle className="text-white">Project Clarification</CardTitle>
          </div>
          <div className="text-sm text-gray-400">
            Question {currentQuestionIndex + 1} of {questions.length}
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="bg-slate-700/50 p-4 rounded-lg">
          <h3 className="text-white font-semibold mb-4">{currentQuestion.question}</h3>
          {renderQuestionInput()}
          {currentQuestion.required && <p className="text-sm text-gray-400 mt-2">* This field is required</p>}
        </div>

        <div className="flex justify-between">
          <Button
            onClick={handlePrevious}
            disabled={currentQuestionIndex === 0}
            variant="outline"
            className="border-slate-600 text-gray-300 bg-transparent"
          >
            Previous
          </Button>

          <Button onClick={handleNext} disabled={!canProceed} className="bg-purple-600 hover:bg-purple-700">
            {isLastQuestion ? (
              <>
                <CheckCircle className="w-4 h-4 mr-2" />
                Complete Clarification
              </>
            ) : (
              "Next Question"
            )}
          </Button>
        </div>

        <div className="flex space-x-1">
          {questions.map((_, index) => (
            <div
              key={index}
              className={`h-2 flex-1 rounded ${index <= currentQuestionIndex ? "bg-purple-600" : "bg-slate-600"}`}
            />
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
