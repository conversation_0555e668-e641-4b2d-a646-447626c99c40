/**
 * Intelligent Codebase Navigator
 * Provides smart navigation and exploration of large codebases
 */

import type { SemanticEmbedding, CodebaseMemory } from './agentic-rag-engine'

export interface NavigationResult {
  suggestedFiles: string[]
  navigationPath: string[]
  reasoning: string
  nextSteps: string[]
  confidence: number
}

export interface ExplorationStrategy {
  type: 'breadth-first' | 'depth-first' | 'semantic-similarity' | 'dependency-based'
  maxDepth: number
  focusAreas: string[]
  avoidPatterns: string[]
}

export interface CodebaseMap {
  modules: Map<string, ModuleInfo>
  dependencies: Map<string, string[]>
  clusters: Map<string, string[]>
  hotspots: string[]
  entryPoints: string[]
}

export interface ModuleInfo {
  path: string
  type: 'component' | 'service' | 'utility' | 'config' | 'test'
  complexity: number
  connections: number
  importance: number
  lastModified: Date
  semanticTags: string[]
}

/**
 * Intelligent Codebase Navigator
 */
export class CodebaseNavigator {
  private codebaseMap: CodebaseMap | null = null
  private explorationHistory: string[] = []
  private navigationPatterns: Map<string, any> = new Map()

  constructor() {
    this.initializeNavigationPatterns()
  }

  /**
   * Navigate codebase based on intent and context
   */
  async navigate(
    intent: string, 
    currentContext: any, 
    embeddings: Map<string, SemanticEmbedding>,
    memory: CodebaseMemory
  ): Promise<NavigationResult> {
    
    // Build codebase map if not exists
    if (!this.codebaseMap) {
      this.codebaseMap = await this.buildCodebaseMap(embeddings)
    }

    // Analyze intent and determine navigation strategy
    const strategy = this.determineNavigationStrategy(intent, currentContext)
    
    // Find relevant starting points
    const startingPoints = this.findStartingPoints(intent, currentContext, embeddings)
    
    // Perform intelligent navigation
    const navigationResult = await this.performNavigation(
      startingPoints, 
      strategy, 
      embeddings, 
      memory
    )
    
    // Update exploration history
    this.updateExplorationHistory(navigationResult.suggestedFiles)
    
    return navigationResult
  }

  /**
   * Get overview of codebase structure
   */
  async getCodebaseOverview(embeddings: Map<string, SemanticEmbedding>): Promise<{
    structure: any
    statistics: any
    recommendations: string[]
    entryPoints: string[]
  }> {
    
    if (!this.codebaseMap) {
      this.codebaseMap = await this.buildCodebaseMap(embeddings)
    }

    const structure = this.analyzeCodebaseStructure()
    const statistics = this.calculateCodebaseStatistics()
    const recommendations = this.generateNavigationRecommendations()
    
    return {
      structure,
      statistics,
      recommendations,
      entryPoints: this.codebaseMap.entryPoints
    }
  }

  /**
   * Find related code based on current location
   */
  async findRelatedCode(
    currentFile: string, 
    embeddings: Map<string, SemanticEmbedding>,
    options: {
      relationshipType?: 'dependency' | 'semantic' | 'structural' | 'all'
      maxResults?: number
      includeTests?: boolean
    } = {}
  ): Promise<{
    related: Array<{
      file: string
      relationship: string
      strength: number
      reason: string
    }>
    suggestions: string[]
  }> {
    
    const related: any[] = []
    const currentEmbedding = embeddings.get(`file:${currentFile}`)
    
    if (!currentEmbedding) {
      return { related: [], suggestions: ['File not found in embeddings'] }
    }

    // Find dependencies
    if (options.relationshipType === 'dependency' || options.relationshipType === 'all') {
      const dependencies = this.findDependencyRelated(currentFile, embeddings)
      related.push(...dependencies)
    }

    // Find semantically similar
    if (options.relationshipType === 'semantic' || options.relationshipType === 'all') {
      const semantic = this.findSemanticallyRelated(currentEmbedding, embeddings)
      related.push(...semantic)
    }

    // Find structurally related
    if (options.relationshipType === 'structural' || options.relationshipType === 'all') {
      const structural = this.findStructurallyRelated(currentFile, embeddings)
      related.push(...structural)
    }

    // Sort by strength and limit results
    const sortedRelated = related
      .sort((a, b) => b.strength - a.strength)
      .slice(0, options.maxResults || 10)

    const suggestions = this.generateRelatedCodeSuggestions(currentFile, sortedRelated)

    return { related: sortedRelated, suggestions }
  }

  /**
   * Get navigation recommendations for exploration
   */
  async getExplorationRecommendations(
    currentContext: any,
    embeddings: Map<string, SemanticEmbedding>
  ): Promise<{
    recommendations: Array<{
      action: string
      target: string
      reason: string
      priority: 'high' | 'medium' | 'low'
    }>
    learningPaths: Array<{
      name: string
      steps: string[]
      difficulty: 'beginner' | 'intermediate' | 'advanced'
    }>
  }> {
    
    const recommendations: any[] = []
    const learningPaths: any[] = []

    // Analyze current context and suggest next steps
    if (currentContext.currentFile) {
      const fileEmbedding = embeddings.get(`file:${currentContext.currentFile}`)
      if (fileEmbedding) {
        recommendations.push(...this.generateFileBasedRecommendations(fileEmbedding))
      }
    }

    // Generate learning paths
    learningPaths.push(...this.generateLearningPaths(embeddings))

    return { recommendations, learningPaths }
  }

  // Private helper methods
  private async buildCodebaseMap(embeddings: Map<string, SemanticEmbedding>): Promise<CodebaseMap> {
    const modules = new Map<string, ModuleInfo>()
    const dependencies = new Map<string, string[]>()
    const clusters = new Map<string, string[]>()
    const hotspots: string[] = []
    const entryPoints: string[] = []

    // Process all file embeddings
    for (const [id, embedding] of embeddings) {
      if (embedding.metadata.type === 'file') {
        const moduleInfo: ModuleInfo = {
          path: embedding.metadata.file,
          type: this.determineModuleType(embedding),
          complexity: embedding.metadata.complexity || 1,
          connections: embedding.metadata.dependencies?.length || 0,
          importance: this.calculateImportance(embedding),
          lastModified: new Date(),
          semanticTags: embedding.metadata.semanticTags || []
        }
        
        modules.set(embedding.metadata.file, moduleInfo)
        
        // Track dependencies
        if (embedding.metadata.dependencies) {
          dependencies.set(embedding.metadata.file, embedding.metadata.dependencies)
        }
        
        // Identify hotspots (high complexity + high connections)
        if (moduleInfo.complexity > 10 && moduleInfo.connections > 5) {
          hotspots.push(embedding.metadata.file)
        }
        
        // Identify entry points
        if (this.isEntryPoint(embedding)) {
          entryPoints.push(embedding.metadata.file)
        }
      }
    }

    // Build clusters based on semantic similarity
    this.buildSemanticClusters(embeddings, clusters)

    return { modules, dependencies, clusters, hotspots, entryPoints }
  }

  private initializeNavigationPatterns(): void {
    this.navigationPatterns.set('find-function', {
      strategy: 'semantic-similarity',
      focusAreas: ['functions', 'methods'],
      searchDepth: 3
    })

    this.navigationPatterns.set('understand-flow', {
      strategy: 'dependency-based',
      focusAreas: ['entry-points', 'main-logic'],
      searchDepth: 5
    })

    this.navigationPatterns.set('debug-issue', {
      strategy: 'breadth-first',
      focusAreas: ['error-handling', 'validation'],
      searchDepth: 4
    })
  }

  private determineNavigationStrategy(intent: string, currentContext: any): ExplorationStrategy {
    const lowerIntent = intent.toLowerCase()
    
    if (lowerIntent.includes('find') || lowerIntent.includes('search')) {
      return {
        type: 'semantic-similarity',
        maxDepth: 3,
        focusAreas: ['functions', 'classes'],
        avoidPatterns: ['test', 'spec']
      }
    } else if (lowerIntent.includes('understand') || lowerIntent.includes('flow')) {
      return {
        type: 'dependency-based',
        maxDepth: 5,
        focusAreas: ['entry-points', 'main-logic'],
        avoidPatterns: ['config', 'constants']
      }
    } else if (lowerIntent.includes('debug') || lowerIntent.includes('fix')) {
      return {
        type: 'breadth-first',
        maxDepth: 4,
        focusAreas: ['error-handling', 'validation', 'business-logic'],
        avoidPatterns: ['documentation', 'examples']
      }
    }
    
    // Default strategy
    return {
      type: 'semantic-similarity',
      maxDepth: 3,
      focusAreas: ['all'],
      avoidPatterns: []
    }
  }

  private findStartingPoints(
    intent: string, 
    currentContext: any, 
    embeddings: Map<string, SemanticEmbedding>
  ): string[] {
    const startingPoints: string[] = []
    
    // Use current file as starting point if available
    if (currentContext.currentFile) {
      startingPoints.push(currentContext.currentFile)
    }
    
    // Use recent files from context
    if (currentContext.recentFiles) {
      startingPoints.push(...currentContext.recentFiles.slice(0, 3))
    }
    
    // Use entry points if no context
    if (startingPoints.length === 0 && this.codebaseMap) {
      startingPoints.push(...this.codebaseMap.entryPoints.slice(0, 2))
    }
    
    return [...new Set(startingPoints)]
  }

  private async performNavigation(
    startingPoints: string[], 
    strategy: ExplorationStrategy, 
    embeddings: Map<string, SemanticEmbedding>,
    memory: CodebaseMemory
  ): Promise<NavigationResult> {
    
    const suggestedFiles: string[] = []
    const navigationPath: string[] = []
    let reasoning = ''
    const nextSteps: string[] = []

    // Perform navigation based on strategy
    switch (strategy.type) {
      case 'semantic-similarity':
        const semanticResults = this.performSemanticNavigation(startingPoints, strategy, embeddings)
        suggestedFiles.push(...semanticResults.files)
        reasoning = semanticResults.reasoning
        break
        
      case 'dependency-based':
        const dependencyResults = this.performDependencyNavigation(startingPoints, strategy, embeddings)
        suggestedFiles.push(...dependencyResults.files)
        reasoning = dependencyResults.reasoning
        break
        
      case 'breadth-first':
        const breadthResults = this.performBreadthFirstNavigation(startingPoints, strategy, embeddings)
        suggestedFiles.push(...breadthResults.files)
        reasoning = breadthResults.reasoning
        break
    }

    // Generate next steps
    nextSteps.push(...this.generateNextSteps(suggestedFiles, strategy))

    // Calculate confidence
    const confidence = this.calculateNavigationConfidence(suggestedFiles, strategy)

    return {
      suggestedFiles: [...new Set(suggestedFiles)].slice(0, 10),
      navigationPath,
      reasoning,
      nextSteps,
      confidence
    }
  }

  private performSemanticNavigation(startingPoints: string[], strategy: ExplorationStrategy, embeddings: Map<string, SemanticEmbedding>): any {
    const files: string[] = []
    
    // Find semantically similar files
    startingPoints.forEach(startPoint => {
      const startEmbedding = embeddings.get(`file:${startPoint}`)
      if (startEmbedding) {
        // Find similar embeddings (simplified)
        for (const [id, embedding] of embeddings) {
          if (embedding.metadata.type === 'file' && 
              embedding.metadata.file !== startPoint &&
              this.hasSemanticSimilarity(startEmbedding, embedding)) {
            files.push(embedding.metadata.file)
          }
        }
      }
    })
    
    return {
      files: files.slice(0, 8),
      reasoning: `Found semantically similar files based on ${startingPoints.length} starting points`
    }
  }

  private performDependencyNavigation(startingPoints: string[], strategy: ExplorationStrategy, embeddings: Map<string, SemanticEmbedding>): any {
    const files: string[] = []
    
    // Follow dependency chains
    startingPoints.forEach(startPoint => {
      if (this.codebaseMap?.dependencies.has(startPoint)) {
        const deps = this.codebaseMap.dependencies.get(startPoint)!
        files.push(...deps.slice(0, 3))
      }
    })
    
    return {
      files: files.slice(0, 8),
      reasoning: `Followed dependency chains from ${startingPoints.length} starting points`
    }
  }

  private performBreadthFirstNavigation(startingPoints: string[], strategy: ExplorationStrategy, embeddings: Map<string, SemanticEmbedding>): any {
    const files: string[] = []
    const visited = new Set<string>()
    const queue = [...startingPoints]
    
    while (queue.length > 0 && files.length < 8) {
      const current = queue.shift()!
      if (visited.has(current)) continue
      
      visited.add(current)
      files.push(current)
      
      // Add related files to queue
      if (this.codebaseMap?.dependencies.has(current)) {
        const deps = this.codebaseMap.dependencies.get(current)!
        queue.push(...deps.filter(dep => !visited.has(dep)))
      }
    }
    
    return {
      files: files.slice(1), // Remove starting points
      reasoning: `Performed breadth-first exploration from ${startingPoints.length} starting points`
    }
  }

  private determineModuleType(embedding: SemanticEmbedding): ModuleInfo['type'] {
    const tags = embedding.metadata.semanticTags || []
    const path = embedding.metadata.file
    
    if (tags.includes('testing') || path.includes('test') || path.includes('spec')) {
      return 'test'
    } else if (tags.includes('frontend') || path.includes('component')) {
      return 'component'
    } else if (tags.includes('api') || tags.includes('backend')) {
      return 'service'
    } else if (path.includes('config') || path.includes('settings')) {
      return 'config'
    }
    
    return 'utility'
  }

  private calculateImportance(embedding: SemanticEmbedding): number {
    let importance = 1
    
    // Higher importance for entry points
    if (this.isEntryPoint(embedding)) {
      importance += 5
    }
    
    // Higher importance for complex files
    if (embedding.metadata.complexity && embedding.metadata.complexity > 10) {
      importance += 3
    }
    
    // Higher importance for files with many dependencies
    if (embedding.metadata.dependencies && embedding.metadata.dependencies.length > 5) {
      importance += 2
    }
    
    return importance
  }

  private isEntryPoint(embedding: SemanticEmbedding): boolean {
    const path = embedding.metadata.file
    return path.includes('index') || 
           path.includes('main') || 
           path.includes('app') ||
           path.includes('server')
  }

  private buildSemanticClusters(embeddings: Map<string, SemanticEmbedding>, clusters: Map<string, string[]>): void {
    // Group files by semantic tags
    const tagGroups = new Map<string, string[]>()
    
    for (const [id, embedding] of embeddings) {
      if (embedding.metadata.type === 'file') {
        const tags = embedding.metadata.semanticTags || []
        tags.forEach(tag => {
          if (!tagGroups.has(tag)) {
            tagGroups.set(tag, [])
          }
          tagGroups.get(tag)!.push(embedding.metadata.file)
        })
      }
    }
    
    // Convert to clusters
    tagGroups.forEach((files, tag) => {
      if (files.length > 1) {
        clusters.set(tag, files)
      }
    })
  }

  private hasSemanticSimilarity(embedding1: SemanticEmbedding, embedding2: SemanticEmbedding): boolean {
    const tags1 = new Set(embedding1.metadata.semanticTags || [])
    const tags2 = new Set(embedding2.metadata.semanticTags || [])
    
    // Check for common tags
    const commonTags = [...tags1].filter(tag => tags2.has(tag))
    return commonTags.length > 0
  }

  private updateExplorationHistory(files: string[]): void {
    this.explorationHistory.push(...files)
    
    // Keep only recent history
    if (this.explorationHistory.length > 100) {
      this.explorationHistory = this.explorationHistory.slice(-50)
    }
  }

  private analyzeCodebaseStructure(): any {
    if (!this.codebaseMap) return {}
    
    return {
      totalModules: this.codebaseMap.modules.size,
      moduleTypes: this.getModuleTypeDistribution(),
      clusters: this.codebaseMap.clusters.size,
      hotspots: this.codebaseMap.hotspots.length,
      entryPoints: this.codebaseMap.entryPoints.length
    }
  }

  private calculateCodebaseStatistics(): any {
    if (!this.codebaseMap) return {}
    
    const complexities = Array.from(this.codebaseMap.modules.values()).map(m => m.complexity)
    const connections = Array.from(this.codebaseMap.modules.values()).map(m => m.connections)
    
    return {
      avgComplexity: complexities.reduce((sum, c) => sum + c, 0) / complexities.length,
      maxComplexity: Math.max(...complexities),
      avgConnections: connections.reduce((sum, c) => sum + c, 0) / connections.length,
      maxConnections: Math.max(...connections)
    }
  }

  private generateNavigationRecommendations(): string[] {
    const recommendations: string[] = []
    
    if (this.codebaseMap) {
      if (this.codebaseMap.hotspots.length > 0) {
        recommendations.push(`Review ${this.codebaseMap.hotspots.length} complexity hotspots`)
      }
      
      if (this.codebaseMap.entryPoints.length > 5) {
        recommendations.push('Consider consolidating entry points')
      }
    }
    
    return recommendations
  }

  private getModuleTypeDistribution(): Record<string, number> {
    if (!this.codebaseMap) return {}
    
    const distribution: Record<string, number> = {}
    
    this.codebaseMap.modules.forEach(module => {
      distribution[module.type] = (distribution[module.type] || 0) + 1
    })
    
    return distribution
  }

  private findDependencyRelated(currentFile: string, embeddings: Map<string, SemanticEmbedding>): any[] {
    const related: any[] = []
    
    if (this.codebaseMap?.dependencies.has(currentFile)) {
      const deps = this.codebaseMap.dependencies.get(currentFile)!
      deps.forEach(dep => {
        related.push({
          file: dep,
          relationship: 'dependency',
          strength: 0.8,
          reason: 'Direct dependency'
        })
      })
    }
    
    return related
  }

  private findSemanticallyRelated(currentEmbedding: SemanticEmbedding, embeddings: Map<string, SemanticEmbedding>): any[] {
    const related: any[] = []
    
    for (const [id, embedding] of embeddings) {
      if (embedding.metadata.type === 'file' && 
          embedding.metadata.file !== currentEmbedding.metadata.file &&
          this.hasSemanticSimilarity(currentEmbedding, embedding)) {
        related.push({
          file: embedding.metadata.file,
          relationship: 'semantic',
          strength: 0.6,
          reason: 'Similar semantic tags'
        })
      }
    }
    
    return related
  }

  private findStructurallyRelated(currentFile: string, embeddings: Map<string, SemanticEmbedding>): any[] {
    const related: any[] = []
    
    // Find files in same directory
    const currentDir = currentFile.split('/').slice(0, -1).join('/')
    
    for (const [id, embedding] of embeddings) {
      if (embedding.metadata.type === 'file') {
        const fileDir = embedding.metadata.file.split('/').slice(0, -1).join('/')
        if (fileDir === currentDir && embedding.metadata.file !== currentFile) {
          related.push({
            file: embedding.metadata.file,
            relationship: 'structural',
            strength: 0.4,
            reason: 'Same directory'
          })
        }
      }
    }
    
    return related
  }

  private generateRelatedCodeSuggestions(currentFile: string, related: any[]): string[] {
    const suggestions: string[] = []
    
    if (related.length === 0) {
      suggestions.push('No related files found - this might be an isolated component')
    } else {
      suggestions.push(`Found ${related.length} related files`)
      if (related.some(r => r.relationship === 'dependency')) {
        suggestions.push('Review dependencies for potential optimizations')
      }
    }
    
    return suggestions
  }

  private generateFileBasedRecommendations(fileEmbedding: SemanticEmbedding): any[] {
    const recommendations: any[] = []
    
    if (fileEmbedding.metadata.complexity && fileEmbedding.metadata.complexity > 10) {
      recommendations.push({
        action: 'Review complexity',
        target: fileEmbedding.metadata.file,
        reason: 'High complexity detected',
        priority: 'high' as const
      })
    }
    
    return recommendations
  }

  private generateLearningPaths(embeddings: Map<string, SemanticEmbedding>): any[] {
    return [
      {
        name: 'Architecture Overview',
        steps: ['Start with entry points', 'Follow main data flow', 'Understand key components'],
        difficulty: 'beginner' as const
      },
      {
        name: 'Deep Dive Analysis',
        steps: ['Analyze complex modules', 'Review dependency chains', 'Study design patterns'],
        difficulty: 'advanced' as const
      }
    ]
  }

  private generateNextSteps(suggestedFiles: string[], strategy: ExplorationStrategy): string[] {
    const nextSteps: string[] = []
    
    if (suggestedFiles.length > 0) {
      nextSteps.push(`Explore ${suggestedFiles[0]} for detailed analysis`)
      if (suggestedFiles.length > 1) {
        nextSteps.push(`Compare with ${suggestedFiles[1]} for patterns`)
      }
    }
    
    nextSteps.push('Use semantic search for specific functionality')
    nextSteps.push('Review related dependencies')
    
    return nextSteps
  }

  private calculateNavigationConfidence(suggestedFiles: string[], strategy: ExplorationStrategy): number {
    let confidence = 0.5
    
    if (suggestedFiles.length > 0) {
      confidence += 0.3
    }
    
    if (strategy.type === 'semantic-similarity') {
      confidence += 0.1
    }
    
    return Math.min(confidence, 1.0)
  }
}
