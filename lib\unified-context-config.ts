/**
 * Configuration for Unified Context Engine
 * Provides default configurations and environment-based setup
 */

import type { UnifiedContextConfig } from './unified-context-engine'

/**
 * Default configuration for development environment
 */
export const defaultConfig: UnifiedContextConfig = {
  neo4j: {
    uri: process.env.NEO4J_URI || 'bolt://localhost:7687',
    user: process.env.NEO4J_USER || 'neo4j',
    password: process.env.NEO4J_PASSWORD || 'password',
    database: process.env.NEO4J_DATABASE || 'neo4j'
  },
  
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD
  },
  
  processing: {
    maxConcurrent: parseInt(process.env.MAX_CONCURRENT_PROCESSES || '5'),
    batchSize: parseInt(process.env.BATCH_SIZE || '100'),
    enableRealTimeUpdates: process.env.ENABLE_REAL_TIME_UPDATES !== 'false'
  },
  
  mcp: {
    enableContext7: process.env.ENABLE_CONTEXT7 !== 'false',
    enableSequentialThinking: process.env.ENABLE_SEQUENTIAL_THINKING !== 'false',
    timeout: parseInt(process.env.MCP_TIMEOUT || '30000')
  }
}

/**
 * Production configuration with enhanced security and performance
 */
export const productionConfig: UnifiedContextConfig = {
  ...defaultConfig,
  processing: {
    ...defaultConfig.processing,
    maxConcurrent: 10,
    batchSize: 200,
    enableRealTimeUpdates: true
  },
  mcp: {
    ...defaultConfig.mcp,
    timeout: 60000
  }
}

/**
 * Test configuration with minimal dependencies
 */
export const testConfig: UnifiedContextConfig = {
  neo4j: {
    uri: 'bolt://localhost:7687',
    user: 'neo4j',
    password: 'test',
    database: 'test'
  },
  
  redis: {
    host: 'localhost',
    port: 6379
  },
  
  processing: {
    maxConcurrent: 2,
    batchSize: 10,
    enableRealTimeUpdates: false
  },
  
  mcp: {
    enableContext7: false,
    enableSequentialThinking: false,
    timeout: 5000
  }
}

/**
 * Get configuration based on environment
 */
export function getConfig(): UnifiedContextConfig {
  const env = process.env.NODE_ENV || 'development'
  
  switch (env) {
    case 'production':
      return productionConfig
    case 'test':
      return testConfig
    default:
      return defaultConfig
  }
}

/**
 * Validate configuration
 */
export function validateConfig(config: UnifiedContextConfig): { isValid: boolean; errors: string[] } {
  const errors: string[] = []
  
  // Validate Neo4j configuration
  if (!config.neo4j.uri) {
    errors.push('Neo4j URI is required')
  }
  
  if (!config.neo4j.user) {
    errors.push('Neo4j user is required')
  }
  
  if (!config.neo4j.password) {
    errors.push('Neo4j password is required')
  }
  
  // Validate Redis configuration
  if (!config.redis.host) {
    errors.push('Redis host is required')
  }
  
  if (!config.redis.port || config.redis.port < 1 || config.redis.port > 65535) {
    errors.push('Redis port must be between 1 and 65535')
  }
  
  // Validate processing configuration
  if (config.processing.maxConcurrent < 1) {
    errors.push('Max concurrent processes must be at least 1')
  }
  
  if (config.processing.batchSize < 1) {
    errors.push('Batch size must be at least 1')
  }
  
  // Validate MCP configuration
  if (config.mcp.timeout < 1000) {
    errors.push('MCP timeout must be at least 1000ms')
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Create configuration from environment variables
 */
export function createConfigFromEnv(): UnifiedContextConfig {
  return {
    neo4j: {
      uri: process.env.UNIFIED_NEO4J_URI || process.env.NEO4J_URI || 'bolt://localhost:7687',
      user: process.env.UNIFIED_NEO4J_USER || process.env.NEO4J_USER || 'neo4j',
      password: process.env.UNIFIED_NEO4J_PASSWORD || process.env.NEO4J_PASSWORD || 'password',
      database: process.env.UNIFIED_NEO4J_DATABASE || process.env.NEO4J_DATABASE || 'neo4j'
    },
    
    redis: {
      host: process.env.UNIFIED_REDIS_HOST || process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.UNIFIED_REDIS_PORT || process.env.REDIS_PORT || '6379'),
      password: process.env.UNIFIED_REDIS_PASSWORD || process.env.REDIS_PASSWORD
    },
    
    processing: {
      maxConcurrent: parseInt(process.env.UNIFIED_MAX_CONCURRENT || process.env.MAX_CONCURRENT_PROCESSES || '5'),
      batchSize: parseInt(process.env.UNIFIED_BATCH_SIZE || process.env.BATCH_SIZE || '100'),
      enableRealTimeUpdates: (process.env.UNIFIED_REAL_TIME_UPDATES || process.env.ENABLE_REAL_TIME_UPDATES || 'true') === 'true'
    },
    
    mcp: {
      enableContext7: (process.env.UNIFIED_ENABLE_CONTEXT7 || process.env.ENABLE_CONTEXT7 || 'true') === 'true',
      enableSequentialThinking: (process.env.UNIFIED_ENABLE_SEQUENTIAL_THINKING || process.env.ENABLE_SEQUENTIAL_THINKING || 'true') === 'true',
      timeout: parseInt(process.env.UNIFIED_MCP_TIMEOUT || process.env.MCP_TIMEOUT || '30000')
    }
  }
}

/**
 * Merge configurations with override support
 */
export function mergeConfigs(base: UnifiedContextConfig, override: Partial<UnifiedContextConfig>): UnifiedContextConfig {
  return {
    neo4j: { ...base.neo4j, ...override.neo4j },
    redis: { ...base.redis, ...override.redis },
    processing: { ...base.processing, ...override.processing },
    mcp: { ...base.mcp, ...override.mcp }
  }
}

/**
 * Configuration presets for common scenarios
 */
export const configPresets = {
  // Minimal setup for development
  minimal: {
    processing: {
      maxConcurrent: 1,
      batchSize: 10,
      enableRealTimeUpdates: false
    },
    mcp: {
      enableContext7: false,
      enableSequentialThinking: false,
      timeout: 5000
    }
  },
  
  // High performance setup
  performance: {
    processing: {
      maxConcurrent: 20,
      batchSize: 500,
      enableRealTimeUpdates: true
    },
    mcp: {
      enableContext7: true,
      enableSequentialThinking: true,
      timeout: 60000
    }
  },
  
  // Balanced setup for most use cases
  balanced: {
    processing: {
      maxConcurrent: 5,
      batchSize: 100,
      enableRealTimeUpdates: true
    },
    mcp: {
      enableContext7: true,
      enableSequentialThinking: true,
      timeout: 30000
    }
  }
} as const

/**
 * Apply preset to base configuration
 */
export function applyPreset(base: UnifiedContextConfig, preset: keyof typeof configPresets): UnifiedContextConfig {
  return mergeConfigs(base, configPresets[preset])
}
