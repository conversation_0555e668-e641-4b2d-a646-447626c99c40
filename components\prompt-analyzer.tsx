"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Brain, Zap } from "lucide-react"
import type { ModuleProps } from "@/types/planning"

export function PromptAnalyzer({ prompt, onComplete }: ModuleProps) {
  const [analysis, setAnalysis] = useState<any>(null)
  const [isAnalyzing, setIsAnalyzing] = useState(true)

  useEffect(() => {
    analyzePrompt()
  }, [])

  const analyzePrompt = async () => {
    setIsAnalyzing(true)

    // Simulate AI analysis
    await new Promise((resolve) => setTimeout(resolve, 2000))

    const analysisResult = {
      projectType: detectProjectType(prompt || ""),
      features: extractFeatures(prompt || ""),
      complexity: assessComplexity(prompt || ""),
      domain: detectDomain(prompt || ""),
    }

    setAnalysis(analysisResult)
    setIsAnalyzing(false)
  }

  const detectProjectType = (prompt: string): string => {
    const lowerPrompt = prompt.toLowerCase()
    if (lowerPrompt.includes("app") || lowerPrompt.includes("website")) return "Web Application"
    if (lowerPrompt.includes("bot") || lowerPrompt.includes("agent")) return "AI Agent"
    if (lowerPrompt.includes("workflow") || lowerPrompt.includes("process")) return "Workflow System"
    if (lowerPrompt.includes("api")) return "API Service"
    return "General Application"
  }

  const extractFeatures = (prompt: string): string[] => {
    const features = []
    const lowerPrompt = prompt.toLowerCase()

    if (lowerPrompt.includes("todo") || lowerPrompt.includes("task")) features.push("Task Management")
    if (lowerPrompt.includes("user") || lowerPrompt.includes("auth")) features.push("User Authentication")
    if (lowerPrompt.includes("database") || lowerPrompt.includes("data")) features.push("Data Storage")
    if (lowerPrompt.includes("chat") || lowerPrompt.includes("message")) features.push("Messaging")
    if (lowerPrompt.includes("real-time") || lowerPrompt.includes("live")) features.push("Real-time Updates")

    return features.length > 0 ? features : ["Core Functionality"]
  }

  const assessComplexity = (prompt: string): string => {
    const wordCount = prompt.split(" ").length
    if (wordCount < 10) return "Simple"
    if (wordCount < 25) return "Medium"
    return "Complex"
  }

  const detectDomain = (prompt: string): string => {
    const lowerPrompt = prompt.toLowerCase()
    if (lowerPrompt.includes("ecommerce") || lowerPrompt.includes("shop")) return "E-commerce"
    if (lowerPrompt.includes("social") || lowerPrompt.includes("network")) return "Social Media"
    if (lowerPrompt.includes("finance") || lowerPrompt.includes("payment")) return "Finance"
    if (lowerPrompt.includes("health") || lowerPrompt.includes("medical")) return "Healthcare"
    if (lowerPrompt.includes("education") || lowerPrompt.includes("learning")) return "Education"
    return "General"
  }

  const handleContinue = () => {
    onComplete(analysis)
  }

  return (
    <Card className="border-slate-700" style={{backgroundColor: '#818181'}}>
      <CardHeader>
        <div className="flex items-center gap-2">
          <Brain className="w-5 h-5 text-purple-400" />
          <CardTitle className="text-white">Prompt Analysis</CardTitle>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {isAnalyzing ? (
          <div className="flex items-center justify-center py-8">
            <div className="flex items-center gap-3">
              <Zap className="w-5 h-5 text-purple-400 animate-pulse" />
              <span className="text-gray-300">Analyzing your project requirements...</span>
            </div>
          </div>
        ) : (
          <>
            <div className="bg-slate-700/50 p-4 rounded-lg">
              <h4 className="text-white font-semibold mb-2">Original Prompt</h4>
              <p className="text-gray-300 italic">"{prompt}"</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <div>
                  <h4 className="text-white font-semibold mb-2">Project Type</h4>
                  <Badge className="bg-purple-600 text-white">{analysis.projectType}</Badge>
                </div>

                <div>
                  <h4 className="text-white font-semibold mb-2">Domain</h4>
                  <Badge variant="outline" className="border-blue-400 text-blue-300">
                    {analysis.domain}
                  </Badge>
                </div>

                <div>
                  <h4 className="text-white font-semibold mb-2">Complexity</h4>
                  <Badge
                    variant="outline"
                    className={`${
                      analysis.complexity === "Simple"
                        ? "border-green-400 text-green-300"
                        : analysis.complexity === "Medium"
                          ? "border-yellow-400 text-yellow-300"
                          : "border-red-400 text-red-300"
                    }`}
                  >
                    {analysis.complexity}
                  </Badge>
                </div>
              </div>

              <div>
                <h4 className="text-white font-semibold mb-2">Identified Features</h4>
                <div className="flex flex-wrap gap-2">
                  {analysis.features.map((feature: string, index: number) => (
                    <Badge key={index} variant="secondary" className="bg-slate-600 text-gray-200">
                      {feature}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>

            <Button onClick={handleContinue} className="w-full bg-purple-600 hover:bg-purple-700">
              Continue to Clarification
            </Button>
          </>
        )}
      </CardContent>
    </Card>
  )
}
